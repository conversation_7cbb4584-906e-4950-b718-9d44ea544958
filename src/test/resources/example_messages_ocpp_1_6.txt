BootNotification:
[2,"622351e3-a1ba-4e1a-b1cf-efa4afbf133e","BootNotification",{"chargeBoxSerialNumber": "EV.2S22P4468A58614B0671B86749F95361C44B001", "chargePointModel": "MONOBLOCK", "chargePointSerialNumber": "srn000000001", "chargePointVendor": "Schneider Electric", "firmwareVersion": "3.3.0.10", "iccid": "000001", "imsi": "0000002", "meterSerialNumber" : "sr00000001", "meterType": "type0"}]
[3,"622351e3-a1ba-4e1a-b1cf-efa4afbf133e",{"currentTime":"2025-01-26T16:44:30.7950736+01:00","interval":10,"status":"Accepted"}]
[2,"633887185","BootNotification",{"chargePointSerialNumber":"1178670","chargePointVendor":"Wall Box Chargers","meterType":"Internal NON compliant","meterSerialNumber":"","chargePointModel":"PPR1-0-2-4","iccid":"89882390000781759215","chargeBoxSerialNumber":"1178670","firmwareVersion":"6.6.12","imsi":"901288882708043"}]
[3,"633887185",{"currentTime":"2025-03-20T15:00:22.4051557+01:00","interval":10,"status":"Accepted"}]


Heartbeat:
[2,"ca4a637f-912b-4776-9e74-10010e40e959","Heartbeat",{}]
[3,"ca4a637f-912b-4776-9e74-10010e40e959",{"currentTime":"2024-08-04T16:38:34.454108+02:00"}]


Authorize
[2, "d4861f36-e4ef-424f-b115-77524f6e9784", "Authorize", {"idTag": "abcdef1234567890"}]
[3, "d4861f36-e4ef-424f-b115-77524f6e9784", {"idTagInfo": {"expiryDate": "2025-02-20T15:10:37.4607438+01:00", "parentIdTag": "abcdef1234567890", "status": "Accepted"}}]


StatusNotification
[2,"622351e3-a1ba-4e1a-b1cf-efa4afbf133e","StatusNotification",{"connectorId":1,"errorCode":"NoError","info":"errorInfo","status":"Available","timestamp":"2024-08-04T17:57:38.018059+02:00","vendorId":"vendorId","vendorErrorCode":"vendorErrorCode"}]
[3,"622351e3-a1ba-4e1a-b1cf-efa4afbf133e",{}]


ChangeAvailability
[2,"3fd30a94-2d55-4a75-b321-6f0ded365b36","ChangeAvailability",{"connectorId":1,"type":"Operative"}]
[3,"3fd30a94-2d55-4a75-b321-6f0ded365b36",{"status": "Accepted"}]


Reset
[2,"641deb80-2577-4f18-899f-afa8498fc4ce","Reset",{"type":"Hard"}]
[3,"641deb80-2577-4f18-899f-afa8498fc4ce",{"status": "Accepted"}]


StartTransaction
[2,"641deb80-2577-4f18-899f-afa8498fc4ce","StartTransaction",{"connectorId":"1", "idTag": "trnr1", "meterStart": 2, "timestamp": "2024-08-04T16:38:34.454108+02:00"}]
[3,"641deb80-2577-4f18-899f-afa8498fc4ce", {"idTagInfo": {"expiryDate": "2025-01-29T17:41:46.2780239+01:00", "parentIdTag": "trnr1", "status": "Accepted"}, "transactionId": "trnr1"}]


StopTransaction
[2,"641deb80-2577-4f18-899f-afa8498fc5ce","StopTransaction",{"idTag":"1", "meterStop": 22, "timestamp": "2024-08-04T18:38:34.454108+02:00", "transactionId": -1413733292, "reason": "Local", "transactionData": []}]
[3,"641deb80-2577-4f18-899f-afa8498fc5ce",{"idTagInfo":{"expiryDate":"2025-01-31T12:02:50.2437346+01:00","parentIdTag":"","status":"Accepted"}}]


RemoteStartTransaction
[2,"3ad9b2ca-8963-4de6-be58-4803ace60ce7","RemoteStartTransaction",{"connectorId":1,"idToken":{"idToken":"","type":"Central","additionalIdTokens":[]}}]
[3,"3ad9b2ca-8963-4de6-be58-4803ace60ce7",{"status":"Accepted"}]


RemoteStopTransaction
[2,"7749440c-609d-4441-adb6-aad2c5b42560","RemoteStopTransaction",{"transactionId":1}]
[3,"d4861f36-e4ef-424f-b115-77524f6e9784",{"status":"Accepted"}]


MeterValues
[2, "d4861f36-e4ef-424f-b115-77524f6e9784", "MeterValues", {"connectorId": 1, "meterValue": [{"timestamp": "2023-10-26T12:00:00Z", "sampledValue": [{"value": "10.5", "measurand": "Energy.Active.Import.Register", "unit": "kWh"}, {"value": "220", "measurand": "Voltage", "unit": "V", "phase": "L1"}]}]}]
[2, "1118004387", "MeterValues", {"connectorId": 0, "meterValue": [{"timestamp": "2025-04-07T13:50:09Z", "sampledValue": [{"format": "Raw", "location": "Outlet", "context": "Sample.Clock", "measurand": "Energy.Active.Import.Register", "unit": "Wh", "value": "0.0"}]}]}]
[2, "d4861f36-e4ef-424f-b115-77524f6e9784", "MeterValues", {"connectorId": 1, "transactionId": 1, "meterValue": [{"timestamp": "2023-10-26T12:00:00Z", "sampledValue": [{"value": "10.5", "context": "Sample.Periodic", "format": "Raw", "measurand": "Energy.Active.Import.Register", "phase": "L1", "location": "Cable", "unit": "kWh"}, {"value": "220", "context": "Sample.Periodic", "format": "Raw", "measurand": "Voltage", "phase": "L1", "location": "Cable", "unit": "V"}]}]}]
[3, "d4861f36-e4ef-424f-b115-77524f6e9784", {}]


ChangeConfiguration
[2, "d4861f36-e4ef-424f-b115-77524f6e9784", "ChangeConfiguration", {"key": "WebSocketPingInterval", "value": "60"}]
[3, "d4861f36-e4ef-424f-b115-77524f6e9784", {"status": "Accepted"}]


GetConfiguration
[2,"d0b3b43e-577f-4670-8718-09cb11617e9f","GetConfiguration",{"key":["key1","key2","unknownKey1","unknownKey2"]}]
[3,"d0b3b43e-577f-4670-8718-09cb11617e9f", {"configurationKey": [{"key":"key1","readonly":true,"value":"key1Value"},{"key":"key2","readonly":false,"value":"key2Value"}],"unknownKey":["unknownKey1","unknownKey2"]}]


GetDiagnostics
[2,"0e6b52bc-36cc-44fd-a348-285d382fefcb","GetDiagnostics",{"location":"*******************************"}]
[3,"0e6b52bc-36cc-44fd-a348-285d382fefcb",{"fileName":""}]


TriggerMessage
[2,"98ee7859-0fc0-4e01-a773-8d9f01701794","TriggerMessage",{"requestedMessage":"DiagnosticsStatusNotification","connectorId":1}]
[3,"98ee7859-0fc0-4e01-a773-8d9f01701794",{"status":"Accepted"}]
