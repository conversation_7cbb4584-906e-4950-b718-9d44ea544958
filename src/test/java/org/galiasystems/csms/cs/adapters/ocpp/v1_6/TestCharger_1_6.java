package org.galiasystems.csms.cs.adapters.ocpp.v1_6;

import com.fasterxml.jackson.databind.node.ArrayNode;
import jakarta.enterprise.inject.Vetoed;
import jakarta.websocket.ClientEndpoint;
import org.galiasystems.csms.cs.adapters.ocpp.OCPPVersion;
import org.galiasystems.csms.cs.adapters.ocpp.messages.MessageType;
import org.galiasystems.csms.cs.adapters.ocpp.v1_6.model.messages.Action;
import org.galiasystems.csms.cs.adapters.ocpp.v1_6.model.payloads.bootNotification.BootNotificationRequest;
import org.galiasystems.csms.cs.adapters.ocpp.TestCharger;
import org.galiasystems.csms.cs.adapters.ocpp.v1_6.model.payloads.heartbeat.HeartbeatRequest;


import java.util.UUID;

@ClientEndpoint
@Vetoed
public class TestCharger_1_6 extends TestCharger {

    public TestCharger_1_6(final long chargingStationId, final String wsUrl, final String wsPassword) {
        super(chargingStationId, wsUrl, wsPassword);
    }

    @Override
    public void connect() {
        super.connect(OCPPVersion.OCPP_1_6);
    }

    @Override
    public ArrayNode createBootNotificationRequest() {
        var bootNotificationRequest = new BootNotificationRequest("serial01", "model-0", "serial01", "charger-v1", "1.0.0", "iccid01", "imsi01", "meterSerial01", "meterType01");
        var uuid = UUID.randomUUID().toString();
        return createMessage(MessageType.CallMessage, uuid, Action.BootNotification.name(), bootNotificationRequest);
    }

    @Override
    public ArrayNode createHeartbeat() {
        return createMessage(MessageType.CallMessage, UUID.randomUUID().toString(), Action.Heartbeat.name(), new HeartbeatRequest());
    }
}