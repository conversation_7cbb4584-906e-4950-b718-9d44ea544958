package org.galiasystems.csms.cs.adapters.ocpp.v2_0_1;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import io.quarkus.test.junit.QuarkusTest;
import io.quarkus.vertx.VertxContextSupport;
import jakarta.inject.Inject;
import org.galiasystems.csms.cs.adapters.ocpp.OCPPVersion;
import org.galiasystems.csms.cs.adapters.ocpp.TestChargerFactory;
import org.galiasystems.csms.cs.adapters.ocpp.messages.MessageType;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.messages.Action;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.common.GenericDeviceModelStatusEnumType;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.common.StatusInfoType;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.getBaseReport.GetBaseReportRequest;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.getBaseReport.GetBaseReportResponse;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.heartbeat.HeartbeatResponse;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.bootNotification.BootNotificationResponse;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.bootNotification.RegistrationStatusEnumType;
import org.galiasystems.csms.management.model.ChargingStation;
import org.galiasystems.csms.management.model.enums.ChargingStationStatus;
import org.galiasystems.csms.management.repository.ChargingStationRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static io.restassured.RestAssured.given;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@QuarkusTest
public class WebSocket201Test {

    @Inject
    ChargingStationRepository chargingStationRepository;

    @Inject
    TestChargerFactory testChargerFactory;

    ObjectMapper objectMapper;

    private TestCharger_2_0_1 testCharger;

    @BeforeEach
    public void setup() throws Throwable {
        this.objectMapper = new ObjectMapper();
        this.objectMapper.registerModule(new JavaTimeModule());
        this.testCharger = (TestCharger_2_0_1) VertxContextSupport.subscribeAndAwait(() -> testChargerFactory.createTestCharger(OCPPVersion.OCPP_2_0_1, "CP00"));
    }

    @Test
    @DisplayName("Charge Point sends BootNotificationRequest and CSMS responds with BootNotificationResponse with status Accepted")
    public void clientBoots() throws Throwable {
        testCharger.connect();

        var bootNotificationRequest = testCharger.createBootNotificationRequest();
        var response = testCharger.sendRequest(bootNotificationRequest);

        assertEquals(MessageType.CallResultMessage.getMessageTypeId(), response.get(0).asInt());
        assertEquals(bootNotificationRequest.get(1), response.get(1)); // message id

        var bootNotificationResponse = objectMapper.treeToValue(response.get(2), BootNotificationResponse.class);

        assertEquals(RegistrationStatusEnumType.Accepted, bootNotificationResponse.status());
        assertEquals(ChargingStationStatus.Online, VertxContextSupport.subscribeAndAwait(() -> chargingStationRepository.getChargingStation(testCharger.getChargingStationId()).map(ChargingStation::getStatus)));

    }

    /**
     * Charge Point sends HeartbeatRequest
     * CSMS responds with HeartbeatResponse
     *
     * @throws Throwable
     */
    @Test
    @DisplayName("Charge Point sends HeartbeatRequest and CSMS responds with HeartbeatResponse")
    public void clientSendsHeartbeat() throws Throwable {
        testCharger.connect();

        var heartbeatRequest = testCharger.createHeartbeat();
        var response = testCharger.sendRequest(heartbeatRequest);
        System.out.println(response);
        assertEquals(MessageType.CallResultMessage.getMessageTypeId(), response.get(0).asInt());
        assertEquals(heartbeatRequest.get(1), response.get(1)); // message id

        var heartbeatResponse = objectMapper.treeToValue(response.get(2), HeartbeatResponse.class);
        assertNotNull(heartbeatResponse.currentTime());
    }

}