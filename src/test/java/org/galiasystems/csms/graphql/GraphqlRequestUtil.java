package org.galiasystems.csms.graphql;

import static io.restassured.RestAssured.given;
import static org.hamcrest.Matchers.is;

import java.util.List;

import org.galiasystems.csms.graphql.model.ChargingStationVariableGraphql;
import org.galiasystems.csms.management.model.enums.ChargingStationStatus;
import org.galiasystems.csms.management.model.enums.ReportStatus;
import org.galiasystems.csms.management.model.enums.ReportType;
import org.galiasystems.csms.management.types.enums.ReportResultStatus;
import org.jboss.logging.Logger;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;

import io.restassured.http.ContentType;
import io.restassured.path.json.JsonPath;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;

@ApplicationScoped
public class GraphqlRequestUtil {
	
	private static final String GRAPHQL_GET_CHARGINGSTATION_REQUEST = 
		  	"""
				{
					"query":
						"
							query ChargingStation ($id: String!) {
								chargingStation(id: $id) {
									id
									status
									reports {
										id
										requestId
										responseAdditionalInfo
										responseStatus
										type
										responseReasonCode
										status
									}
									chargingStationVariables {
										id
										supportsMonitoring
										variableName
										connectorId
										dataType
										minLimit
										evseId
										unit
										valuesList
										maxLimit
										componentInstance
										variableInstance
										componentName
										chargingStationVariableValues {
											id
											type
											value
											mutability
										}
									}
								}
							}
						",
					"variables":
						{
							"id": "%s"
						}
				}
			""";
	
	private static final String GRAPHQL_EXECUTE_REPORT_REQUEST = 
			"""
				{
					"query":
						"
							mutation ExecuteReport($chargingStationId: String!,
									$reportType: ReportType 
							) {
								executeReport(
									 chargingStationId: $chargingStationId
									 reportType: $reportType 
								) {
										id
										requestId
										type
										status
										responseStatus
										responseAdditionalInfo
										responseReasonCode
								}
							}
						",
					"variables":
						{
							"chargingStationId": "%s",
							"reportType": "%s"
						}
				}
			""";

	  	private static final String GRAPHQL_GET_REPORT_REQUEST = 
		  	"""
				{
					"query":
						"
							query Report($id: BigInteger!
							) {
								report(id: $id
								) {
										id
										requestId
										type
										status
										responseStatus
										responseAdditionalInfo
										responseReasonCode
								}
							}
						",
					"variables":
						{
							"id": %s
						}
				}
			""";
	
  	@Inject
    private Logger log;
  	
  	private ObjectMapper objectMapper;
  	
  	public GraphqlRequestUtil() {
  		this.objectMapper = new ObjectMapper();
        this.objectMapper.registerModule(new JavaTimeModule());
  	}
  	
  	public void getChargingStation(final String chargingStationId, final ChargingStationStatus status,
  			final List<ChargingStationVariableGraphql> chargingStationVariables) throws Throwable {
  		
		final String query = String.format(GRAPHQL_GET_CHARGINGSTATION_REQUEST, 
				chargingStationId);
		
		final JsonPath jsonPath = 
				JsonPath.from(this.objectMapper.writeValueAsString(chargingStationVariables));
		
		final var getChatgingStationResponse = given()
//			.port(8888)
	        .body(query)
	        .contentType(ContentType.JSON)
	        .post("/graphql/")
	        .then()
	        .contentType(ContentType.JSON)
	        .body("data.chargingStation.id", is(chargingStationId))
	        .body("data.chargingStation.status", is(status.name()))
	        .body("data.chargingStation.chargingStationVariables", is(jsonPath.getList("")))
	        .statusCode(200);
		
		if (this.log.isDebugEnabled()) {
			this.log.debug( getChatgingStationResponse.extract().asString());
		}
	}

	public int executeReport(final String chargingStationId, 
			final ReportType reportType, final ReportStatus reportStatus) {
		
		final String query = String.format(GRAPHQL_EXECUTE_REPORT_REQUEST, 
				chargingStationId, reportType.name());
		
		final var executeReportResponse = given()
//			.port(8888)
	        .body(query)
	        .contentType(ContentType.JSON)
	        .post("/graphql/")
	        .then()
	        .contentType(ContentType.JSON)
	        .body("data.executeReport.type", is(reportType.name()))
	        .body("data.executeReport.status", is(reportStatus.name()))
	        .statusCode(200);
		
		if (this.log.isDebugEnabled()) {
			this.log.debug( executeReportResponse.extract().asString());
		}
		
		return executeReportResponse.extract().jsonPath().get("data.executeReport.id");
	}
	
	public void getReport(final int reportId, final ReportType reportType, 
			final ReportStatus reportStatus, final ReportResultStatus responseStatus,
			final String responseReasonCode, final String responseAdditionalInfo) {
		
		final String query = String.format(GRAPHQL_GET_REPORT_REQUEST, reportId);
		
		final var getReportResponse =  given()
//			.port(8888)
	        .body(query)
	        .contentType(ContentType.JSON)
	        .post("/graphql/")
	        .then()
	        .contentType(ContentType.JSON)
	        .body("data.report.id", is(reportId))
	        .body("data.report.requestId", is(reportId))
	        .body("data.report.type", is(reportType == null ? null : reportType.name()))
	        .body("data.report.status", is(reportStatus == null ? null : reportStatus.name()))
	        .body("data.report.responseStatus", is(responseStatus == null ? null : responseStatus.name()))
	        .body("data.report.responseReasonCode", is(responseReasonCode))
	        .body("data.report.responseAdditionalInfo", is(responseAdditionalInfo))
	        .statusCode(200);
		
		if (this.log.isDebugEnabled()) {
			this.log.debug( getReportResponse.extract().asString());
		}
	}
}
