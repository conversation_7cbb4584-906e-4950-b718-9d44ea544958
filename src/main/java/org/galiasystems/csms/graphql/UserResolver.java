package org.galiasystems.csms.graphql;

import java.util.Collection;

import org.eclipse.microprofile.graphql.Description;
import org.eclipse.microprofile.graphql.GraphQLApi;
import org.eclipse.microprofile.graphql.Id;
import org.eclipse.microprofile.graphql.Mutation;
import org.eclipse.microprofile.graphql.Name;
import org.eclipse.microprofile.graphql.NonNull;
import org.eclipse.microprofile.graphql.Query;
import org.galiasystems.csms.graphql.model.UserFilterGraphql;
import org.galiasystems.csms.graphql.model.UserGraphql;
import org.galiasystems.csms.graphql.utils.GraphqlEntityMapper;
import org.galiasystems.csms.management.csms.UserService;
import org.galiasystems.csms.management.model.User;

import io.smallrye.mutiny.Uni;
import jakarta.inject.Inject;

@GraphQLApi
public class UserResolver {

	@Inject
	private UserService userService;
	
	@Inject
	private GraphqlEntityMapper entityMapper;
	
	@Mutation("createUser")
	@Description("Create a new User") 
	public @NonNull Uni<UserGraphql> createUser(final @Id @Name("tenantId") Long tenantId, 
			final @Name("userName") @NonNull String userName) {

		return this.userService
				.createTenantAdmin(tenantId, userName)
				.onItem()
				.transform(this.entityMapper::createUserGraphql);
	}
	
	@Mutation("updateUser")
	@Description("Update an existing User") 
	public @NonNull Uni<UserGraphql> updateUser(final @Id @Name("id") @NonNull long id, 
			final @Name("userName") @NonNull String userName) {
		final User user = new User(id, null, userName, null);
		return this.userService
				.updateUser(user)
				.onItem()
				.transform(this.entityMapper::createUserGraphql);
	}

	@Query("user")
	@Description("Get User by id")
	public Uni<UserGraphql> getUser(final @Id @Name("id") @NonNull long id) {
		
		return this.userService
        		.getUser(id)
        		.onItem()
        		.transform(this.entityMapper::createUserGraphql);
	}
	
	@Query("userByUserName") 
	@Description("Get user by the user name.") 
	public Uni<UserGraphql> getUserByUserName(final @Name("userName") @NonNull String userName) {
		
		return this.userService
        		.getUserByUserName(userName)
        		.onItem()
        		.transform(this.entityMapper::createUserGraphql);
	}
	
	@Query("users") 
	@Description("Get all Users.") 
	public @NonNull Uni<Collection<@NonNull UserGraphql>> getUsers(final @Name("filter") UserFilterGraphql filter) {
		
		return this.userService
        		.getUsers(this.entityMapper.createUserFilter(filter))
        		.onItem()
        		.transform((users) -> users.stream()
	        		.map(this.entityMapper::createUserGraphql).toList());
	}

	@Mutation("generateChargingStationPassword")
	@Description("Generate new password for a charging station")
	public @NonNull Uni<String> generateChargingStationPassword(final @Id @Name("chargingStationId") @NonNull long chargingStationId) {
		return this.userService.generateChargingStationPassword(chargingStationId);
	}
}