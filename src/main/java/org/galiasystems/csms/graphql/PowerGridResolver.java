package org.galiasystems.csms.graphql;

import java.util.Collection;

import org.eclipse.microprofile.graphql.Description;
import org.eclipse.microprofile.graphql.GraphQLApi;
import org.eclipse.microprofile.graphql.Id;
import org.eclipse.microprofile.graphql.Mutation;
import org.eclipse.microprofile.graphql.Name;
import org.eclipse.microprofile.graphql.NonNull;
import org.eclipse.microprofile.graphql.Query;
import org.galiasystems.csms.graphql.model.PowerGridGraphql;
import org.galiasystems.csms.graphql.utils.GraphqlEntityMapper;
import org.galiasystems.csms.management.Csms;
import org.galiasystems.csms.management.model.PowerGrid;

import io.smallrye.mutiny.Uni;
import jakarta.inject.Inject;

@GraphQLApi
public class PowerGridResolver {

	@Inject
	private Csms managerService;
	
	@Inject
	private GraphqlEntityMapper entityMapper;
	
	@Mutation("createPowerGrid")
	@Description("Create a new PowerGrid for the given Location with the given name") 
	public @NonNull Uni<PowerGridGraphql> createPowerGrid(final @Id @Name("locationId") @NonNull long locationId, 
			final @Name("name") @NonNull String name) {
		
		final PowerGrid powerGrid = new PowerGrid(null, name);
		return this.managerService
				.createPowerGrid(locationId, powerGrid)
				.onItem()
				.transform(this.entityMapper::createPowerGridGraphql);
	}
	
	@Query("powerGrid") 
	@Description("Get PowerGrid by id") 
	public Uni<PowerGridGraphql> getPowerGrid(final @Id @Name("id") @NonNull long id) {
		
		return this.managerService
        		.getPowerGrid(id)
        		.onItem()
        		.transform(this.entityMapper::createPowerGridGraphql);
	}
	
	@Query("powerGrids") 
	@Description("Get all PowerGrids for the Location") 
	public @NonNull Uni<Collection<@NonNull PowerGridGraphql>> getPowerGrids(final @Id @Name("locationId") @NonNull long locationId) {
		
		return this.managerService
        		.getPowerGrids(locationId)
        		.onItem()
        		.transform((powerGrids) -> powerGrids.stream()
	        		.map(this.entityMapper::createPowerGridGraphql).toList());
	}
}