package org.galiasystems.csms.graphql;

import java.util.Collection;

import org.eclipse.microprofile.graphql.Description;
import org.eclipse.microprofile.graphql.GraphQLApi;
import org.eclipse.microprofile.graphql.Id;
import org.eclipse.microprofile.graphql.Mutation;
import org.eclipse.microprofile.graphql.Name;
import org.eclipse.microprofile.graphql.NonNull;
import org.eclipse.microprofile.graphql.Query;
import org.galiasystems.csms.graphql.model.FloorGraphql;
import org.galiasystems.csms.graphql.utils.GraphqlEntityMapper;
import org.galiasystems.csms.management.Csms;
import org.galiasystems.csms.management.model.Floor;
import org.galiasystems.csms.management.model.enums.FloorLayout;

import io.smallrye.mutiny.Uni;
import jakarta.inject.Inject;

@GraphQLApi
public class FloorResolver {

	@Inject
	private Csms managerService;
	
	@Inject
	private GraphqlEntityMapper entityMapper;
	
	@Mutation("createFloor")
	@Description("Create a new Floor for the given Location with the given name") 
	public @NonNull Uni<FloorGraphql> createFloor(final @Id @Name("locationId") @NonNull long locationId, 
			final @Name("name") @NonNull String name) {
		
		final Floor floor = new Floor(null, 0, name, FloorLayout.Rectangle, 0, 0);
		return this.managerService
				.createFloor(locationId, floor)
				.onItem()
				.transform(this.entityMapper::createFloorGraphql);
	}
	
	@Query("floor") 
	@Description("Get Floor by id") 
	public Uni<FloorGraphql> getFloor(final @Id @Name("id") @NonNull long id) {
		
		return this.managerService
        		.getFloor(id)
        		.onItem()
        		.transform(this.entityMapper::createFloorGraphql);
	}
	
	@Query("floors") 
	@Description("Get all Floors for the Location") 
	public @NonNull Uni<Collection<@NonNull FloorGraphql>> getFloors(final @Id @Name("locationId") @NonNull long locationId) {
		
		return this.managerService
        		.getFloors(locationId)
        		.onItem()
        		.transform((floors) -> floors.stream()
	        		.map(this.entityMapper::createFloorGraphql).toList());
	}
}