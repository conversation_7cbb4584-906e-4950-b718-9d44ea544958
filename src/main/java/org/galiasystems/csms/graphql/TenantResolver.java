package org.galiasystems.csms.graphql;

import java.util.Collection;

import org.eclipse.microprofile.graphql.Description;
import org.eclipse.microprofile.graphql.GraphQLApi;
import org.eclipse.microprofile.graphql.Id;
import org.eclipse.microprofile.graphql.Mutation;
import org.eclipse.microprofile.graphql.Name;
import org.eclipse.microprofile.graphql.NonNull;
import org.eclipse.microprofile.graphql.Query;
import org.galiasystems.csms.graphql.model.TenantGraphql;
import org.galiasystems.csms.graphql.utils.GraphqlEntityMapper;
import org.galiasystems.csms.management.Csms;
import org.galiasystems.csms.management.model.Tenant;

import io.smallrye.mutiny.Uni;
import jakarta.inject.Inject;

@GraphQLApi
public class TenantResolver {

	@Inject
	private Csms managerService;
	
	@Inject
	private GraphqlEntityMapper entityMapper;
	
	@Mutation("createTenant")
	@Description("Create a new Tenant.") 
	public @NonNull Uni<TenantGraphql> createTenant(final @Name("name") @NonNull String name) {
		
		final Tenant tenant = new Tenant(null, name);
		return this.managerService
				.createTenant(tenant)
				.onItem()
				.transform(this.entityMapper::createTenantGraphql);
	}
	
	@Mutation("updateTenant")
	@Description("Update an existing Tenant.") 
	public @NonNull Uni<TenantGraphql> updateTenant(final @Id @Name("id") Long id, final @Name("name") @NonNull String name) {
		
		final Tenant tenant = new Tenant(id, name);
		return this.managerService
				.updateTenant(tenant)
				.onItem()
				.transform(this.entityMapper::createTenantGraphql);
	}
	
	@Query("tenant") 
	@Description("Get Tenant by id")
//	@RolesAllowed(RoleNames.ROLE_GLOBAL_ADMIN)
	public Uni<TenantGraphql> getTenant(final @Id @Name("id") @NonNull long id) {
		
		return this.managerService
        		.getTenant(id)
        		.onItem()
        		.transform(this.entityMapper::createTenantGraphql);
	}
	
	@Query("tenants") 
	@Description("Get all existing Tenants") 
//	@RolesAllowed(RoleNames.ROLE_TENANT_ADMIN)
	public @NonNull Uni<Collection<@NonNull TenantGraphql>> getTenants() {
		
		return this.managerService
        		.getTenants()
        		.onItem()
        		.transform((tenants) -> tenants.stream()
	        		.map(this.entityMapper::createTenantGraphql).toList());
	}
}