package org.galiasystems.csms.graphql.model;

import org.eclipse.microprofile.graphql.NonNull;
import org.eclipse.microprofile.graphql.Type;
import org.galiasystems.csms.management.model.enums.VariableValueType;
import org.galiasystems.csms.management.types.enums.GetVariableResultStatus;

@Type("GetVariableResult")
public record GetVariableResultGraphql(@NonNull GetVariableResultStatus resultStatus, VariableValueType variableValueType, 
		String attributeValue, String componentName, String componentInstance, Integer evseId, Integer evseConnectorId, 
		@NonNull String variableName, String variableInstance, String reasonCode, String additionalInfo) {
}