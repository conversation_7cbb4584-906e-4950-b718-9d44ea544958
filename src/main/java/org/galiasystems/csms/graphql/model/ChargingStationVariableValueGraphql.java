package org.galiasystems.csms.graphql.model;

import org.eclipse.microprofile.graphql.Id;
import org.eclipse.microprofile.graphql.NonNull;
import org.eclipse.microprofile.graphql.Type;
import org.galiasystems.csms.management.model.enums.VariableValueMutability;
import org.galiasystems.csms.management.model.enums.VariableValueType;

@Type("ChargingStationVariableValue")
public record ChargingStationVariableValueGraphql(@Id @NonNull Long id, VariableValueType type, String value, 
		VariableValueMutability mutability) {
}