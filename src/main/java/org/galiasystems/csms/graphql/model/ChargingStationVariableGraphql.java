package org.galiasystems.csms.graphql.model;

import java.math.BigDecimal;
import java.util.Collection;

import org.eclipse.microprofile.graphql.Id;
import org.eclipse.microprofile.graphql.NonNull;
import org.eclipse.microprofile.graphql.Type;
import org.galiasystems.csms.management.model.enums.DataType;

@Type("ChargingStationVariable")
public record ChargingStationVariableGraphql(@Id @NonNull Long id, @NonNull String componentName, String componentInstance, 
		Integer evseId, Integer connectorId, @NonNull String variableName, String variableInstance, String unit, DataType dataType,
		BigDecimal minLimit, BigDecimal maxLimit, String valuesList, Boolean supportsMonitoring,
		@NonNull Collection<@NonNull ChargingStationVariableValueGraphql> chargingStationVariableValues) {
}