package org.galiasystems.csms.graphql.model;

import org.eclipse.microprofile.graphql.Id;
import org.eclipse.microprofile.graphql.NonNull;
import org.eclipse.microprofile.graphql.Type;
import org.galiasystems.csms.management.model.enums.FloorLayout;

@Type("Floor")
public record FloorGraphql(@Id @NonNull long id, @NonNull int level, @NonNull String name, 
		@NonNull FloorLayout layout, @NonNull int rowCount, @NonNull int columnCount) {
}