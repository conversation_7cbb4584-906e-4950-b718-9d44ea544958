package org.galiasystems.csms.graphql.model;

import java.time.ZonedDateTime;

import org.eclipse.microprofile.graphql.Id;
import org.eclipse.microprofile.graphql.NonNull;
import org.eclipse.microprofile.graphql.Type;
import org.galiasystems.csms.cs.ChargingStationAdapterType;
import org.galiasystems.csms.management.model.enums.AvailabilityStatus;
import org.galiasystems.csms.management.model.enums.ChargingStationStatus;

@Type("ChargingStation")
public record ChargingStationGraphql(@Id @NonNull Long id, @NonNull String name, @NonNull String url, @NonNull String userName, String password,
									@NonNull ChargingStationStatus status, @NonNull AvailabilityStatus availabilityStatus,
									ZonedDateTime lastStatusUpdate, String errorCode, String errorInfo, String vendorImplementationId,
									 String vendorErrorCode, String chargePointSerialNumber, String chargePointVendor,
									 String meterType, String meterSerialNumber, String chargePointModel, String iccid,
									 String chargeBoxSerialNumber, String firmwareVersion, String imsi,
									 ChargingStationAdapterType adapterType) {
}