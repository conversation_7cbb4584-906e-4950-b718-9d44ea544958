package org.galiasystems.csms.graphql.model;

import java.time.ZonedDateTime;

import org.eclipse.microprofile.graphql.Id;
import org.eclipse.microprofile.graphql.NonNull;
import org.eclipse.microprofile.graphql.Type;
import org.galiasystems.csms.management.model.enums.AvailabilityStatus;
import org.galiasystems.csms.management.model.enums.ConnectorStatus;

@Type("Connector")
public record ConnectorGraphql(@Id @NonNull long id, @NonNull int evseConnectorId, @NonNull ConnectorStatus status, 
		@NonNull AvailabilityStatus availabilityStatus, ZonedDateTime lastStatusUpdate, String errorCode, String errorInfo, 
		String vendorImplementationId, String vendorErrorCode) {
}