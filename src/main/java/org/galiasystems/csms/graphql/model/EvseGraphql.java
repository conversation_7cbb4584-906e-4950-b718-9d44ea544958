package org.galiasystems.csms.graphql.model;

import org.eclipse.microprofile.graphql.Id;
import org.eclipse.microprofile.graphql.NonNull;
import org.eclipse.microprofile.graphql.Type;
import org.galiasystems.csms.management.model.enums.AvailabilityStatus;
import org.galiasystems.csms.management.model.enums.EvseStatus;

@Type("Evse")
public record EvseGraphql(@Id @NonNull long id, @NonNull int chargingStationEvseId, @NonNull EvseStatus status, 
		@NonNull AvailabilityStatus availabilityStatus, Integer parkingFloor, Integer parkingRow, 
		Integer parkingColumn, String parkingPlaceId) {
}