package org.galiasystems.csms.graphql.model;

import java.util.Collection;
import java.util.Date;

import org.eclipse.microprofile.graphql.Id;
import org.eclipse.microprofile.graphql.NonNull;
import org.eclipse.microprofile.graphql.Type;
import org.galiasystems.csms.management.model.enums.TransactionChargingState;
import org.galiasystems.csms.management.model.enums.TransactionEventTriggerReason;
import org.galiasystems.csms.management.model.enums.TransactionEventType;
import org.galiasystems.csms.management.model.enums.TransactionReason;

@Type("EvseTransactionEvent")
public record EvseTransactionEventGraphql(@Id @NonNull long id, @NonNull TransactionEventType eventType,
										  @NonNull Date timestamp,
										  @NonNull TransactionEventTriggerReason triggerReason,
										  @NonNull Integer eventSeqNo, Boolean offline, Integer numberOfPhasesUsed,
										  Integer cableMaxCurrent, Integer reservationId,
										  TransactionChargingState chargingState, Integer timeSpentCharging,
										  TransactionReason stoppedReason, Integer remoteStartId,
										  IdTokenGraphql idToken, Collection<@NonNull MeterValueGraphql> meterValues) {
}