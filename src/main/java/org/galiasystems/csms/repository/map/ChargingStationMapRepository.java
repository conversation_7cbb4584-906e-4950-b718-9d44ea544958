package org.galiasystems.csms.repository.map;


import io.quarkus.arc.DefaultBean;
import jakarta.enterprise.context.ApplicationScoped;

@DefaultBean
@ApplicationScoped
public class ChargingStationMapRepository {}

//implements ChargingStationRepository {
//	
//	private final Map<String, ChargingStation> chargingStations = new HashMap<>();
//	
//	@Inject
//    private Logger log;
//	
//	
//	@Override
//	public Uni<ChargingStation> createChargingStation(final String id) {
//		final ChargingStationEntity chargingStation = new ChargingStationEntity(id);
//		
//		chargingStations.put(id, chargingStation);
//		
//		log.info("Charging Station persisted: " + chargingStation);
//		
//		return Uni.createFrom().item(chargingStation);
//	}
//
//	@Override
//	public Uni<ChargingStation> getChargingStation(final String id) {
//		final ChargingStation chargingStation = chargingStations.get(id);
//		return Uni.createFrom().item(chargingStation);
//	}
//	
//	@Override
//	public Uni<ChargingStation> updateChargingStation(final String id, final Consumer<ChargingStation> callback) {
//				
//		return getChargingStation(id)
//				.onItem()
//				.invoke(callback);
//	}
//
//	@Override
//	public Uni<Collection<? extends ChargingStation>> getChargingStations() {
//		return Uni.createFrom().item(this.chargingStations.values());
//		
//	}
//}
