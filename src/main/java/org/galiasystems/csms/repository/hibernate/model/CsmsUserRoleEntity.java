package org.galiasystems.csms.repository.hibernate.model;

import org.galiasystems.csms.management.model.enums.Role;
import org.galiasystems.csms.repository.hibernate.model.common.IdBaseEntity;
import org.hibernate.annotations.JdbcType;
import org.hibernate.dialect.PostgreSQLEnumJdbcType;

import io.quarkus.security.jpa.RolesValue;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import jakarta.persistence.Version;

@Entity(name="CsmsUserRole")
@Table(name = "csms_user_role")
@SequenceGenerator(name = "defaultIdGen", sequenceName = "csms_user_role_id_seq", allocationSize = 1, initialValue = 1)
public class CsmsUserRoleEntity extends IdBaseEntity {

	private static final long serialVersionUID = 1L;
	
	private Role role;
	
	private Integer version = Integer.valueOf(0);
	
	private CsmsUserEntity user;
	
	private LocationEntity location;
	
	private PowerGridEntity powerGrid;
	
	private ChargingStationEntity chargingStation;
	
	public CsmsUserRoleEntity() {
		super();
	}
	
	public CsmsUserRoleEntity(final Long id, final Role role) {
		super(id);
		this.role = role;
	}

	@Column(nullable = false)
	@JdbcType(PostgreSQLEnumJdbcType.class)
	public Role getRole() {
		return role;
	}

	public void setRole(Role role) {
		this.role = role;
	}
	
	@RolesValue
	@Transient
	public String getRoleName() {
		return this.role.getRoleName();
	}
	
	public void setRoleName(final String roleName) {
		this.role = Role.getRole(roleName);
	}

	@Version
	@Column(nullable = false)
	public Integer getVersion() {
		return version;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name="user_id", nullable=false)
	public CsmsUserEntity getUser() {
		return user;
	}
	
	public void setUser(final CsmsUserEntity user) {
		this.user = user;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name="location_id")
	public LocationEntity getLocation() {
		return location;
	}
	
	public void setLocation(final LocationEntity location) {
		this.location = location;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name="power_grid_id")
	public PowerGridEntity getPowerGrid() {
		return powerGrid;
	}
	
	public void setPowerGrid(final PowerGridEntity powerGrid) {
		this.powerGrid = powerGrid;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name="charging_station_id")
	public ChargingStationEntity getChargingStation() {
		return chargingStation;
	}
	
	public void setChargingStation(final ChargingStationEntity chargingStation) {
		this.chargingStation = chargingStation;
	}
}
