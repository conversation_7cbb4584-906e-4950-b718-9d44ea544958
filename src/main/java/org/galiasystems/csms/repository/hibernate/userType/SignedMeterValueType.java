package org.galiasystems.csms.repository.hibernate.userType;

import java.io.Serializable;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Objects;

import org.galiasystems.csms.management.model.SignedMeterValue;
import org.hibernate.engine.spi.SharedSessionContractImplementor;
import org.hibernate.type.SqlTypes;
import org.hibernate.usertype.UserType;

import io.vertx.core.json.JsonObject;

public class SignedMeterValueType implements UserType<SignedMeterValue> {

	@Override
	public int getSqlType() {
		return SqlTypes.JSON;
	}

	@Override
	public Class<SignedMeterValue> returnedClass() {
		return SignedMeterValue.class;
	}

	@Override
	public boolean equals(final SignedMeterValue signedMeterValue1, 
			final SignedMeterValue signedMeterValue2) {		
		return Objects.equals( signedMeterValue1, signedMeterValue2 );
	}

	@Override
	public int hashCode(final SignedMeterValue signedMeterValue) {
		return Objects.hashCode(signedMeterValue);
	}

	@Override
	public SignedMeterValue nullSafeGet(final ResultSet resultSet, final int position, 
			final SharedSessionContractImplementor session, final Object owner) throws SQLException {
		final JsonObject jsonObject = resultSet.getObject(position, JsonObject.class);
		if (jsonObject == null) {
	        return null;
	    }
	    return jsonObject.mapTo(SignedMeterValue.class);
	}

	@Override
	public void nullSafeSet(final PreparedStatement preparedStatement, final SignedMeterValue signedMeterValue, 
			final int index, final SharedSessionContractImplementor session) throws SQLException {
		final JsonObject jsonObject = JsonObject.mapFrom(signedMeterValue);
		preparedStatement.setObject(index, jsonObject);
		
	}

	@Override
	public SignedMeterValue deepCopy(final SignedMeterValue signedMeterValue) {
		return signedMeterValue;
	}

	@Override
	public boolean isMutable() {
		return false;
	}

	@Override
	public Serializable disassemble(final SignedMeterValue signedMeterValue) {
		return signedMeterValue;
	}

	@Override
	public SignedMeterValue assemble(final Serializable signedMeterValue, final Object owner) {
		return (SignedMeterValue) signedMeterValue;
	}
}