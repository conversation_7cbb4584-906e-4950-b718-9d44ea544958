package org.galiasystems.csms.repository.hibernate.model;

import java.time.ZonedDateTime;
import java.util.HashSet;
import java.util.Set;

import org.galiasystems.csms.management.model.enums.AvailabilityStatus;
import org.galiasystems.csms.management.model.enums.ConnectorStatus;
import org.galiasystems.csms.repository.hibernate.model.common.IdBaseEntity;
import org.hibernate.annotations.JdbcType;
import org.hibernate.dialect.PostgreSQLEnumJdbcType;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.persistence.Version;

@Entity(name="Connector")
@Table(name = "connector")
@SequenceGenerator(name = "defaultIdGen", sequenceName = "connector_id_seq", allocationSize = 1, initialValue = 1)
public class ConnectorEntity extends IdBaseEntity {

	private static final long serialVersionUID = 1L;
	
	private int evseConnectorId;
	
	private ConnectorStatus status;
	
	private AvailabilityStatus availabilityStatus;
	
	private ZonedDateTime lastStatusUpdate;
	
	private String errorCode;
	
	private String errorInfo;
	
	private String vendorImplementationId;
	
	private String vendorErrorCode;
	
	private Integer version = Integer.valueOf(0);
	
	private EvseEntity evse;
	
	private Set<EvseTransactionEntity> transactions = new HashSet<>();
	
	public ConnectorEntity() {
		super();
	}
	
	public ConnectorEntity(final Long id) {
		super(id);
	}
	
	public ConnectorEntity(final Long id, final int evseConnectorId, final ConnectorStatus status, final AvailabilityStatus availabilityStatus,
			final ZonedDateTime lastStatusUpdate, final String errorCode, final String errorInfo, final String vendorImplementationId, 
			final String vendorErrorCode) {
		this(id);
		this.evseConnectorId = evseConnectorId;
		this.status = status;
		this.availabilityStatus = availabilityStatus;
		this.lastStatusUpdate = lastStatusUpdate;
		this.errorCode = errorCode;
		this.errorInfo = errorInfo;
		this.vendorImplementationId = vendorImplementationId;
		this.vendorErrorCode = vendorErrorCode;
	}
	
	@Column(nullable = false)
	public int getEvseConnectorId() {
		return evseConnectorId;
	}

	public void setEvseConnectorId(final int connectorId) {
		this.evseConnectorId = connectorId;
	}

	@Column(nullable = false)
	@JdbcType(PostgreSQLEnumJdbcType.class)
	public ConnectorStatus getStatus() {
		return status;
	}

	public void setStatus(final ConnectorStatus status) {
		this.status = status;
	}
	
	@Column(nullable = false)
	@JdbcType(PostgreSQLEnumJdbcType.class)
	public AvailabilityStatus getAvailabilityStatus() {
		return availabilityStatus;
	}

	public void setAvailabilityStatus(final AvailabilityStatus availabilityStatus) {
		this.availabilityStatus = availabilityStatus;
	}
	
	public ZonedDateTime getLastStatusUpdate() {
		return lastStatusUpdate;
	}

	public void setLastStatusUpdate(final ZonedDateTime lastStatusUpdate) {
		this.lastStatusUpdate = lastStatusUpdate;
	}

	public String getErrorCode() {
		return errorCode;
	}

	public void setErrorCode(final String errorCode) {
		this.errorCode = errorCode;
	}

	public String getErrorInfo() {
		return errorInfo;
	}

	public void setErrorInfo(final String errorInfo) {
		this.errorInfo = errorInfo;
	}

	public String getVendorImplementationId() {
		return vendorImplementationId;
	}

	public void setVendorImplementationId(final String vendorImplementationId) {
		this.vendorImplementationId = vendorImplementationId;
	}

	public String getVendorErrorCode() {
		return vendorErrorCode;
	}

	public void setVendorErrorCode(final String vendorErrorCode) {
		this.vendorErrorCode = vendorErrorCode;
	}

	@Version
	@Column(nullable = false)
	public Integer getVersion() {
		return version;
	}

	@ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name="evse_id", nullable=false)
	public EvseEntity getEvse() {
		return evse;
	}
	
	public void setEvse(final EvseEntity evse) {
		this.evse = evse;
	}
	
	@OneToMany(mappedBy="connector", fetch = FetchType.LAZY)
	public Set<EvseTransactionEntity> getTransactions() {
		return transactions;
	}
	
	public void setTransactions(final Set<EvseTransactionEntity> transactions) {
		this.transactions = transactions;
	}

	public void addTransaction(final EvseTransactionEntity transaction) {
		getTransactions().add(transaction);
		transaction.setConnector(this);
	}

	public void removeTransaction(final EvseTransactionEntity transaction) {
		getTransactions().remove(transaction);
		transaction.setConnector(null);
	}
}