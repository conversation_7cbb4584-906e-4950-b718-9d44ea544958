package org.galiasystems.csms.repository.hibernate.utils;

//import io.quarkus.runtime.StartupEvent;
//import io.vertx.mutiny.pgclient.PgPool;
//import org.eclipse.microprofile.config.inject.ConfigProperty;
//
//import jakarta.enterprise.context.ApplicationScoped;
//import jakarta.enterprise.event.Observes;

// @ApplicationScoped
public class DBInit {

//    private final PgPool client;
//    private final boolean schemaCreate;
//
//    public DBInit(PgPool client, @ConfigProperty(name = "myapp.schema.create", defaultValue = "true") boolean schemaCreate) {
//        this.client = client;
//        this.schemaCreate = schemaCreate;
//    }
//
//    void onStart(@Observes StartupEvent ev) {
//        if (schemaCreate) {
//            initdb();
//        }
//    }
//
//    private void initdb() {
//        // TODO
//    }
}