package org.galiasystems.csms.repository.hibernate.model;

import java.util.HashSet;
import java.util.Set;

import org.galiasystems.csms.repository.hibernate.model.common.IdBaseEntity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.persistence.Version;

@Entity(name="Location")
@Table(name = "location")
@SequenceGenerator(name = "defaultIdGen", sequenceName = "location_id_seq", allocationSize = 1, initialValue = 1)
public class LocationEntity extends IdBaseEntity {

	private static final long serialVersionUID = 1L;

	private String name;
	
	private Integer version = Integer.valueOf(0);
	
	private TenantEntity tenant;
	
	private Set<FloorEntity> floors = new HashSet<>();
	
	private Set<PowerGridEntity> powerGrids = new HashSet<>();
	
	private Set<CsmsUserRoleEntity> roles = new HashSet<>();
	
	public LocationEntity() {
		super();
	}
	
	public LocationEntity(final Long id) {
		super(id);
	}

	public LocationEntity(final Long id, final String name) {
		this(id);
		this.name = name;
	}
	
	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	@Version
	@Column(nullable = false)
	public Integer getVersion() {
		return version;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name="tenant_id", nullable=false)
	public TenantEntity getTenant() {
		return tenant;
	}
	
	public void setTenant(final TenantEntity tenant) {
		this.tenant = tenant;
	}

	@OneToMany(mappedBy="location", fetch = FetchType.LAZY)
	public Set<FloorEntity> getFloors() {
		return floors;
	}
	
	public void setFloors(final Set<FloorEntity> floors) {
		this.floors = floors;
	}

	public void addFloor(final FloorEntity floor) {
		getFloors().add(floor);
		floor.setLocation(this);
	}

	public void removeFloor(final FloorEntity floor) {
		getFloors().remove(floor);
		floor.setLocation(null);
	}
	
	@OneToMany(mappedBy="location", fetch = FetchType.LAZY)
	public Set<PowerGridEntity> getPowerGrids() {
		return powerGrids;
	}
	
	public void setPowerGrids(final Set<PowerGridEntity> powerGrids) {
		this.powerGrids = powerGrids;
	}

	public void addPowerGrid(final PowerGridEntity powerGrid) {
		getPowerGrids().add(powerGrid);
		powerGrid.setLocation(this);
	}

	public void removePowerGrid(final PowerGridEntity powerGrid) {
		getPowerGrids().remove(powerGrid);
		powerGrid.setLocation(null);
	}
	
	@OneToMany(mappedBy = "location", fetch = FetchType.LAZY)
	public Set<CsmsUserRoleEntity> getRoles() {
		return roles;
	}
	
	public void setRoles(final Set<CsmsUserRoleEntity> roles) {
		this.roles = roles;
	}
	
	public void addRole(final CsmsUserRoleEntity role) {
		getRoles().add(role);
		role.setLocation(this);
	}

	public void removeRole(final CsmsUserRoleEntity role) {
		getRoles().remove(role);
		role.setLocation(null);
	}
}