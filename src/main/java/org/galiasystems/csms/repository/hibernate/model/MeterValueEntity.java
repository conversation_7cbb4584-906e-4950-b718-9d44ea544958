package org.galiasystems.csms.repository.hibernate.model;

import jakarta.persistence.*;

import org.galiasystems.csms.management.model.SignedMeterValue;
import org.galiasystems.csms.management.model.UnitOfMeasure;
import org.galiasystems.csms.management.model.enums.*;
import org.galiasystems.csms.repository.hibernate.model.common.IdBaseEntity;
import org.galiasystems.csms.repository.hibernate.userType.SignedMeterValueType;
import org.galiasystems.csms.repository.hibernate.userType.UnitOfMeasureType;
import org.hibernate.annotations.JdbcType;
import org.hibernate.annotations.Type;
import org.hibernate.dialect.PostgreSQLEnumJdbcType;

import java.math.BigDecimal;
import java.time.ZonedDateTime;

@Entity(name = "MeterValue")
@Table(name = "meter_value")
@SequenceGenerator(name = "defaultIdGen", sequenceName = "meter_value_id_seq", allocationSize = 1, initialValue = 1)
public class MeterValueEntity extends IdBaseEntity {

    private static final long serialVersionUID = 1L;

    private ZonedDateTime timestamp;

    private BigDecimal value;

    private SampledValueReadingContext context;

    private Measurand measurand;

    private MeasuredPhase phase;

    private MeasurementLocation location;

    private SignedMeterValue signedMeterValue;
	   
    private UnitOfMeasure unitOfMeasure;
    
    private Integer version = Integer.valueOf(0);
    
    private ChargingStationEntity chargingStation;

    private Integer chargingStationEvseId;
    
//    private EvseEntity evse;

    public ZonedDateTime getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(ZonedDateTime timestamp) {
        this.timestamp = timestamp;
    }

    public BigDecimal getValue() {
        return value;
    }

    public void setValue(BigDecimal value) {
        this.value = value;
    }

    @JdbcType(PostgreSQLEnumJdbcType.class)
    public SampledValueReadingContext getContext() {
        return context;
    }

    public void setContext(SampledValueReadingContext context) {
        this.context = context;
    }

    @JdbcType(PostgreSQLEnumJdbcType.class)
    public Measurand getMeasurand() {
        return measurand;
    }

    public void setMeasurand(Measurand measurand) {
        this.measurand = measurand;
    }

    @JdbcType(PostgreSQLEnumJdbcType.class)
    public MeasuredPhase getPhase() {
        return phase;
    }

    public void setPhase(MeasuredPhase phase) {
        this.phase = phase;
    }

    @JdbcType(PostgreSQLEnumJdbcType.class)
    public MeasurementLocation getLocation() {
        return location;
    }

    public void setLocation(MeasurementLocation location) {
        this.location = location;
    }
    
    @Type(SignedMeterValueType.class)
    @Column(columnDefinition = "jsonb")
	public SignedMeterValue getSignedMeterValue() {
		return this.signedMeterValue;
	}

	public void setSignedMeterValue(final SignedMeterValue signedMeterValue) {
		this.signedMeterValue = signedMeterValue;
	}
	
	@Type(UnitOfMeasureType.class)
    @Column(columnDefinition = "jsonb")
	public UnitOfMeasure getUnitOfMeasure() {
		return this.unitOfMeasure;
	}

	public void setUnitOfMeasure(final UnitOfMeasure unitOfMeasure) {
		this.unitOfMeasure = unitOfMeasure;
	}
    
    @Version
	@Column(nullable = false)
	public Integer getVersion() {
		return version;
	}
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name="charging_station_id", nullable=false)
	public ChargingStationEntity getChargingStation() {
		return chargingStation;
	}
	
	public void setChargingStation(final ChargingStationEntity chargingStation) {
		this.chargingStation = chargingStation;
	}

	public Integer getChargingStationEvseId() {
		return chargingStationEvseId;
	}

	public void setChargingStationEvseId(final Integer chargingStationEvseId) {
		this.chargingStationEvseId = chargingStationEvseId;
	}

//	@ManyToOne(fetch = FetchType.LAZY)
//    @JoinColumn(name="evse_id", nullable=true)
//	public EvseEntity getEvse() {
//		return evse;
//	}
//	
//	public void setEvse(final EvseEntity evse) {
//		this.evse = evse;
//	}
}