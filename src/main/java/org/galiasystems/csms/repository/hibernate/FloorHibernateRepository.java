package org.galiasystems.csms.repository.hibernate;


import java.util.Collection;

import org.galiasystems.csms.management.model.Floor;
import org.galiasystems.csms.management.repository.FloorRepository;
import org.galiasystems.csms.repository.hibernate.model.FloorEntity;
import org.galiasystems.csms.repository.hibernate.model.LocationEntity;
import org.galiasystems.csms.repository.hibernate.utils.HibernateRepositoryEntityMapper;

import io.quarkus.hibernate.reactive.panache.PanacheRepository;
import io.quarkus.hibernate.reactive.panache.common.WithSession;
import io.quarkus.hibernate.reactive.panache.common.WithTransaction;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;

@ApplicationScoped
public class FloorHibernateRepository implements FloorRepository, PanacheRepository<FloorEntity>  {

	@Inject
	private HibernateRepositoryEntityMapper entityMapper;
	
	@Override
	@WithTransaction
	public Uni<Floor> createFloor(final long locationId, final Floor floor) {
		final FloorEntity floorEntity = this.entityMapper.createFloorEntity(floor);
		floorEntity.setLocation(new LocationEntity(locationId));
		return persist(floorEntity)
				.onItem()
				.transform(this.entityMapper::createFloor);
	}

	@Override
	@WithSession
	public Uni<Collection<Floor>> getFloors() {
		return listAll().onItem().transform(this.entityMapper::createFloorCollection);
	}

	@Override
	@WithSession
	public Uni<Collection<Floor>> getFloorsOf(long locationId) {
		return list("location.id", locationId)
				.onItem().transform(this.entityMapper::createFloorCollection);
	}

	@Override
	@WithSession
	public Uni<Floor> getFloor(long id) {
		return findById(id).onItem().transform(this.entityMapper::createFloor);
	}
}