package org.galiasystems.csms.repository.hibernate;

import java.util.Collection;
import java.util.function.Consumer;

import org.galiasystems.csms.management.model.Report;
import org.galiasystems.csms.management.repository.ReportRepository;
import org.galiasystems.csms.repository.hibernate.model.ChargingStationEntity;
import org.galiasystems.csms.repository.hibernate.model.ReportEntity;
import org.galiasystems.csms.repository.hibernate.utils.HibernateRepositoryEntityMapper;

import io.quarkus.hibernate.reactive.panache.PanacheRepository;
import io.quarkus.hibernate.reactive.panache.common.WithSession;
import io.quarkus.hibernate.reactive.panache.common.WithTransaction;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;

@ApplicationScoped
public class ReportHibernateRepository implements ReportRepository,
				PanacheRepository<ReportEntity> {

	@Inject
	private HibernateRepositoryEntityMapper entityMapper;
	
	@Override
	@WithTransaction
	public Uni<Report> createReport(final Long chargingStationId, final Report report) {
		
		final ReportEntity reportEntity = this.entityMapper.createReportEntity(report);
		reportEntity.setChargingStation(new ChargingStationEntity(chargingStationId));
		
		return persist(reportEntity)
				.onItem()
				.transform(this.entityMapper::createReport);
	}
	
	@Override
	@WithTransaction
	public Uni<Report> updateReport(long reportId, final Consumer<Report> reportChangeOperation) {
		final Uni<ReportEntity> reportEntityUni = getReportEntity(reportId);
		
		return updateReport(reportEntityUni, reportChangeOperation);
	}
	
	@Override
	@WithTransaction
	public Uni<Report> updateReport(final Long chargingStationId, final long requestId,
			final Consumer<Report> changeOperation) {
		
		final Uni<ReportEntity> reportEntityUni = getReportEntity(chargingStationId, requestId);
		
		return updateReport(reportEntityUni, changeOperation);
				
	}
	
	@Override
	@WithSession
	public Uni<Report> getReport(long reportId) {
		return getReportEntity(reportId)
				.onItem()
				.transform(this.entityMapper::createReport);
	}
	
	@Override
	@WithSession
	public Uni<Collection<Report>> getReports() {
		return listAll().onItem().transform(this.entityMapper::createReportCollection);
	}

	@Override
	@WithSession
	public Uni<Collection<Report>> getReports(final long chargingStationId) {
		return list("chargingStation.id", chargingStationId)
				.onItem().transform(this.entityMapper::createReportCollection);
	}
	
	private Uni<ReportEntity> getReportEntity(final long reportId) {
		return findById(reportId);
	}

	private Uni<ReportEntity> getReportEntity(final Long chargingStationId, final long requestId) {
//		return getSession()
//				.onItem()
//				.transformToUni((session) -> 
//					session.createQuery("select r from Report r where r.chargingStation.id = :chargingStationId and r.requestId = :requestId", ReportEntity.class)
//					.setParameter("chargingStationId", chargingStationId)
//					.setParameter("requestId", requestId)
//					.setPlan(session.getEntityGraph(ReportEntity.class, "graph.Report.chargingStation"))
//					.getSingleResult()
//				);
		
		
//		return find("chargingStation.id = ?1 and requestId = ?2", chargingStationId, requestId).singleResult()
//				.call((reportEntity) -> Mutiny.fetch(reportEntity.getChargingStation()));
		
		return find("chargingStation.id = ?1 and requestId = ?2", chargingStationId, requestId).singleResult();
	}
	
	private Uni<Report> updateReport(final Uni<ReportEntity> reportEntityUni, final Consumer<Report> changeOperation) {

		return reportEntityUni
				.onItem()
				.transform((reportEntity) -> {
					final Report report = this.entityMapper.createReport(reportEntity);
					changeOperation.accept(report);
					this.updateReportEntity(reportEntity, report);
					return report;
				});
	}
	
	private void updateReportEntity(final ReportEntity reportEntity,
			final Report report) {
		final Long id = report.getId();
		if (id != null) {
			reportEntity.setId(id);
		}
		reportEntity.setRequestId(report.getRequestId());
		reportEntity.setType(report.getType());
		reportEntity.setStatus(report.getStatus());
		reportEntity.setResponseStatus(report.getResponseStatus());
		reportEntity.setResponseReasonCode(report.getResponseReasonCode());
		reportEntity.setResponseAdditionalInfo(report.getResponseAdditionalInfo());
	}
}