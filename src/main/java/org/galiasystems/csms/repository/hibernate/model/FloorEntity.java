package org.galiasystems.csms.repository.hibernate.model;

import org.galiasystems.csms.management.model.enums.FloorLayout;
import org.galiasystems.csms.repository.hibernate.model.common.IdBaseEntity;
import org.hibernate.annotations.JdbcType;
import org.hibernate.dialect.PostgreSQLEnumJdbcType;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.persistence.Version;

@Entity(name="Floor")
@Table(name = "floor")
@SequenceGenerator(name = "defaultIdGen", sequenceName = "floor_id_seq", allocationSize = 1, initialValue = 1)
public class FloorEntity extends IdBaseEntity {

	private static final long serialVersionUID = 1L;
	
	private int level;
	
	private String name;
	
	private FloorLayout layout; 
	
	private int rowCount;
	
	private int columnCount;
	
	private Integer version = Integer.valueOf(0);
	
	private LocationEntity location;
	
	public FloorEntity() {
		super();
	}
	
	public FloorEntity(final Long id, final int level, final String name, final FloorLayout layout, 
			final int rowCount, final int columnCount) {
		super(id);
		this.level = level;
		this.name = name;
		this.layout = layout;
		this.rowCount = rowCount;
		this.columnCount = columnCount;
	}

	public int getLevel() {
		return level;
	}

	public void setLevel(int level) {
		this.level = level;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	@Column(nullable = false)
	@JdbcType(PostgreSQLEnumJdbcType.class)
	public FloorLayout getLayout() {
		return layout;
	}

	public void setLayout(FloorLayout layout) {
		this.layout = layout;
	}

	public int getRowCount() {
		return rowCount;
	}

	public void setRowCount(int rowCount) {
		this.rowCount = rowCount;
	}

	public int getColumnCount() {
		return columnCount;
	}

	public void setColumnCount(int columnCount) {
		this.columnCount = columnCount;
	}
	
	@Version
	@Column(nullable = false)
	public Integer getVersion() {
		return version;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name="location_id", nullable=false)
	public LocationEntity getLocation() {
		return location;
	}
	
	public void setLocation(final LocationEntity location) {
		this.location = location;
	}
}
