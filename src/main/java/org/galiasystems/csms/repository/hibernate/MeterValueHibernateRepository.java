package org.galiasystems.csms.repository.hibernate;

import io.quarkus.hibernate.reactive.panache.PanacheRepository;
import io.quarkus.hibernate.reactive.panache.common.WithSession;
import io.quarkus.hibernate.reactive.panache.common.WithTransaction;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import org.galiasystems.csms.management.model.MeterValue;
import org.galiasystems.csms.management.repository.MeterValueRepository;
import org.galiasystems.csms.repository.hibernate.model.MeterValueEntity;
import org.galiasystems.csms.repository.hibernate.utils.HibernateRepositoryEntityMapper;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

@ApplicationScoped
public class MeterValueHibernateRepository implements MeterValueRepository, PanacheRepository<MeterValueEntity> {
    @Inject
    private HibernateRepositoryEntityMapper entityMapper;

    @Override
    @WithTransaction
    public Uni<Void> createMeterValues(final long chargingStationId, final Integer chargingStationEvseId, 
			final Collection<MeterValue> meterValues) {
        List<MeterValueEntity> meterValuesToPersist = new ArrayList<>();
        meterValues.forEach(meterValue -> meterValuesToPersist.addAll(
        		this.entityMapper.createMeterValueEntities(chargingStationId, chargingStationEvseId, meterValue)));
        return persist(meterValuesToPersist);
    }

    @Override
    @WithSession
    public Uni<Collection<MeterValue>> getMeterValues(final Long chargingStationId, final Integer evseChargingStationId) {
        if (evseChargingStationId == null) {
            return find("chargingStation.id = ?1", chargingStationId)
                    .list()
                    .onItem()
                    .transform(this.entityMapper::createMeterValueCollection);
        } else {
            return find("chargingStation.id = ?1 and chargingStationEvseId = ?2", chargingStationId, evseChargingStationId)
                    .list()
                    .onItem()
                    .transform(this.entityMapper::createMeterValueCollection);
        }
    }
}