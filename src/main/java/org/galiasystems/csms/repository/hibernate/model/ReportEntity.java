package org.galiasystems.csms.repository.hibernate.model;

import org.galiasystems.csms.management.model.enums.ReportStatus;
import org.galiasystems.csms.management.model.enums.ReportType;
import org.galiasystems.csms.management.types.enums.ReportResultStatus;
import org.galiasystems.csms.repository.hibernate.model.common.IdBaseEntity;
import org.hibernate.annotations.JdbcType;
import org.hibernate.dialect.PostgreSQLEnumJdbcType;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.NamedEntityGraph;
import jakarta.persistence.NamedAttributeNode;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.persistence.Version;

@Entity(name="Report")
@Table(name = "report")
@NamedEntityGraph(
	name = "graph.Report.chargingStation", 
	attributeNodes = {
		@NamedAttributeNode("chargingStation")
	}
)
@SequenceGenerator(name = "defaultIdGen", sequenceName = "report_id_seq", allocationSize = 1, initialValue = 1)
public class ReportEntity extends IdBaseEntity {
	
	private static final long serialVersionUID = 1L;

	private Long requestId;
	
	private ReportType type;
	
	private ReportStatus status;
	
	private ReportResultStatus responseStatus;
	
	private String responseReasonCode;
	
	private String responseAdditionalInfo;
	
	private Integer version = Integer.valueOf(0);
	
	private ChargingStationEntity chargingStation;
	

	public ReportEntity() {
		super();
	}
	
	public ReportEntity(final Long id) {
		super(id);
	}
	
	public ReportEntity(final Long id, final Long requestId, final ReportType type, final ReportStatus status, 
    		final ReportResultStatus responseStatus, final String responseReasonCode, 
    		final String responseAdditionalInfo) {
		this(id);
		this.requestId = requestId;
		this.type = type;
		this.status = status;
		this.responseStatus = responseStatus;
		this.responseReasonCode = responseReasonCode;
		this.responseAdditionalInfo = responseAdditionalInfo;
	}
	
	public Long getRequestId() {
		return requestId;
	}

	public void setRequestId(final Long requestId) {
		this.requestId = requestId;
	}

	@Column(nullable = false)
	@JdbcType(PostgreSQLEnumJdbcType.class)
	public ReportType getType() {
		return type;
	}


	public void setType(final ReportType type) {
		this.type = type;
	}
	
	@Column(nullable = false)
	@JdbcType(PostgreSQLEnumJdbcType.class)
	public ReportStatus getStatus() {
		return status;
	}

	public void setStatus(final ReportStatus status) {
		this.status = status;
	}

    @JdbcType(PostgreSQLEnumJdbcType.class)
    public ReportResultStatus getResponseStatus() {
		return responseStatus;
	}

	public void setResponseStatus(final ReportResultStatus responseStatus) {
		this.responseStatus = responseStatus;
	}

	public String getResponseReasonCode() {
		return responseReasonCode;
	}

	public void setResponseReasonCode(final String responseReasonCode) {
		this.responseReasonCode = responseReasonCode;
	}

	public String getResponseAdditionalInfo() {
		return responseAdditionalInfo;
	}

	public void setResponseAdditionalInfo(final String responseAdditionalInfo) {
		this.responseAdditionalInfo = responseAdditionalInfo;
	}
	
	@Version
	@Column(nullable = false)
	public Integer getVersion() {
		return version;
	}

	@ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name="charging_station_id", nullable=false)
	public ChargingStationEntity getChargingStation() {
		return chargingStation;
	}

	public void setChargingStation(final ChargingStationEntity chargingStation) {
		this.chargingStation = chargingStation;
	}
}
