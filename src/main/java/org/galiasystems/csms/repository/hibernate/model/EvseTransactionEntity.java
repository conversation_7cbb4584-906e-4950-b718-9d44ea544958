package org.galiasystems.csms.repository.hibernate.model;

import java.util.HashSet;
import java.util.Set;

import org.galiasystems.csms.repository.hibernate.model.common.IdBaseEntity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.persistence.Version;

@Entity(name="EvseTransaction")
@Table(name = "evse_transaction")
@SequenceGenerator(name = "defaultIdGen", sequenceName = "evse_transaction_id_seq", allocationSize = 1, initialValue = 1)
public class EvseTransactionEntity extends IdBaseEntity {

	private static final long serialVersionUID = 1L;
	
	private String evseTransactionId; 
	
	private Integer version = Integer.valueOf(0);

	private EvseEntity evse;
	
	private ConnectorEntity connector;
	
	private Set<EvseTransactionEventEntity> transactionEvents = new HashSet<>();

	
	public EvseTransactionEntity() {
		super();
	}
	
	public EvseTransactionEntity(final Long id) {
		super(id);
	}
	
	public EvseTransactionEntity(final Long id, final String evseTransactionId) {
		this(id);
		this.evseTransactionId = evseTransactionId;
	}

	@Column(nullable = false)
	public String getEvseTransactionId() {
		return evseTransactionId;
	}

	public void setEvseTransactionId(String evseTransactionId) {
		this.evseTransactionId = evseTransactionId;
	}
	
	@Version
	@Column(nullable = false)
	public Integer getVersion() {
		return version;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name="evse_id", nullable=false)
	public EvseEntity getEvse() {
		return evse;
	}
	
	public void setEvse(final EvseEntity evse) {
		this.evse = evse;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "connector_id", nullable = true)
	public ConnectorEntity getConnector() {
		return connector;
	}
	
	public void setConnector(final ConnectorEntity connector) {
		this.connector = connector;
	}
	
	@OneToMany(mappedBy="transaction", fetch = FetchType.LAZY)
	public Set<EvseTransactionEventEntity> getTransactionEvents() {
		return transactionEvents;
	}
	
	public void setTransactionEvents(final Set<EvseTransactionEventEntity> transactionEvents) {
		this.transactionEvents = transactionEvents;
	}

	public void addTransactionEvent(final EvseTransactionEventEntity transactionEvent) {
		getTransactionEvents().add(transactionEvent);
		transactionEvent.setTransaction(this);
	}

	public void removeTransactionEvent(final EvseTransactionEventEntity transactionEvent) {
		getTransactionEvents().remove(transactionEvent);
		transactionEvent.setTransaction(null);
	}
}