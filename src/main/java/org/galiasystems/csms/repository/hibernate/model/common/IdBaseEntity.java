package org.galiasystems.csms.repository.hibernate.model.common;

import java.util.Objects;

import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.MappedSuperclass;

@MappedSuperclass
public abstract class IdBaseEntity extends BaseEntity {

	private static final long serialVersionUID = 1L;
	
	
	private Long id;
	
	public IdBaseEntity() {
		super();
	}
	
	public IdBaseEntity(final Long id) {
		this();
		this.id = id;
	}

	@Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "defaultIdGen")
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Override
	public final int hashCode() {
		return getEffectiveClass(this).hashCode();
	}

	@Override
	public final boolean equals(Object obj) {
		if (this == obj) {
			return true;
		}

		if (obj == null) {
			return false;
		}

		final Class<?> objEffectiveClass = getEffectiveClass(obj);

		final Class<?> thisEffectiveClass = getEffectiveClass(this);

		if (thisEffectiveClass != objEffectiveClass) {
			return false;
		}
		final IdBaseEntity other = (IdBaseEntity) obj;
		return getId() != null && Objects.equals(getId(), other.getId());
	}
}