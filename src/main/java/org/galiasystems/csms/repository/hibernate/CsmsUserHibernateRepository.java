package org.galiasystems.csms.repository.hibernate;

import java.util.Collection;
import java.util.function.BiConsumer;

import org.galiasystems.csms.management.filter.UserFilter;
import org.galiasystems.csms.management.model.User;
import org.galiasystems.csms.management.model.enums.Role;
import org.galiasystems.csms.management.repository.UserRepository;
import org.galiasystems.csms.repository.hibernate.model.CsmsUserEntity;
import org.galiasystems.csms.repository.hibernate.model.TenantEntity;
import org.galiasystems.csms.repository.hibernate.utils.HibernateRepositoryEntityMapper;

import io.quarkus.elytron.security.common.BcryptUtil;
import io.quarkus.hibernate.reactive.panache.PanacheRepository;
import io.quarkus.hibernate.reactive.panache.common.WithSession;
import io.quarkus.hibernate.reactive.panache.common.WithTransaction;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.NoResultException;

@ApplicationScoped
public class CsmsUserHibernateRepository implements UserRepository, PanacheRepository<CsmsUserEntity> {

	@Inject
	private HibernateRepositoryEntityMapper entityMapper;
	
	@Override
	@WithTransaction
	public Uni<User> createUser(final User user) {
		final Long tenantId = user.getTenantId();
		final TenantEntity tenant;
		if (tenantId == null) {
			tenant = null;
		} else {
			tenant = new TenantEntity(tenantId);
		}
		
		final CsmsUserEntity csmsUserEntity = this.entityMapper.createCsmsUserEntity(user);
		final String password = csmsUserEntity.getPassword();
		final String codedPassword;
		if (password != null) {
			codedPassword = BcryptUtil.bcryptHash(password);
		} else {
			codedPassword = null;
		}
		csmsUserEntity.setPassword(codedPassword);
		csmsUserEntity.setTenant(tenant);
		
		return persist(csmsUserEntity)
				.onItem()
				.transform((userEntity) -> {
					final User result = this.entityMapper.createUser(userEntity);
					this.entityMapper.updateUserEntity(userEntity, result);
					return result;
				})
				;
	}

	@Override
	@WithTransaction
	public Uni<User> updateUser(final User user) {
		return updateUserEntity(user, this.entityMapper::updateUserEntity);
	}

	@Override
	@WithTransaction
	public Uni<User> updateUsersPassword(final User user) {
		return updateUserEntity(user, this.entityMapper::updateUserEntitysPassword);
	}

	private Uni<User> updateUserEntity(final User user, BiConsumer<CsmsUserEntity, User> updateOperation) {
		final Long userId = user.getId();
		return findById(userId)
				.onItem()
				.ifNull()
				.failWith(new IllegalStateException("User to update not found with id: " + userId))
				.onItem()
				.invoke(userEntity -> updateOperation.accept(userEntity, user))
				.onItem()
				.transform(this.entityMapper::createUser);
	}


	@Override
	@WithSession
	public Uni<Collection<User>> getUsers(final UserFilter filter) {
		if (filter == null) {
			return listAll().onItem().transform(this.entityMapper::createUserCollection);
		}
		
		return find("""
				select u
				from CsmsUserEntity u
				where
					tenant.id = ?1 
					and exists (
							select 1
							from CsmsUserRole r
							where r.user = u
							 	  and r.role = ?2 
						
						)
				""", filter.tenantId(), filter.role())
				.list()
				.onItem()
				.transform(this.entityMapper::createUserCollection);
	}

	@Override
	@WithSession
	public Uni<User> getUser(final long id) {
		return findById(id).onItem().transform(this.entityMapper::createUser);
	}

	@Override
	@WithSession
	public Uni<User> getUserByUserName(final String userName) {		
		return find("userName = ?1", userName)
				.singleResult()
				.onFailure(NoResultException.class)
				.recoverWithNull()
				.onItem()
				.transform(this.entityMapper::createUser);
	}

	@Override
	@WithSession
	public Uni<User> getChargingStationUserByChargingStationId(final Long id) {
		final String query;
		final Object[] params;
		query = """
				select u
				from CsmsUserEntity u
				where
					 exists (
							select 1
							from CsmsUserRole r
							where r.user = u
								  and r.chargingStation.id = ?1
							 	  and r.role = ?2
								
						)
				""";
		params = new Object[]{id, Role.ChargingStation};

		return find(query, params)
				.singleResult()
				.onItem()
				.transform(this.entityMapper::createUser);

	}
}