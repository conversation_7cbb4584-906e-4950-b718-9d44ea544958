package org.galiasystems.csms.repository.hibernate.model;

import java.time.ZonedDateTime;
import java.util.HashSet;
import java.util.Set;

import jakarta.persistence.*;

import org.galiasystems.csms.cs.ChargingStationAdapterType;
import org.galiasystems.csms.management.model.enums.AvailabilityStatus;
import org.galiasystems.csms.management.model.enums.ChargingStationStatus;
import org.galiasystems.csms.repository.hibernate.model.common.IdBaseEntity;
import org.hibernate.annotations.Filter;
import org.hibernate.annotations.FilterDef;
import org.hibernate.annotations.JdbcType;
import org.hibernate.annotations.ParamDef;
import org.hibernate.dialect.PostgreSQLEnumJdbcType;

@Entity(name="ChargingStation")
@Table(name = "charging_station")
@SequenceGenerator(name = "defaultIdGen", sequenceName = "csms_charging_station_id_seq", allocationSize = 1, initialValue = 1)
@FilterDef(name="authorize", parameters={@ParamDef( name="userId", type=Long.class )})
@Filter(name = "authorize" , condition = " id in (select ur.charging_station_id from csms_user u join csms_user_role ur on u.id = ur.user_id where u.id = :userId)")
@NamedEntityGraph(
		name = ChargingStationEntity.ENTITY_GRAPH_ALL,
		attributeNodes = {
				@NamedAttributeNode("reports"),
				@NamedAttributeNode(value = "chargingStationVariables", subgraph = "chargingStationVariables-subgraph"),
		},
		subgraphs = {
				@NamedSubgraph(
						name = "chargingStationVariables-subgraph",
						attributeNodes = {
								@NamedAttributeNode("chargingStationVariableValues")
						}
				)
		}
)
@NamedEntityGraph(
		name = ChargingStationEntity.ENTITY_GRAPH_VARIABLES_AND_VALUES,
		attributeNodes = {
				@NamedAttributeNode(value = "chargingStationVariables", subgraph = "chargingStationVariables-subgraph"),
		},
		subgraphs = {
				@NamedSubgraph(
						name = "chargingStationVariables-subgraph",
						attributeNodes = {
								@NamedAttributeNode("chargingStationVariableValues")
						}
				)
		}
)
public class ChargingStationEntity extends IdBaseEntity {

	private static final long serialVersionUID = 1L;

	public static final String ENTITY_GRAPH_VARIABLES_AND_VALUES = "graph.ChargingStation.variables.and.values";
	public static final String ENTITY_GRAPH_ALL = "graph.ChargingStation.all";

	private String name;
	private String url;
	private ChargingStationStatus status;
	private AvailabilityStatus availabilityStatus;
	private ZonedDateTime lastStatusUpdate;
	private String errorCode;
	private String errorInfo;
	private String vendorImplementationId;
	private String vendorErrorCode;
	private String chargePointSerialNumber;
	private String chargePointVendor;
	private String meterType;
	private String meterSerialNumber;
	private String chargePointModel;
	private String iccid;
	private String chargeBoxSerialNumber;
	private String firmwareVersion;
	private String imsi;
	private ChargingStationAdapterType adapterType;
	private Integer version = Integer.valueOf(0);
    private PowerGridEntity powerGrid;
	private Set<ReportEntity> reports = new HashSet<>();
	private Set<ChargingStationVariableEntity> chargingStationVariables = new HashSet<>();
	private Set<CsmsUserRoleEntity> roles = new HashSet<>();
	private Set<MeterValueEntity> meterValues = new HashSet<>();
	
	

	public ChargingStationEntity() {
		super();
	}

	public ChargingStationEntity(final Long id) {
		super(id);
	}

	public ChargingStationEntity(final Long id, final String name, final String url, final ChargingStationStatus status,
								 final AvailabilityStatus availabilityStatus, final ZonedDateTime lastStatusUpdate,
								 final String errorCode, final String errorInfo, final String vendorImplementationId,
								 final String vendorErrorCode, final String chargePointSerialNumber,
								 final String chargePointVendor, final String meterType, final String meterSerialNumber,
								 final String chargePointModel, final String iccid, final String chargeBoxSerialNumber,
								 final String firmwareVersion, final String imsi, final ChargingStationAdapterType communicationProtocol) {
		super(id);
		this.name = name;
		this.url = url;
		this.status = status;
		this.availabilityStatus = availabilityStatus;
		this.lastStatusUpdate = lastStatusUpdate;
		this.errorCode = errorCode;
		this.errorInfo = errorInfo;
		this.vendorImplementationId = vendorImplementationId;
		this.vendorErrorCode = vendorErrorCode;
		this.chargePointSerialNumber = chargePointSerialNumber;
		this.chargePointVendor = chargePointVendor;
		this.meterType = meterType;
		this.meterSerialNumber = meterSerialNumber;
		this.chargePointModel = chargePointModel;
		this.iccid = iccid;
		this.chargeBoxSerialNumber = chargeBoxSerialNumber;
		this.firmwareVersion = firmwareVersion;
		this.imsi = imsi;
		this.adapterType = communicationProtocol;
	}

	public String getName() {
		return name;
	}

	public void setName(final String name) {
		this.name = name;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	@Column(nullable = false)
	@JdbcType(PostgreSQLEnumJdbcType.class)
	public ChargingStationStatus getStatus() {
		return status;
	}

	public void setStatus(final ChargingStationStatus status) {
		this.status = status;
	}

	@Column(nullable = false)
	@JdbcType(PostgreSQLEnumJdbcType.class)
	public AvailabilityStatus getAvailabilityStatus() {
		return availabilityStatus;
	}

	public void setAvailabilityStatus(final AvailabilityStatus availabilityStatus) {
		this.availabilityStatus = availabilityStatus;
	}

	public ZonedDateTime getLastStatusUpdate() {
		return lastStatusUpdate;
	}

	public void setLastStatusUpdate(final ZonedDateTime lastStatusUpdate) {
		this.lastStatusUpdate = lastStatusUpdate;
	}

	public String getErrorCode() {
		return errorCode;
	}

	public void setErrorCode(final String errorCode) {
		this.errorCode = errorCode;
	}

	public String getErrorInfo() {
		return errorInfo;
	}

	public void setErrorInfo(final String errorInfo) {
		this.errorInfo = errorInfo;
	}

	public String getVendorImplementationId() {
		return vendorImplementationId;
	}

	public void setVendorImplementationId(final String vendorImplementationId) {
		this.vendorImplementationId = vendorImplementationId;
	}

	public String getVendorErrorCode() {
		return vendorErrorCode;
	}

	public void setVendorErrorCode(final String vendorErrorCode) {
		this.vendorErrorCode = vendorErrorCode;
	}

	public String getChargePointSerialNumber() {
		return chargePointSerialNumber;
	}

	public void setChargePointSerialNumber(String chargePointSerialNumber) {
		this.chargePointSerialNumber = chargePointSerialNumber;
	}

	public String getChargePointVendor() {
		return chargePointVendor;
	}

	public void setChargePointVendor(String chargePointVendor) {
		this.chargePointVendor = chargePointVendor;
	}

	public String getMeterType() {
		return meterType;
	}

	public void setMeterType(String meterType) {
		this.meterType = meterType;
	}

	public String getMeterSerialNumber() {
		return meterSerialNumber;
	}

	public void setMeterSerialNumber(String meterSerialNumber) {
		this.meterSerialNumber = meterSerialNumber;
	}

	public String getChargePointModel() {
		return chargePointModel;
	}

	public void setChargePointModel(String chargePointModel) {
		this.chargePointModel = chargePointModel;
	}

	public String getIccid() {
		return iccid;
	}

	public void setIccid(String iccid) {
		this.iccid = iccid;
	}

	public String getChargeBoxSerialNumber() {
		return chargeBoxSerialNumber;
	}

	public void setChargeBoxSerialNumber(String chargeBoxSerialNumber) {
		this.chargeBoxSerialNumber = chargeBoxSerialNumber;
	}

	public String getFirmwareVersion() {
		return firmwareVersion;
	}

	public void setFirmwareVersion(String firmwareVersion) {
		this.firmwareVersion = firmwareVersion;
	}

	public String getImsi() {
		return imsi;
	}

	public void setImsi(String imsi) {
		this.imsi = imsi;
	}

	@Column(name = "adapter_type")
	@JdbcType(PostgreSQLEnumJdbcType.class)
	public ChargingStationAdapterType getAdapterType() {
		return adapterType;
	}

	public void setAdapterType(final ChargingStationAdapterType adapterType) {
		this.adapterType = adapterType;
	}
	
	@Version
	@Column(nullable = false)
	public Integer getVersion() {
		return version;
	}

	@ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name="power_grid_id", nullable=false)
	public PowerGridEntity getPowerGrid() {
		return powerGrid;
	}

	public void setPowerGrid(final PowerGridEntity powerGrid) {
		this.powerGrid = powerGrid;
	}

	@OneToMany(mappedBy = "chargingStation", fetch = FetchType.LAZY)
	public Set<ReportEntity> getReports() {
		return this.reports;
	}

	public void setReports(final Set<ReportEntity> reports) {
		this.reports = reports;
	}

	public void addReport(final ReportEntity report) {
		getReports().add(report);
		report.setChargingStation(this);
	}

	public void removeReport(final ReportEntity report) {
		getReports().remove(report);
		report.setChargingStation(null);
	}

	@OneToMany(mappedBy = "chargingStation", fetch = FetchType.LAZY)
	public Set<ChargingStationVariableEntity> getChargingStationVariables() {
		return chargingStationVariables;
	}

	public void setChargingStationVariables(final Set<ChargingStationVariableEntity> chargingStationVariables) {
		this.chargingStationVariables = chargingStationVariables;
	}

	public void addChargingStationVariable(final ChargingStationVariableEntity chargingStationVariable) {
		getChargingStationVariables().add(chargingStationVariable);
		chargingStationVariable.setChargingStation(this);
	}

	public void removeChargingStationVariable(final ChargingStationVariableEntity chargingStationVariable) {
		getChargingStationVariables().remove(chargingStationVariable);
		chargingStationVariable.setChargingStation(null);
	}

	@OneToMany(mappedBy = "chargingStation", fetch = FetchType.LAZY)
	public Set<CsmsUserRoleEntity> getRoles() {
		return roles;
	}

	public void setRoles(final Set<CsmsUserRoleEntity> roles) {
		this.roles = roles;
	}

	public void addRole(final CsmsUserRoleEntity role) {
		getRoles().add(role);
		role.setChargingStation(this);
	}

	public void removeRole(final CsmsUserRoleEntity role) {
		getRoles().remove(role);
		role.setChargingStation(null);
	}
	
	@OneToMany(mappedBy = "chargingStation", fetch = FetchType.LAZY)
	public Set<MeterValueEntity> getMeterValues() {
		return meterValues;
	}

	public void setMeterValues(final Set<MeterValueEntity> meterValues) {
		this.meterValues = meterValues;
	}

	public void addMeterValue(final MeterValueEntity meterValue) {
		getMeterValues().add(meterValue);
		meterValue.setChargingStation(this);
	}

	public void removeMeterValue(final MeterValueEntity meterValue) {
		getMeterValues().remove(meterValue);
		meterValue.setChargingStation(null);
	}

	@Override
	public String toString() {
		return "ChargingStationEntity[" +
				"id=" + getId() +
				", name='" + name + '\'' +
				", url='" + url + '\'' +
				", status=" + status +
				", availabilityStatus=" + availabilityStatus +
				", lastStatusUpdate=" + lastStatusUpdate +
				", errorCode='" + errorCode + '\'' +
				", errorInfo='" + errorInfo + '\'' +
				", vendorImplementationId='" + vendorImplementationId + '\'' +
				", vendorErrorCode='" + vendorErrorCode + '\'' +
				", chargePointSerialNumber='" + chargePointSerialNumber + '\'' +
				", chargePointVendor='" + chargePointVendor + '\'' +
				", meterType='" + meterType + '\'' +
				", meterSerialNumber='" + meterSerialNumber + '\'' +
				", chargePointModel='" + chargePointModel + '\'' +
				", iccid='" + iccid + '\'' +
				", chargeBoxSerialNumber='" + chargeBoxSerialNumber + '\'' +
				", firmwareVersion='" + firmwareVersion + '\'' +
				", imsi='" + imsi + '\'' +
				", adapterType=" + adapterType +
				']';
	}
}