package org.galiasystems.csms.repository.hibernate;

import java.util.Collection;

import org.galiasystems.csms.management.model.PowerGrid;
import org.galiasystems.csms.management.repository.PowerGridRepository;
import org.galiasystems.csms.repository.hibernate.model.LocationEntity;
import org.galiasystems.csms.repository.hibernate.model.PowerGridEntity;
import org.galiasystems.csms.repository.hibernate.utils.HibernateRepositoryEntityMapper;

import io.quarkus.hibernate.reactive.panache.PanacheRepository;
import io.quarkus.hibernate.reactive.panache.common.WithSession;
import io.quarkus.hibernate.reactive.panache.common.WithTransaction;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;

@ApplicationScoped
public class PowerGridHibernateRepository implements PowerGridRepository, PanacheRepository<PowerGridEntity>  {

	@Inject
	private HibernateRepositoryEntityMapper entityMapper;
	
	@Override
	@WithTransaction
	public Uni<PowerGrid> createPowerGrid(final long locationId, final PowerGrid powerGrid) {
		final PowerGridEntity powerGridEntity = this.entityMapper.createPowerGridEntity(powerGrid);
		powerGridEntity.setLocation(new LocationEntity(locationId));
		return persist(powerGridEntity)
				.onItem()
				.transform(this.entityMapper::createPowerGrid);
	}

	@Override
	@WithSession
	public Uni<Collection<PowerGrid>> getPowerGrids() {
		return listAll().onItem().transform(this.entityMapper::createPowerGridCollection);
	}

	@Override
	@WithSession
	public Uni<Collection<PowerGrid>> getPowerGridsOf(final long locationId) {
		return list("location.id", locationId)
				.onItem().transform(this.entityMapper::createPowerGridCollection);
	}

	@Override
	@WithSession
	public Uni<PowerGrid> getPowerGrid(final long id) {
		return findById(id).onItem().transform(this.entityMapper::createPowerGrid);
	}

}