package org.galiasystems.csms.repository.hibernate;

import java.time.ZonedDateTime;
import java.util.Collection;

import org.galiasystems.csms.management.model.Connector;
import org.galiasystems.csms.management.model.enums.ConnectorStatus;
import org.galiasystems.csms.management.repository.ConnectorRepository;
import org.galiasystems.csms.repository.hibernate.model.ConnectorEntity;
import org.galiasystems.csms.repository.hibernate.model.EvseEntity;
import org.galiasystems.csms.repository.hibernate.utils.HibernateRepositoryEntityMapper;

import io.quarkus.hibernate.reactive.panache.PanacheRepository;
import io.quarkus.hibernate.reactive.panache.common.WithSession;
import io.quarkus.hibernate.reactive.panache.common.WithTransaction;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.NoResultException;

@ApplicationScoped
public class ConnectorHibernateRepository implements ConnectorRepository, PanacheRepository<ConnectorEntity> {

	@Inject
	private HibernateRepositoryEntityMapper entityMapper;
	
	@Override
	@WithTransaction
	public Uni<Connector> createConnector(final long evseId, final Connector connector) {
		final ConnectorEntity connectorEntity = this.entityMapper.createConnectorEntity(connector);
		connectorEntity.setEvse(new EvseEntity(evseId));
		return persist(connectorEntity)
				.onItem()
				.transform(this.entityMapper::createConnector);
	}

	@Override
	@WithSession
	public Uni<Collection<Connector>> getConnectors() {
		return listAll()
				.onItem()
				.transform(this.entityMapper::createConnectorCollection);
	}

	@Override
	@WithSession
	public Uni<Collection<Connector>> getConnectorsOf(final long evseId) {
		return list("evse.id", evseId)
				.onItem()
				.transform(this.entityMapper::createConnectorCollection);
	}

	@Override
	@WithSession
	public Uni<Connector> getConnector(final long id) {
		return findById(id)
				.onItem()
				.transform(this.entityMapper::createConnector);
	}

	@Override
	@WithSession
	public Uni<Connector> getConnectorByEvseConnectorId(final long evseId, final Integer evseConnectorId) {
		return getConnectorEntityByEvseConnectorId(evseId, evseConnectorId)
				.onItem()
				.transform(this.entityMapper::createConnector)
				.onFailure().recoverWithNull();
	}

	@Override
	@WithTransaction
	public Uni<Connector> updateConnectorStatusIfExists(final long evseId, final Integer evseConnectorId,
			final ConnectorStatus status, final ZonedDateTime lastStatusUpdate, final String errorCode, 
			final String errorInfo, final String vendorImplementationId, final String vendorErrorCode) {
		return getConnectorEntityByEvseConnectorId(evseId, evseConnectorId)
				.onItem()
				.ifNotNull()
				.invoke((connectorEntity) -> {
					connectorEntity.setStatus(status);
					connectorEntity.setLastStatusUpdate(lastStatusUpdate);
					connectorEntity.setErrorCode(errorCode);
					connectorEntity.setErrorInfo(errorInfo);
					connectorEntity.setVendorImplementationId(vendorImplementationId);
					connectorEntity.setVendorErrorCode(vendorErrorCode);
				})
				.onItem()
				.transform(this.entityMapper::createConnector);
		
	}

	private Uni<ConnectorEntity> getConnectorEntityByEvseConnectorId(final long evseId, final Integer evseConnectorId) {
		return find("evse.id = ?1 and evseConnectorId = ?2", evseId, evseConnectorId)
				.singleResult()
				.onFailure(NoResultException.class)
				.recoverWithNull();
	}
}