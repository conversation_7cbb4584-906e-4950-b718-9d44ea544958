package org.galiasystems.csms.repository.hibernate;

import java.util.Collection;

import org.galiasystems.csms.management.model.EvseTransactionEvent;
import org.galiasystems.csms.management.repository.EvseTransactionEventRepository;
import org.galiasystems.csms.repository.hibernate.model.EvseTransactionEntity;
import org.galiasystems.csms.repository.hibernate.model.EvseTransactionEventEntity;
import org.galiasystems.csms.repository.hibernate.utils.HibernateRepositoryEntityMapper;

import io.quarkus.hibernate.reactive.panache.PanacheRepository;
import io.quarkus.hibernate.reactive.panache.common.WithSession;
import io.quarkus.hibernate.reactive.panache.common.WithTransaction;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;

@ApplicationScoped
public class EvseTransactionEventHibernateRepository implements EvseTransactionEventRepository, PanacheRepository<EvseTransactionEventEntity> {

	@Inject
	private HibernateRepositoryEntityMapper entityMapper;
	
	@Override
	@WithTransaction
	public Uni<EvseTransactionEvent> createEvseTransactionEvent(final long evseTransactionId,
			final EvseTransactionEvent transactionEvent) {
		
		final EvseTransactionEventEntity transactionEventEntity = this.entityMapper.createEvseTransactionEventEntity(transactionEvent);
		transactionEventEntity.setTransaction(new EvseTransactionEntity(evseTransactionId));
		return persist(transactionEventEntity)
				.onFailure()
				.invoke((error) -> {
					System.out.print(error);
				})
				.onItem()
				.transform(this.entityMapper::createEvseTransactionEvent);
	}

	@Override
	@WithSession
	public Uni<EvseTransactionEvent> getEvseTransactionEvent(final long id) {
		return findById(id)
				.onItem()
				.transform(this.entityMapper::createEvseTransactionEvent);
	}

	@Override
	@WithSession
	public Uni<Collection<EvseTransactionEvent>> getEvseTransactionEvents(final long evseTransactionId) {
		return list("transaction.id", evseTransactionId)
				.onItem()
				.transform(this.entityMapper::createEvseTransactionEventCollection);
	}
}