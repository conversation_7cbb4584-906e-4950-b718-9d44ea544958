package org.galiasystems.csms.repository.hibernate.model;

import java.math.BigDecimal;
import java.util.HashSet;
import java.util.Set;

import org.galiasystems.csms.management.model.enums.DataType;
import org.galiasystems.csms.repository.hibernate.model.common.IdBaseEntity;
import org.hibernate.annotations.JdbcType;
import org.hibernate.dialect.PostgreSQLEnumJdbcType;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.NamedAttributeNode;
import jakarta.persistence.NamedEntityGraph;
import jakarta.persistence.OneToMany;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.persistence.Version;

@Entity(name="ChargingStationVariable")
@Table(name = "charging_station_variable")
@NamedEntityGraph(
	name = ChargingStationVariableEntity.ENTITY_GRAPH_VARIABLE_VALUES,
	attributeNodes = {
		@NamedAttributeNode("chargingStationVariableValues"),
	}
)
@SequenceGenerator(name = "defaultIdGen", sequenceName = "charging_station_variable_id_seq", allocationSize = 1, initialValue = 1)
public class ChargingStationVariableEntity extends IdBaseEntity {
	
	private static final long serialVersionUID = 1L;
	
	public static final String ENTITY_GRAPH_VARIABLE_VALUES = "graph.ChargingStation.variable.values";

	//
	// Component identification
	//
	private String componentName;
	
	private String componentInstance;
	
	private Integer evseId;
	
	private Integer connectorId;
	
	//
	// Variable identification
	//
	private String variableName;
	
	private String variableInstance;
	
	//
	// Variable characteristics related attributes
	//
	private String unit;
	
	private DataType dataType;
	
	private BigDecimal minLimit;
	
	private BigDecimal maxLimit;
	
	private String valuesList;
	
	private Boolean supportsMonitoring;
	
	private Integer version = Integer.valueOf(0);
	
	private ChargingStationEntity chargingStation;
	
	private Set<ChargingStationVariableValueEntity> chargingStationVariableValues = new HashSet<>();

	public ChargingStationVariableEntity() {
		super();
	}
	
	public ChargingStationVariableEntity(final Long id) {
		super(id);
	}
	
	public ChargingStationVariableEntity(final Long id, final String componentName, final String componentInstance, 
			final Integer evseId, final Integer connectorId, final String variableName, 
			final String variableInstance, final String unit, final DataType dataType,
			final BigDecimal minLimit, final BigDecimal maxLimit, final String valuesList, 
			final Boolean supportsMonitoring) {
		this(id);
		this.componentName = componentName;
		this.componentInstance = componentInstance;
		this.evseId = evseId;
		this.connectorId = connectorId;
		this.variableName = variableName;
		this.variableInstance = variableInstance;
		this.unit = unit;
		this.dataType = dataType;
		this.minLimit = minLimit;
		this.maxLimit = maxLimit;
		this.valuesList = valuesList;
		this.supportsMonitoring = supportsMonitoring;
	}

	@Column(nullable = false)
	public String getComponentName() {
		return componentName;
	}

	public void setComponentName(final String componentName) {
		this.componentName = componentName;
	}

	public String getComponentInstance() {
		return componentInstance;
	}

	public void setComponentInstance(final String componentInstance) {
		this.componentInstance = componentInstance;
	}

	public Integer getEvseId() {
		return evseId;
	}

	public void setEvseId(final Integer evseId) {
		this.evseId = evseId;
	}

	public Integer getConnectorId() {
		return connectorId;
	}

	public void setConnectorId(final Integer connectorId) {
		this.connectorId = connectorId;
	}

	@Column(nullable = false)
	public String getVariableName() {
		return variableName;
	}

	public void setVariableName(final String variableName) {
		this.variableName = variableName;
	}

	public String getVariableInstance() {
		return variableInstance;
	}

	public void setVariableInstance(final String variableInstance) {
		this.variableInstance = variableInstance;
	}

	public String getUnit() {
		return unit;
	}

	public void setUnit(final String unit) {
		this.unit = unit;
	}

	@JdbcType(PostgreSQLEnumJdbcType.class)
	public DataType getDataType() {
		return dataType;
	}

	public void setDataType(final DataType dataType) {
		this.dataType = dataType;
	}

	public BigDecimal getMinLimit() {
		return minLimit;
	}

	public void setMinLimit(final BigDecimal minLimit) {
		this.minLimit = minLimit;
	}

	public BigDecimal getMaxLimit() {
		return maxLimit;
	}

	public void setMaxLimit(final BigDecimal maxLimit) {
		this.maxLimit = maxLimit;
	}

	public String getValuesList() {
		return valuesList;
	}

	public void setValuesList(final String valuesList) {
		this.valuesList = valuesList;
	}
	
	public Boolean getSupportsMonitoring() {
		return supportsMonitoring;
	}

	public void setSupportsMonitoring(final Boolean supportsMonitoring) {
		this.supportsMonitoring = supportsMonitoring;
	}
	
	@Version
	@Column(nullable = false)
	public Integer getVersion() {
		return version;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name="charging_station_id", nullable=false)
	public ChargingStationEntity getChargingStation() {
		return chargingStation;
	}
	
	public void setChargingStation(final ChargingStationEntity chargingStation) {
		this.chargingStation = chargingStation;
	}

	@OneToMany(mappedBy="chargingStationVariable", fetch = FetchType.EAGER)
	public Set<ChargingStationVariableValueEntity> getChargingStationVariableValues() {
		return chargingStationVariableValues;
	}
	
	public void setChargingStationVariableValues(final Set<ChargingStationVariableValueEntity> chargingStationVariableValues) {
		this.chargingStationVariableValues = chargingStationVariableValues;
	}

	public void addChargingStationVariableValue(final ChargingStationVariableValueEntity chargingStationVariableValue) {
		getChargingStationVariableValues().add(chargingStationVariableValue);
		chargingStationVariableValue.setChargingStationVariable(this);
	}

	public void removeChargingStationVariableValue(final ChargingStationVariableValueEntity chargingStationVariableValue) {
		getChargingStationVariableValues().remove(chargingStationVariableValue);
		chargingStationVariableValue.setChargingStationVariable(null);
	}
}
