package org.galiasystems.csms.repository.hibernate.model.common;

import java.io.Serializable;

import org.hibernate.proxy.HibernateProxy;

import jakarta.persistence.MappedSuperclass;

@MappedSuperclass
public abstract class BaseEntity implements Serializable {

	private static final long serialVersionUID = 1L;
	
	protected Class<?> getEffectiveClass(final Object obj) {
		return obj instanceof HibernateProxy 
					? ((HibernateProxy) obj).getHibernateLazyInitializer().getPersistentClass() 
					: obj.getClass(); 
	}

}
