package org.galiasystems.csms.repository.hibernate.login.oauth2.model;

import org.galiasystems.csms.repository.hibernate.model.common.IdBaseEntity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.persistence.Version;

@Entity(name="AuthorizationCodeRequestData")
@Table(name = "authorization_code_request_data")
@SequenceGenerator(name = "defaultIdGen", sequenceName = "authorization_code_request_data_id_seq", allocationSize = 1, initialValue = 1)
public class AuthorizationCodeRequestDataEntity extends IdBaseEntity {

	private static final long serialVersionUID = 1L;

	private String sessionId;
    
    private String codeVerifier;
    
    private Integer version = Integer.valueOf(0);
    
    public AuthorizationCodeRequestDataEntity() {
		super();
	}

	public AuthorizationCodeRequestDataEntity(final String sessionId, final String codeVerifier) {
		super();
		this.sessionId = sessionId;
		this.codeVerifier = codeVerifier;
	}

	@Column(nullable = false, unique = true)
	public String getSessionId() {
		return sessionId;
	}

	public void setSessionId(final String sessionId) {
		this.sessionId = sessionId;
	}

	@Column(nullable = false)
	public String getCodeVerifier() {
		return codeVerifier;
	}

	public void setCodeVerifier(final String codeVerifier) {
		this.codeVerifier = codeVerifier;
	}
	
	@Version
	@Column(nullable = false)
	public Integer getVersion() {
		return version;
	}
}
