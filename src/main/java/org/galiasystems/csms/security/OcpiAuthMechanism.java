package org.galiasystems.csms.security;

import java.security.Permission;
import java.security.Principal;
import java.util.Collections;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

import io.netty.handler.codec.http.HttpResponseStatus;
import io.quarkus.security.credential.Credential;
import io.quarkus.security.identity.IdentityProviderManager;
import io.quarkus.security.identity.SecurityIdentity;
import io.quarkus.vertx.http.runtime.security.ChallengeData;
import io.quarkus.vertx.http.runtime.security.HttpAuthenticationMechanism;
import io.smallrye.mutiny.Uni;
import io.vertx.ext.web.RoutingContext;
import jakarta.annotation.Priority;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.enterprise.inject.Alternative;

@Alternative
@Priority(1)
@ApplicationScoped
public class OcpiAuthMechanism implements HttpAuthenticationMechanism {

	private static final String OCPI_PATH = "/ocpi";
	
	@Override
	public Uni<SecurityIdentity> authenticate(final RoutingContext context, final IdentityProviderManager identityProviderManager) {
		
		if (!context.request().uri().startsWith(OCPI_PATH)) {
			return Uni.createFrom().optional(Optional.empty());
		}
		
		return Uni.createFrom().item(new SecurityIdentity() {

			@Override
			public Principal getPrincipal() {
				return new Principal() {
					
					@Override
					public String getName() {
						return "testOcpiUser";
					}
				};
			}

			@Override
			public boolean isAnonymous() {
				return false;
			}

			@Override
			public Set<String> getRoles() {
				return Collections.emptySet();
			}

			@Override
			public boolean hasRole(final String role) {
				return false;
			}

			@Override
			public <T extends Credential> T getCredential(Class<T> credentialType) {
				return null;
			}

			@Override
			public Set<Credential> getCredentials() {
				return null;
			}

			@Override
			public <T> T getAttribute(String name) {
				return null;
			}

			@Override
			public Map<String, Object> getAttributes() {
				return Collections.emptyMap();
			}

			@Override
			public Uni<Boolean> checkPermission(Permission permission) {
				return Uni.createFrom().item(true);
			}
		});
	}

	@Override
	public Uni<ChallengeData> getChallenge(final RoutingContext context) {
		return Uni.createFrom().item(new ChallengeData(HttpResponseStatus.UNAUTHORIZED.code(),
                null, null));
	}

	@Override
	public int getPriority() {
		return 500;
	}
}
