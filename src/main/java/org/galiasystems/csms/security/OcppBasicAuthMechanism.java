package org.galiasystems.csms.security;

import java.util.Optional;
import java.util.Set;

import io.quarkus.security.identity.IdentityProviderManager;
import io.quarkus.security.identity.SecurityIdentity;
import io.quarkus.security.identity.request.AuthenticationRequest;
import io.quarkus.vertx.http.runtime.security.BasicAuthenticationMechanism;
import io.quarkus.vertx.http.runtime.security.ChallengeData;
import io.quarkus.vertx.http.runtime.security.HttpCredentialTransport;
import io.smallrye.mutiny.Uni;
import io.vertx.ext.web.RoutingContext;
import jakarta.annotation.Priority;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.enterprise.inject.Alternative;
import jakarta.inject.Inject;

@Alternative
@Priority(1)
@ApplicationScoped
public class OcppBasicAuthMechanism extends OcppAuthMechanismBase {

	@Inject
	private BasicAuthenticationMechanism basicAuthenticationMechanism;
	
	@Override
	public Uni<SecurityIdentity> authenticateOcpp(final RoutingContext context, final IdentityProviderManager identityProviderManager) {
		
		if (isCertAuthentication(context)) {
			return Uni.createFrom().optional(Optional.empty());
		}
		return this.basicAuthenticationMechanism.authenticate(context, identityProviderManager);
	}
	
	@Override
	public Uni<ChallengeData> getChallenge(final RoutingContext context) {
		return basicAuthenticationMechanism.getChallenge(context);
	}

	@Override
	public Set<Class<? extends AuthenticationRequest>> getCredentialTypes() {
		return basicAuthenticationMechanism.getCredentialTypes();
	}

	@Override
	public Uni<HttpCredentialTransport> getCredentialTransport(final RoutingContext context) {
		return basicAuthenticationMechanism.getCredentialTransport(context);
	}

	@Override
	public Uni<Boolean> sendChallenge(final RoutingContext context) {
		return basicAuthenticationMechanism.sendChallenge(context);
	}

	@Override
	public int getPriority() {
		return 498;
	}

}
