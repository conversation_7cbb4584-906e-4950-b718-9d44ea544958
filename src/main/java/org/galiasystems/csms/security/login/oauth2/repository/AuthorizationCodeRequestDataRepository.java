package org.galiasystems.csms.security.login.oauth2.repository;

import org.galiasystems.csms.security.login.oauth2.model.AuthorizationCodeRequestData;

import io.smallrye.mutiny.Uni;

public interface AuthorizationCodeRequestDataRepository {

	Uni<AuthorizationCodeRequestData> createAuthorizationCodeRequestData(
			final AuthorizationCodeRequestData authorizationCodeRequestDat);

	Uni<AuthorizationCodeRequestData> getAuthorizationCodeRequestDataBySessionId(final String sessionId);

}
