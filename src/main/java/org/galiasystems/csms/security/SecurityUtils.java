package org.galiasystems.csms.security;

import org.apache.commons.text.RandomStringGenerator;


public class SecurityUtils {
    public static String generateStrongPassword(int length) {

        RandomStringGenerator pwdGenerator = new RandomStringGenerator.Builder()
                .withinRange(33, 126)
                .filteredBy(Character::isLetterOrDigit, ch -> "!@#$%^&*()-_+=<>?".indexOf(ch) >= 0)
                .get();

        return pwdGenerator.generate(length);
    }
}