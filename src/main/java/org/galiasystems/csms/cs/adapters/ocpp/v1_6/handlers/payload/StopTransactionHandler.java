package org.galiasystems.csms.cs.adapters.ocpp.v1_6.handlers.payload;

import io.smallrye.mutiny.Uni;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import org.galiasystems.csms.cs.ChargingStationAdapterProxy;
import org.galiasystems.csms.cs.adapters.ocpp.handlers.payload.RequestPayloadHandler;
import org.galiasystems.csms.cs.adapters.ocpp.v1_6.model.payloads.common.IdTagInfo;
import org.galiasystems.csms.cs.adapters.ocpp.v1_6.model.payloads.stopTransaction.StopTransactionRequest;
import org.galiasystems.csms.cs.adapters.ocpp.v1_6.model.payloads.stopTransaction.StopTransactionResponse;
import org.galiasystems.csms.cs.adapters.ocpp.v1_6.utils.MeterValuesConverter;
import org.galiasystems.csms.management.model.EvseTransactionEvent;
import org.galiasystems.csms.management.model.MeterValue;
import org.galiasystems.csms.management.model.SampledValue;
import org.galiasystems.csms.management.model.enums.TransactionChargingState;
import org.galiasystems.csms.management.model.enums.TransactionEventTriggerReason;
import org.galiasystems.csms.management.model.enums.TransactionEventType;
import org.galiasystems.csms.management.types.TransactionEventResult;
import org.galiasystems.csms.management.types.enums.AuthorizationStatus;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Date;
import java.util.Set;

@Singleton
public class StopTransactionHandler extends RequestPayloadHandler<StopTransactionRequest, StopTransactionResponse> {

    @Inject
    private ChargingStationAdapterProxy chargingStationAdapterProxy;

    @Override
    protected Class<StopTransactionRequest> getRequestPayloadType() {
        return StopTransactionRequest.class;
    }

    @Override
    protected Uni<StopTransactionResponse> onMessage(final long chargingStationId, String messageId, final StopTransactionRequest requestPayload) {

    	final String transactionId = String.valueOf(requestPayload.transactionId());
    	
    	final EvseTransactionEvent transactionEvent = MeterValuesConverter.createEvseTransactionEvent(requestPayload.transactionData());
    	final EvseTransactionEvent transactionStopEvent = this.createStopTransactionEvent(requestPayload);
    	
        return this.chargingStationAdapterProxy.transactionEventReceived(chargingStationId, null, null,
    			transactionId, transactionEvent)
        		.onItem()
        		.transformToUni((unused) -> this.chargingStationAdapterProxy.transactionEventReceived(chargingStationId, null, null,
        			transactionId, transactionStopEvent))
                .onItem()
                .transform(this::createTransactionEventResponse);
    }

    private EvseTransactionEvent createStopTransactionEvent(final StopTransactionRequest requestPayload) {
    	
    	final SampledValue sampledValue = SampledValue.builder().value(BigDecimal.valueOf(requestPayload.meterStop())).build();
    	final MeterValue meterValue = new MeterValue(requestPayload.timestamp(), Set.of(sampledValue));
    	
        return EvseTransactionEvent.builder().eventType(TransactionEventType.Ended)
                .timestamp(Date.from(requestPayload.timestamp().toInstant()))
                .triggerReason(TransactionEventTriggerReason.Trigger)
                .eventSeqNo(2)
                .offline(false)
                .chargingState(TransactionChargingState.EVConnected)
                .meterValues(Set.of(meterValue))
                .build();
    }

    private StopTransactionResponse createTransactionEventResponse(final TransactionEventResult transactionEventResult) {

        return new StopTransactionResponse(
                new IdTagInfo(ZonedDateTime.now(), "", AuthorizationStatus.Accepted));
    }
}