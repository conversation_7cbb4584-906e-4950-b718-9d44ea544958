package org.galiasystems.csms.cs.adapters.ocpp.v1_6.model.payloads.triggerMessage;

import org.galiasystems.csms.cs.adapters.ocpp.payloads.CSMSRequest;

/**
 * PDU sent by the Central System to the Charge Point
 *
 * @param requestedMessage Required
 *                         Field type: MessageTrigger
 *                         Cardinality: 1..1
 * @param connectorId      Optional. Only filled in when request applies to a specific connector
 *                         Field type: integer
 *                         Cardinality: 0..1
 */
public record TriggerMessageRequest(MessageTrigger requestedMessage, Integer connectorId) implements CSMSRequest {
}