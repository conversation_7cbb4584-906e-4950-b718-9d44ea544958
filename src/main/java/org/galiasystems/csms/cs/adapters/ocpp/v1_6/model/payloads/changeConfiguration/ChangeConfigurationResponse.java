package org.galiasystems.csms.cs.adapters.ocpp.v1_6.model.payloads.changeConfiguration;

import org.galiasystems.csms.cs.adapters.ocpp.payloads.CSResponse;

/**
 * U returned from Charge Point to Central System
 *
 * @param status Required. Returns whether configuration change has been accepted
 *               Field type: ConfigurationStatus
 *               Cardinality: 1..1
 */
public record ChangeConfigurationResponse(ConfigurationStatus status) implements CSResponse {
}