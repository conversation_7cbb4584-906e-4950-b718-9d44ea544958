package org.galiasystems.csms.cs.adapters.ocpp.messages;

import org.galiasystems.csms.cs.adapters.ocpp.payloads.CSMSRequest;

/**
 * Request message sending by the server.
 */
public class SendingCallMessage<A extends Enum<A>> extends CallMessage<CSMSRequest, A> {

	public SendingCallMessage(final String messageId, final A action, final CSMSRequest payload) {
		super(messageId, action, payload);
	}

	@Override
	public String toString() {
		return "SendingCallMessage " + super.toString();
	}
}