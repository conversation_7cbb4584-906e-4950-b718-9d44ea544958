package org.galiasystems.csms.cs.adapters.ocpp.messages;

/**
 * Response message.
 * 
 * If the call can be handled correctly the result will be a regular CALLRESULT. 
 * 
 * Error situations that are covered by the definition of the OCPP response definition are not considered errors in this context. 
 * They are regular results and as such will be treated as a normal CALLRESULT, 
 * even if the result is undesirable for the recipient.
 * 
 * A CALLRESULT always consists of 3 elements: The standard elements MessageTypeId and MessageId and a payload, 
 * containing the response to the Action in the original Call.
 * 
 * The syntax of a CALLRESULT looks like this: 
 * [<MessageTypeId>, "<MessageId>", {<Payload>}]
 */
public abstract class CallResultMessage<P> extends MessageWithPayload<P> {

	protected CallResultMessage(final String messageId, final P payload) {
		super(MessageType.CallResultMessage, messageId, payload);
	}

	@Override
	public String toString() {
		return "CallResultMessage " + super.toString();
	}
}