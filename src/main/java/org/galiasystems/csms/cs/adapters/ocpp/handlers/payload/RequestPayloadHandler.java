package org.galiasystems.csms.cs.adapters.ocpp.handlers.payload;

import org.jboss.logging.Logger;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import io.smallrye.mutiny.Uni;
import jakarta.inject.Inject;

public abstract class RequestPayloadHandler<ReqP, RespP> {

	@Inject
	protected Logger log;
	
	@Inject
	ObjectMapper objectMapper;

	public Uni<RespP> onMessage(final long chargingStationId, final String messageId, final String requestPayloadAsString) {
		
		if (this.log.isDebugEnabled()) {
			this.log.debug("Request payload received: chargingStationId=" + chargingStationId 
					+ "; messageId: " + messageId 
					+ "; request payload: " + requestPayloadAsString 
					+ "; handler: " + getClass().getSimpleName());
		}
		
		try {
			final ReqP payload = objectMapper.readValue(requestPayloadAsString, getRequestPayloadType());
			return onMessage(chargingStationId, messageId, payload);
		} catch (JsonProcessingException e) {
			throw new IllegalStateException(e);
		}
		
	}
	
	protected abstract Class<ReqP> getRequestPayloadType();

	protected abstract Uni<RespP> onMessage(final long chargingStationId, final String messageId,
			ReqP requestPayload);
	
	
}