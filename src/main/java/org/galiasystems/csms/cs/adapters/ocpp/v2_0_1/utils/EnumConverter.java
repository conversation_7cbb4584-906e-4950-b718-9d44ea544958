package org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.utils;

import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.changeAvailability.ChangeAvailabilityStatusEnumType;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.changeAvailability.OperationalStatusEnumType;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.common.AttributeEnumType;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.common.AuthorizationStatusEnumType;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.common.GenericDeviceModelStatusEnumType;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.common.IdTokenEnumType;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.common.LocationEnumType;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.common.MeasurandEnumType;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.common.MessageFormatEnumType;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.common.PhaseEnumType;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.common.ReadingContextEnumType;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.common.RequestStartStopStatusEnumType;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.getBaseReport.ReportBaseEnumType;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.getVariables.GetVariableStatusEnumType;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.notifyReport.DataEnumType;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.notifyReport.MutabilityEnumType;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.reset.ResetEnumType;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.reset.ResetStatusEnumType;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.setVariables.SetVariableStatusEnumType;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.statusNotification.ConnectorStatusEnumType;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.transactionEvent.ChargingStateEnumType;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.transactionEvent.ReasonEnumType;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.transactionEvent.TransactionEventEnumType;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.transactionEvent.TriggerReasonEnumType;
import org.galiasystems.csms.management.model.enums.AvailabilityStatus;
import org.galiasystems.csms.management.model.enums.ConnectorStatus;
import org.galiasystems.csms.management.model.enums.DataType;
import org.galiasystems.csms.management.model.enums.IdTokenType;
import org.galiasystems.csms.management.model.enums.Measurand;
import org.galiasystems.csms.management.model.enums.MeasuredPhase;
import org.galiasystems.csms.management.model.enums.MeasurementLocation;
import org.galiasystems.csms.management.model.enums.ReportType;
import org.galiasystems.csms.management.model.enums.ResetType;
import org.galiasystems.csms.management.model.enums.SampledValueReadingContext;
import org.galiasystems.csms.management.model.enums.TransactionChargingState;
import org.galiasystems.csms.management.model.enums.TransactionEventTriggerReason;
import org.galiasystems.csms.management.model.enums.TransactionEventType;
import org.galiasystems.csms.management.model.enums.TransactionReason;
import org.galiasystems.csms.management.model.enums.VariableValueMutability;
import org.galiasystems.csms.management.model.enums.VariableValueType;
import org.galiasystems.csms.management.types.enums.AuthorizationStatus;
import org.galiasystems.csms.management.types.enums.ChangeAvailabilityResultStatus;
import org.galiasystems.csms.management.types.enums.GetVariableResultStatus;
import org.galiasystems.csms.management.types.enums.MessageFormat;
import org.galiasystems.csms.management.types.enums.ReportResultStatus;
import org.galiasystems.csms.management.types.enums.RequestStartStopTransactionStatus;
import org.galiasystems.csms.management.types.enums.ResetStatus;
import org.galiasystems.csms.management.types.enums.SetVariableResultStatus;

public final class EnumConverter {
	
	private EnumConverter() {
	}
	
	////////////////////////////////////////////////////////////////
	/// Conversion functions from CSMS enums to OCPP enums
	////////////////////////////////////////////////////////////////
	
	public static ReportBaseEnumType convertReportType(final ReportType reportType) {
		
		if (reportType == null) {
			return null;
		}
		
		switch(reportType) {
			case ReportType.BaseReport_ConfigurationInventory: 
				return ReportBaseEnumType.ConfigurationInventory;
			case ReportType.BaseReport_FullInventory: 
				return ReportBaseEnumType.FullInventory;
			case ReportType.BaseReport_SummaryInventory:
				return ReportBaseEnumType.SummaryInventory;
			default: {
				throw new IllegalStateException("Unsupported ReportType: " + reportType.name());
			}
		}
	}
	
	public static AttributeEnumType convertVariableValueType(final VariableValueType type) {
		
		if (type == null) {
			return null;
		}
		
		switch(type) {
			case VariableValueType.Actual:
				return AttributeEnumType.Actual;
			case VariableValueType.Target:
				return AttributeEnumType.Target;
			case VariableValueType.MinSet:
				return AttributeEnumType.MinSet;
			case VariableValueType.MaxSet:
				return AttributeEnumType.MaxSet;
			default:
				throw new IllegalStateException("Unsupported VariableValueType: " + type.name());
		}
	}
	
	public static ResetEnumType convertResetType(final ResetType resetType) {
		
		if (resetType == null) {
			return null;
		}
		
		switch(resetType) {
			case ResetType.Immediate: 
				return ResetEnumType.Immediate;
			case ResetType.OnIdle: 
				return ResetEnumType.OnIdle;
			default: {
				throw new IllegalStateException("Unsupported ResetType: " + resetType.name());
			}
		}
	}
	
	public static OperationalStatusEnumType convertAvailabilityStatus(final AvailabilityStatus availabilityStatus) {
		
		if (availabilityStatus == null) {
			return null;
		}
		
		switch(availabilityStatus) {
			case AvailabilityStatus.Inoperative: 
				return OperationalStatusEnumType.Inoperative;
			case AvailabilityStatus.Operative: 
				return OperationalStatusEnumType.Operative;
			default: {
				throw new IllegalStateException("Unsupported AvailabilityStatus: " + availabilityStatus.name());
			}
		}
	}
	
	public static MessageFormatEnumType convertMessageFormat(final MessageFormat format) {
		
		if (format == null) {
			return null;
		}
		
		switch(format) {
			case MessageFormat.ASCII: 
				return MessageFormatEnumType.ASCII;
			case MessageFormat.HTML: 
				return MessageFormatEnumType.HTML;
			case MessageFormat.URI: 
				return MessageFormatEnumType.URI;
			case MessageFormat.UTF8: 
				return MessageFormatEnumType.UTF8;
			default: {
				throw new IllegalStateException("Unsupported MessageFormat: " + format.name());
			}
		}
	}
	
	public static AuthorizationStatusEnumType convertAuthorizationStatus(final AuthorizationStatus status) {
		
		if (status == null) {
			return null;
		}
		
		switch(status) {
			case AuthorizationStatus.Accepted: 
				return AuthorizationStatusEnumType.Accepted;
			case AuthorizationStatus.Blocked: 
				return AuthorizationStatusEnumType.Blocked;
			case AuthorizationStatus.ConcurrentTx: 
				return AuthorizationStatusEnumType.ConcurrentTx;
			case AuthorizationStatus.Expired: 
				return AuthorizationStatusEnumType.Expired;
			case AuthorizationStatus.Invalid: 
				return AuthorizationStatusEnumType.Invalid;
			case AuthorizationStatus.NoCredit: 
				return AuthorizationStatusEnumType.NoCredit;
			case AuthorizationStatus.NotAllowedTypeEVSE: 
				return AuthorizationStatusEnumType.NotAllowedTypeEVSE;
			case AuthorizationStatus.NotAtThisLocation: 
				return AuthorizationStatusEnumType.NotAtThisLocation;
			case AuthorizationStatus.NotAtThisTime: 
				return AuthorizationStatusEnumType.NotAtThisTime;
			case AuthorizationStatus.Unknown: 
				return AuthorizationStatusEnumType.Unknown;
			default: {
				throw new IllegalStateException("Unsupported AuthorizationStatus: " + status.name());
			}
		}
	}
	

	public static IdTokenEnumType convertIdTokenType(final IdTokenType idTokenType) {
		
		if (idTokenType == null) {
			return null;
		}
		
		switch(idTokenType) {
			case IdTokenType.Central:
				return IdTokenEnumType.Central;
			case IdTokenType.eMAID:
				return IdTokenEnumType.eMAID;
			case IdTokenType.ISO14443:
				return IdTokenEnumType.ISO14443;
			case IdTokenType.ISO15693:
				return IdTokenEnumType.ISO15693;
			case IdTokenType.KeyCode:
				return IdTokenEnumType.KeyCode;
			case IdTokenType.Local:
				return IdTokenEnumType.Local;
			case IdTokenType.MacAddress:
				return IdTokenEnumType.MacAddress;
			case IdTokenType.NoAuthorization:
				return IdTokenEnumType.NoAuthorization;
			default:
				throw new IllegalStateException("Unknown IdTokenType type: " + idTokenType.name());
		}
	}

	////////////////////////////////////////////////////////////////
	/// Conversion functions from OCPP enums to CSMS enums 
	////////////////////////////////////////////////////////////////
	
	public static ReportResultStatus convertGenericDeviceModelStatusEnumType(
			final GenericDeviceModelStatusEnumType genericDeviceModelStatusEnumType) {
		
		if (genericDeviceModelStatusEnumType == null) {
			return null;
		}
	
		switch(genericDeviceModelStatusEnumType) {
			case GenericDeviceModelStatusEnumType.Accepted: {
				return ReportResultStatus.Accepted;
			}
			case GenericDeviceModelStatusEnumType.Rejected: {
				return ReportResultStatus.Rejected;
			}
			case GenericDeviceModelStatusEnumType.NotSupported: {
				return ReportResultStatus.NotSupported;
			}
			case GenericDeviceModelStatusEnumType.EmptyResultSet: {
				return ReportResultStatus.EmptyResultSet;
			}
			default:
				throw new IllegalStateException("Unsupported GenericDeviceModelStatusEnumType: " + genericDeviceModelStatusEnumType);
		}
	}
	
	public static DataType convertDataEnumType(final DataEnumType dataType) {
		
		if (dataType == null) {
			return null;
		}
		
		switch(dataType) {
			case DataEnumType.string:
				return DataType.String;
			case DataEnumType.decimal:
				return DataType.Decimal;
			case DataEnumType.integer:
				return DataType.Integer;
			case DataEnumType.dateTime:
				return DataType.DateTime;
			case DataEnumType.Boolean:
				return DataType.Boolean;
			case DataEnumType.OptionList:
				return DataType.OptionList;
			case DataEnumType.SequenceList:
				return DataType.SequenceList;
			case DataEnumType.MemberList:
				return DataType.MemberList;
			default:
				throw new IllegalStateException("Unknown DataEnumType type: " + dataType.name());
		
		}
	}
	
	public static VariableValueMutability convertMutabilityEnumType(final MutabilityEnumType mutability) {
		
		if (mutability == null) {
			return null;
		}
		
		switch(mutability) {
			case MutabilityEnumType.ReadOnly:
				return VariableValueMutability.ReadOnly;
			case MutabilityEnumType.WriteOnly:
				return VariableValueMutability.WriteOnly;
			case MutabilityEnumType.ReadWrite:
				return VariableValueMutability.ReadWrite;
			default:
				throw new IllegalStateException("Unknown MutabilityEnumType type: " + mutability.name());
		}
	}

	public static VariableValueType convertAttributeEnumType(final AttributeEnumType type) {
		
		if (type == null) {
			return null;
		}
		
		switch(type) {
			case AttributeEnumType.Actual:
				return VariableValueType.Actual;
			case AttributeEnumType.Target:
				return VariableValueType.Target;
			case AttributeEnumType.MinSet:
				return VariableValueType.MinSet;
			case AttributeEnumType.MaxSet:
				return VariableValueType.MaxSet;
			default:
				throw new IllegalStateException("Unknown AttributeEnumType type: " + type.name());
		}
	}
	
	public static ConnectorStatus convertConnectorStatusEnumType(final ConnectorStatusEnumType connectorStatus) {
		
		if (connectorStatus == null) {
			return null;
		}
		
		switch(connectorStatus) {
			case ConnectorStatusEnumType.Available:
				return ConnectorStatus.Available;
			case ConnectorStatusEnumType.Occupied:
				return ConnectorStatus.Occupied;
			case ConnectorStatusEnumType.Reserved:
				return ConnectorStatus.Reserved;
			case ConnectorStatusEnumType.Unavailable:
				return ConnectorStatus.Unavailable;
			case ConnectorStatusEnumType.Faulted:
				return ConnectorStatus.Faulted;
			default:
				throw new IllegalStateException("Unknown ConnectorStatusEnumType type: " + connectorStatus.name());
		}
	}
	
	public static ResetStatus convertResetStatusEnumType(final ResetStatusEnumType resetStatus) {
		
		if (resetStatus == null) {
			return null;
		}
		
		switch(resetStatus) {
			case ResetStatusEnumType.Accepted:
				return ResetStatus.Accepted;
			case ResetStatusEnumType.Rejected:
				return ResetStatus.Rejected;
			case ResetStatusEnumType.Scheduled:
				return ResetStatus.Scheduled;
			default:
				throw new IllegalStateException("Unknown ResetStatusEnumType type: " + resetStatus.name());
		}
	}
	
	public static SetVariableResultStatus convertSetVariableStatusEnumType(final SetVariableStatusEnumType setVariableResultStatus) {
		
		if (setVariableResultStatus == null) {
			return null;
		}
		
		switch(setVariableResultStatus) {
			case SetVariableStatusEnumType.Accepted:
				return SetVariableResultStatus.Accepted;
			case SetVariableStatusEnumType.Rejected:
				return SetVariableResultStatus.Rejected;
			case SetVariableStatusEnumType.UnknownComponent:
				return SetVariableResultStatus.UnknownComponent;
			case SetVariableStatusEnumType.UnknownVariable:
				return SetVariableResultStatus.UnknownVariable;
			case SetVariableStatusEnumType.NotSupportedAttributeType:
				return SetVariableResultStatus.NotSupportedAttributeType;
			case SetVariableStatusEnumType.RebootRequired:
				return SetVariableResultStatus.RebootRequired;
			default:
				throw new IllegalStateException("Unknown SetVariableStatusEnumType type: " + setVariableResultStatus.name());
		}
	}
	
	public static GetVariableResultStatus convertGetVariableStatusEnumType(final GetVariableStatusEnumType getVariableResultStatus) {
		
		if (getVariableResultStatus == null) {
			return null;
		}
		
		switch(getVariableResultStatus) {
			case GetVariableStatusEnumType.Accepted:
				return GetVariableResultStatus.Accepted;
			case GetVariableStatusEnumType.Rejected:
				return GetVariableResultStatus.Rejected;
			case GetVariableStatusEnumType.UnknownComponent:
				return GetVariableResultStatus.UnknownComponent;
			case GetVariableStatusEnumType.UnknownVariable:
				return GetVariableResultStatus.UnknownVariable;
			case GetVariableStatusEnumType.NotSupportedAttributeType:
				return GetVariableResultStatus.NotSupportedAttributeType;
			default:
				throw new IllegalStateException("Unknown GetVariableStatusEnumType type: " + getVariableResultStatus.name());
		}
	}
	
	public static ChangeAvailabilityResultStatus convertChangeAvailabilityStatusEnumType(
			final ChangeAvailabilityStatusEnumType changeAvailabilityResultStatus) {
		
		if (changeAvailabilityResultStatus == null) {
			return null;
		}
		
		switch(changeAvailabilityResultStatus) {
			case ChangeAvailabilityStatusEnumType.Accepted:
				return ChangeAvailabilityResultStatus.Accepted;
			case ChangeAvailabilityStatusEnumType.Rejected:
				return ChangeAvailabilityResultStatus.Rejected;
			case ChangeAvailabilityStatusEnumType.Scheduled:
				return ChangeAvailabilityResultStatus.Scheduled;
			default:
				throw new IllegalStateException("Unknown ChangeAvailabilityStatusEnumType type: " + changeAvailabilityResultStatus.name());
		}
	}

	public static TransactionEventType convertTransactionEventEnumType(final TransactionEventEnumType eventType) {
		
		if (eventType == null) {
			return null;
		}
		
		switch(eventType) {
			case TransactionEventEnumType.Started:
				return TransactionEventType.Started;
			case TransactionEventEnumType.Updated:
				return TransactionEventType.Updated;
			case TransactionEventEnumType.Ended:
				return TransactionEventType.Ended;
			default:
				throw new IllegalStateException("Unknown TransactionEventEnumType type: " + eventType.name());
		}
	}

	public static TransactionEventTriggerReason convertTriggerReasonEnumType(final TriggerReasonEnumType triggerReason) {
		
		if (triggerReason == null) {
			return null;
		}
		
		switch(triggerReason) {
			case TriggerReasonEnumType.Authorized:
				return TransactionEventTriggerReason.Authorized;
			case TriggerReasonEnumType.CablePluggedIn:
				return TransactionEventTriggerReason.CablePluggedIn;
			case TriggerReasonEnumType.ChargingRateChanged:
				return TransactionEventTriggerReason.ChargingRateChanged;
			case TriggerReasonEnumType.ChargingStateChanged:
				return TransactionEventTriggerReason.ChargingStateChanged;
			case TriggerReasonEnumType.Deauthorized:
				return TransactionEventTriggerReason.Deauthorized;
			case TriggerReasonEnumType.EnergyLimitReached:
				return TransactionEventTriggerReason.EnergyLimitReached;
			case TriggerReasonEnumType.EVCommunicationLost:
				return TransactionEventTriggerReason.EVCommunicationLost;
			case TriggerReasonEnumType.EVConnectTimeout:
				return TransactionEventTriggerReason.EVConnectTimeout;
			case TriggerReasonEnumType.MeterValueClock:
				return TransactionEventTriggerReason.MeterValueClock;
			case TriggerReasonEnumType.MeterValuePeriodic:
				return TransactionEventTriggerReason.MeterValuePeriodic;
			case TriggerReasonEnumType.TimeLimitReached:
				return TransactionEventTriggerReason.TimeLimitReached;
			case TriggerReasonEnumType.Trigger:
				return TransactionEventTriggerReason.Trigger;
			case TriggerReasonEnumType.UnlockCommand:
				return TransactionEventTriggerReason.UnlockCommand;
			case TriggerReasonEnumType.StopAuthorized:
				return TransactionEventTriggerReason.StopAuthorized;
			case TriggerReasonEnumType.EVDeparted:
				return TransactionEventTriggerReason.EVDeparted;
			case TriggerReasonEnumType.EVDetected:
				return TransactionEventTriggerReason.EVDetected;
			case TriggerReasonEnumType.RemoteStop:
				return TransactionEventTriggerReason.RemoteStop;
			case TriggerReasonEnumType.RemoteStart:
				return TransactionEventTriggerReason.RemoteStart;
			case TriggerReasonEnumType.AbnormalCondition:
				return TransactionEventTriggerReason.AbnormalCondition;
			case TriggerReasonEnumType.SignedDataReceived:
				return TransactionEventTriggerReason.SignedDataReceived;
			case TriggerReasonEnumType.ResetCommand:
				return TransactionEventTriggerReason.ResetCommand;
			default:
				throw new IllegalStateException("Unknown TriggerReasonEnumType type: " + triggerReason.name());
		}
	}

	public static TransactionChargingState convertChargingStateEnumType(final ChargingStateEnumType chargingState) {
		
		if (chargingState == null) {
			return null;
		}
		
		switch(chargingState) {
			case ChargingStateEnumType.Charging:
				return TransactionChargingState.Charging;
			case ChargingStateEnumType.EVConnected:
				return TransactionChargingState.EVConnected;
			case ChargingStateEnumType.SuspendedEV:
				return TransactionChargingState.SuspendedEV;
			case ChargingStateEnumType.SuspendedEVSE:
				return TransactionChargingState.SuspendedEVSE;
			case ChargingStateEnumType.Idle:
				return TransactionChargingState.Idle;
			default:
				throw new IllegalStateException("Unknown ChargingStateEnumType type: " + chargingState.name());
		}
	}

	public static TransactionReason convertReasonEnumType(final ReasonEnumType stoppedReason) {
		
		if (stoppedReason == null) {
			return null;
		}
		
		switch(stoppedReason) {
			case ReasonEnumType.DeAuthorized:
				return TransactionReason.DeAuthorized;
			case ReasonEnumType.EmergencyStop:
				return TransactionReason.EmergencyStop;
			case ReasonEnumType.EnergyLimitReached:
				return TransactionReason.EnergyLimitReached;
			case ReasonEnumType.EVDisconnected:
				return TransactionReason.EVDisconnected;
			case ReasonEnumType.GroundFault:
				return TransactionReason.GroundFault;
			case ReasonEnumType.ImmediateReset:
				return TransactionReason.ImmediateReset;
			case ReasonEnumType.Local:
				return TransactionReason.Local;
			case ReasonEnumType.LocalOutOfCredit:
				return TransactionReason.LocalOutOfCredit;
			case ReasonEnumType.MasterPass:
				return TransactionReason.MasterPass;
			case ReasonEnumType.Other:
				return TransactionReason.Other;
			case ReasonEnumType.OvercurrentFault:
				return TransactionReason.OvercurrentFault;
			case ReasonEnumType.PowerLoss:
				return TransactionReason.PowerLoss;
			case ReasonEnumType.PowerQuality:
				return TransactionReason.PowerQuality;
			case ReasonEnumType.Reboot:
				return TransactionReason.Reboot;
			case ReasonEnumType.Remote:
				return TransactionReason.Remote;
			case ReasonEnumType.SOCLimitReached:
				return TransactionReason.SOCLimitReached;
			case ReasonEnumType.StoppedByEV:
				return TransactionReason.StoppedByEV;
			case ReasonEnumType.TimeLimitReached:
				return TransactionReason.TimeLimitReached;
			case ReasonEnumType.Timeout:
				return TransactionReason.Timeout;
			default:
				throw new IllegalStateException("Unknown ReasonEnumType type: " + stoppedReason.name());
		}
	}

	public static IdTokenType convertIdTokenEnumType(final IdTokenEnumType idTokenType) {
		
		if (idTokenType == null) {
			return null;
		}
		
		switch(idTokenType) {
			case IdTokenEnumType.Central:
				return IdTokenType.Central;
			case IdTokenEnumType.eMAID:
				return IdTokenType.eMAID;
			case IdTokenEnumType.ISO14443:
				return IdTokenType.ISO14443;
			case IdTokenEnumType.ISO15693:
				return IdTokenType.ISO15693;
			case IdTokenEnumType.KeyCode:
				return IdTokenType.KeyCode;
			case IdTokenEnumType.Local:
				return IdTokenType.Local;
			case IdTokenEnumType.MacAddress:
				return IdTokenType.MacAddress;
			case IdTokenEnumType.NoAuthorization:
				return IdTokenType.NoAuthorization;
			default:
				throw new IllegalStateException("Unknown IdTokenEnumType type: " + idTokenType.name());
		}
	}

	public static SampledValueReadingContext convertReadingContextEnumType(final ReadingContextEnumType context) {
		
		if (context == null) {
			return null;
		}
		
		switch(context) {
			case ReadingContextEnumType.Interruption_Begin:
				return SampledValueReadingContext.Interruption_Begin;
			case ReadingContextEnumType.Interruption_End:
				return SampledValueReadingContext.Interruption_End;
			case ReadingContextEnumType.Other:
				return SampledValueReadingContext.Other;
			case ReadingContextEnumType.Sample_Clock:
				return SampledValueReadingContext.Sample_Clock;
			case ReadingContextEnumType.Sample_Periodic:
				return SampledValueReadingContext.Sample_Periodic;
			case ReadingContextEnumType.Transaction_Begin:
				return SampledValueReadingContext.Transaction_Begin;
			case ReadingContextEnumType.Transaction_End:
				return SampledValueReadingContext.Transaction_End;
			case ReadingContextEnumType.Trigger:
				return SampledValueReadingContext.Trigger;
			default:
				throw new IllegalStateException("Unknown ReadingContextEnumType type: " + context.name());
		}
	}

	public static Measurand convertMeasurandEnumType(final MeasurandEnumType measurand) {
		
		if (measurand == null) {
			return null;
		}
		
		switch(measurand) {
			case MeasurandEnumType.Current_Export:
				return Measurand.Current_Export;
			case MeasurandEnumType.Current_Import:
				return Measurand.Current_Import;
			case MeasurandEnumType.Current_Offered:
				return Measurand.Current_Offered;
			case MeasurandEnumType.Energy_Active_Export_Register:
				return Measurand.Energy_Active_Export_Register;
			case MeasurandEnumType.Energy_Active_Import_Register:
				return Measurand.Energy_Active_Import_Register;
			case MeasurandEnumType.Energy_Reactive_Export_Register:
				return Measurand.Energy_Reactive_Export_Register;
			case MeasurandEnumType.Energy_Reactive_Import_Register:
				return Measurand.Energy_Reactive_Import_Register;
			case MeasurandEnumType.Energy_Active_Export_Interval:
				return Measurand.Energy_Active_Export_Interval;
			case MeasurandEnumType.Energy_Active_Import_Interval:
				return Measurand.Energy_Active_Import_Interval;
			case MeasurandEnumType.Energy_Active_Net:
				return Measurand.Energy_Active_Net;
			case MeasurandEnumType.Energy_Reactive_Export_Interval:
				return Measurand.Energy_Reactive_Export_Interval;
			case MeasurandEnumType.Energy_Reactive_Import_Interval:
				return Measurand.Energy_Reactive_Import_Interval;
			case MeasurandEnumType.Energy_Reactive_Net:
				return Measurand.Energy_Reactive_Net;
			case MeasurandEnumType.Energy_Apparent_Net:
				return Measurand.Energy_Apparent_Net;
			case MeasurandEnumType.Energy_Apparent_Import:
				return Measurand.Energy_Apparent_Import;
			case MeasurandEnumType.Energy_Apparent_Export:
				return Measurand.Energy_Apparent_Export;
			case MeasurandEnumType.Frequency:
				return Measurand.Frequency;
			case MeasurandEnumType.Power_Active_Export:
				return Measurand.Power_Active_Export;
			case MeasurandEnumType.Power_Active_Import:
				return Measurand.Power_Active_Import;
			case MeasurandEnumType.Power_Factor:
				return Measurand.Power_Factor;
			case MeasurandEnumType.Power_Offered:
				return Measurand.Power_Offered;
			case MeasurandEnumType.Power_Reactive_Export:
				return Measurand.Power_Reactive_Export;
			case MeasurandEnumType.Power_Reactive_Import:
				return Measurand.Power_Reactive_Import;
			case MeasurandEnumType.SoC:
				return Measurand.SoC;
			case MeasurandEnumType.Voltage:
				return Measurand.Voltage;
			default:
				throw new IllegalStateException("Unknown MeasurandEnumType type: " + measurand.name());
		}
	}

	public static MeasuredPhase convertPhaseEnumType(final PhaseEnumType phase) {
		
		if (phase == null) {
			return null;
		}
		
		switch(phase) {
			case PhaseEnumType.L1:
				return MeasuredPhase.L1;
			case PhaseEnumType.L2:
				return MeasuredPhase.L2;
			case PhaseEnumType.L3:
				return MeasuredPhase.L3;
			case PhaseEnumType.N:
				return MeasuredPhase.N;
			case PhaseEnumType.L1_N:
				return MeasuredPhase.L1_N;
			case PhaseEnumType.L2_N:
				return MeasuredPhase.L2_N;
			case PhaseEnumType.L3_N:
				return MeasuredPhase.L3_N;
			case PhaseEnumType.L1_L2:
				return MeasuredPhase.L1_L2;
			case PhaseEnumType.L2_L3:
				return MeasuredPhase.L2_L3;
			case PhaseEnumType.L3_L1:
				return MeasuredPhase.L3_L1;
			default:
				throw new IllegalStateException("Unknown PhaseEnumType type: " + phase.name());
		}
	}

	public static MeasurementLocation convertLocationEnumType(final LocationEnumType location) {
		
		if (location == null) {
			return null;
		}
		
		switch(location) {
			case LocationEnumType.Body:
				return MeasurementLocation.Body;
			case LocationEnumType.Cable:
				return MeasurementLocation.Cable;
			case LocationEnumType.EV:
				return MeasurementLocation.EV;
			case LocationEnumType.Inlet:
				return MeasurementLocation.Inlet;
			case LocationEnumType.Outlet:
				return MeasurementLocation.Outlet;
			default:
				throw new IllegalStateException("Unknown LocationEnumType type: " + location.name());
		}
	}

	public static RequestStartStopTransactionStatus convertRequestStartStopStatusEnumType(final RequestStartStopStatusEnumType status) {
		if (status == null) {
			return null;
		}

		switch (status) {
			case RequestStartStopStatusEnumType.Accepted:
				return RequestStartStopTransactionStatus.Accepted;
			case RequestStartStopStatusEnumType.Rejected:
				return RequestStartStopTransactionStatus.Rejected;
			default:
				throw new IllegalStateException("Unknown RequestStartStopStatusEnumType type: " + status.name());
		}
	}
}
