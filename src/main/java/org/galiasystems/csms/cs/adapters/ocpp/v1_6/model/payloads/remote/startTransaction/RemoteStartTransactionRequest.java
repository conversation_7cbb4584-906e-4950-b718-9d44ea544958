package org.galiasystems.csms.cs.adapters.ocpp.v1_6.model.payloads.remote.startTransaction;

import org.galiasystems.csms.cs.adapters.ocpp.payloads.CSMSRequest;

/**
 * RemoteStartTransaction.req PDU sent to Charge Point by Central
 * System
 *
 * @param connectorId       Optional. Number of the connector on which to start the transaction.
 *                          connectorId SHALL be > 0
 *                          Field type: integer
 *                          Cardinality: 0..1
 * @param idToken           Required. The identifier that Charge Point must use to start a transaction.
 *                          Field type: IdToken (String, length 20, case insensitive)
 *                          Cardinality: 1..1
 * @param chargingProfile 	Optional. Charging Profile to be used by the Charge Point for the requested
 *                          transaction. ChargingProfilePurpose MUST be set to TxProfile
 *                          Field type: ChargingProfile
 *                          Cardinality: 0..1
 */
public record RemoteStartTransactionRequest(Integer connectorId,
                                            String idToken,
                                            ChargingProfile chargingProfile) implements CSMSRequest {
}