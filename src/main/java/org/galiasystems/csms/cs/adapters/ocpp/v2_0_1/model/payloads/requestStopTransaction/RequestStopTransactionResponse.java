package org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.requestStopTransaction;

import org.galiasystems.csms.cs.adapters.ocpp.payloads.CSResponse;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.common.RequestStartStopStatusEnumType;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.common.StatusInfoType;

/**
 * RequestStopTransactionResponse PDU sent by the Charging Station to the CSMS.
 * 
 * @param status          Required. Status indicating whether Charging Station accepts the request to stop a transaction.
 *                        <p>
 *                        Field Type: RequestStartStopStatusEnumType
 *                        Cardinality: 1..1
 *                        
 * @param statusInfo      Optional. Detailed status information.
 *                        <p>
 *                        Field Type: StatusInfoType
 *                        Cardinality: 0..1
 */
public record RequestStopTransactionResponse(RequestStartStopStatusEnumType status, StatusInfoType statusInfo) implements CSResponse {
}