package org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.common;

import com.fasterxml.jackson.annotation.JsonValue;

/**
 * Phase specifies how a measured value is to be interpreted. 
 * Please note that not all values of Phase are applicable to all Measurands.
 */
public enum PhaseEnumType {
	
	/**
	 * Measured on L1.
	 */
	L1("L1"),
	
	/**
	 * Measured on L2.
	 */
	L2("L2"),
	
	/**
	 * Measured on L3.
	 */
	L3("L3"),
	
	/**
	 * Measured on Neutral.
	 */
	N("N"),
	
	/**
	 * Measured on L1 with respect to Neutral conductor.
	 */
	L1_N("L1-N"),
	
	/**
	 * Measured on L2 with respect to Neutral conductor.
	 */
	L2_N("L2-N"),
	
	/**
	 * Measured on L3 with respect to Neutral conductor.
	 */
	L3_N("L3-N"),
	
	/**
	 * Measured between L1 and L2.
	 */
	L1_L2("L1-L2"),
	
	/**
	 * Measured between L2 and L3.
	 */
	L2_L3("L2-L3"),
	
	/**
	 * Measured between L3 and L1.
	 */
	L3_L1("L3-L1"),
	;
	
	private String phaseType;
	
	private PhaseEnumType(final String phaseType) {
		this.phaseType = phaseType;
	}
	
	@JsonValue
	public String getPhaseType() {
		return phaseType;
	}
}