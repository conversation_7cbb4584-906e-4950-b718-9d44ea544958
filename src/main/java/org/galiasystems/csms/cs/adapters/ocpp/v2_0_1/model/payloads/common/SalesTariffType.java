package org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.common;

/**
 * SalesTariffType is used in ChargingScheduleType.
 * This dataType is based on dataTypes from ISO 15118-2.
 * 
 * @param id	 					Required. SalesTariff identifier used to identify one sales tariff. 
 * 									An SAID remains a unique identifier for one schedule throughout a charging session.
 *                        	 		<p>
 *                        	 		Field Type: integer
 *                        	 		Cardinality: 1..1
 *                        
 * @param salesTariffDescription	Optional. A human readable title/short description of the sales tariff 
 * 									e.g. for HMI display purposes.
 *                        	 		<p>
 *                        	 		Field Type: string[0..32]
 *                        	 		Cardinality: 0..1
 *                        
 * @param numEPriceLevels	 		Optional. Defines the overall number of distinct price levels 
 * 									used across all provided SalesTariff elements.
 *                        	 		<p>
 *                        	 		Field Type: integer
 *                        	 		Cardinality: 0..1
 *                        
 * @param salesTariffEntry	 		Required. Encapsulating element describing all relevant details 
 * 									for one time interval of the SalesTariff. 
 * 									The number of SalesTariffEntry elements is limited by the parameter maxScheduleTuples.
 *                        	 		<p>
 *                        	 		Field Type: SalesTariffEntryType
 *                        	 		Cardinality: 1..1024
 */
public record SalesTariffType(int id, String salesTariffDescription, Integer numEPriceLevels,
		SalesTariffEntryType salesTariffEntry) {
}