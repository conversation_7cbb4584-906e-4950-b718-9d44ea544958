package org.galiasystems.csms.cs.adapters.ocpp.handlers.payload;

import org.galiasystems.csms.cs.adapters.ocpp.util.Utils;
import org.jboss.logging.Logger;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import io.smallrye.mutiny.Uni;
import io.vertx.core.eventbus.DeliveryOptions;
import io.vertx.mutiny.core.eventbus.EventBus;
import jakarta.inject.Inject;

public abstract class ResponsePayloadHandler<ReqP, RespP> {
	
	private static final DeliveryOptions DELIVERY_OPTIONS;
	static {
		DELIVERY_OPTIONS = new DeliveryOptions();
		DELIVERY_OPTIONS.setCodecName("quarkus_default_local_codec");
	}

	@Inject
    Logger log;
	
	@Inject
	ObjectMapper objectMapper;
	
	@Inject
	EventBus bus;

	public Uni<Void> onMessage(final long chargingStationId, final String messageId,
			final String responsePayloadAsString, final String requestPayloadAsString) {
		
		if (this.log.isDebugEnabled()) {
			this.log.debug("Response payload received: chargingStationId=" + chargingStationId 
					+ "; messageId: " + messageId 
					+ "; response payload: " + responsePayloadAsString 
					+ "; request payload: " + requestPayloadAsString 
					+ "; handler: " + getClass().getSimpleName());
		}
		
		try {
			final RespP responsePayload = objectMapper.readValue(responsePayloadAsString, getResponsePayloadType());
			final ReqP requestPayload = objectMapper.readValue(requestPayloadAsString, getRequestPayloadType());
			return onMessage(chargingStationId, messageId, responsePayload, requestPayload);
		} catch (JsonProcessingException e) {
			throw new IllegalStateException(e);
		}
		
	}
	
	protected abstract Class<ReqP> getRequestPayloadType();
	
	protected abstract Class<RespP> getResponsePayloadType();

	protected abstract Uni<Void> onMessage(final long chargingStationId, final String messageId,
			RespP responsePayload, ReqP requestPayload);

	protected void sendResponseMessage(final long chargingStationId, final String messageId, final Object message) {
		this.bus.publish(Utils.getSynchronousMessageId(chargingStationId, messageId), message, DELIVERY_OPTIONS);
	}
}