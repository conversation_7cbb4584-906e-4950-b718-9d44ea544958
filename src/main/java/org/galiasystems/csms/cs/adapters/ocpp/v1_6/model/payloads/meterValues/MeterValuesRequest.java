package org.galiasystems.csms.cs.adapters.ocpp.v1_6.model.payloads.meterValues;

import org.galiasystems.csms.cs.adapters.ocpp.payloads.CSRequest;
import org.galiasystems.csms.cs.adapters.ocpp.v1_6.model.payloads.common.MeterValue;

import java.util.Collection;

/**
 * sent by the Charge Point to the Central System
 *
 * @param connectorId   Required. This contains a number (>0) designating a connector of the Charge
 *                      Point.‘0’ (zero) is used to designate the main powermeter.
 *                      Field type: integer (>=0)
 *                      Cardinality: 1..1
 *                      
 * @param transactionId Optional. The transaction to which these meter samples are related.
 *                      Field type: integer
 *                      Cardinality: 0..1
 *                      
 * @param meterValue    Required. The sampled meter values with timestamps.
 *                      Field type: MeterValue
 *                      Cardinality: 1..*
 */
public record MeterValuesRequest(int connectorId, Integer transactionId,
                                 Collection<MeterValue> meterValue) implements CSRequest {
}