package org.galiasystems.csms.cs.adapters.ocpp.v1_6.model.payloads.remote.stopTransaction;

import org.galiasystems.csms.cs.adapters.ocpp.payloads.CSMSRequest;

/**
 * sent to Charge Point by Central
 * System
 *
 * @param transactionId Required. The identifier of the transaction which Charge Point is requested to
 *                      stop.
 *                      Field type: integer
 *                      Cardinality: 1..1
 */
public record RemoteStopTransactionRequest(long transactionId) implements CSMSRequest {
}