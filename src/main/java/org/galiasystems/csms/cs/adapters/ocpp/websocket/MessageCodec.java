package org.galiasystems.csms.cs.adapters.ocpp.websocket;

import org.galiasystems.csms.cs.adapters.ocpp.messages.SendingCallResultMessage;
import org.jboss.logging.Logger;

import io.quarkus.websockets.next.runtime.JsonTextMessageCodec;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;

@Singleton
public class MessageCodec extends JsonTextMessageCodec {

	@Inject
    private Logger log;
	
	@Override
    public String encode(final Object object) {
		
		final Object objectToTransform;
        if (object instanceof SendingCallResultMessage callResultMessage) {

            objectToTransform = new Object[]{callResultMessage.getMessageType().getMessageTypeId(),
        			callResultMessage.getMessageId(), callResultMessage.getPayload()};
        } else {
        	objectToTransform = object;
        }
        
        
        
        final String result = super.encode(objectToTransform);
        
        if (this.log.isDebugEnabled()) {
        	this.log.debug("Websocket message sent: " + result);
        }
        
        return result;
    }
}