package org.galiasystems.csms.cs.adapters.ocpp.v1_6.model.payloads.common;

/**
 * Allowable values of the optional "unit" field of a Value element, 
 * as used in SampledValue. Default value of "unit" is always "Wh".
 */
public enum UnitOfMeasure {
    /**
     * Watt-hours (energy). Default.
     */
    Wh,
    
    /**
     * kiloWatt-hours (energy).
     */
    kWh,
    
    /**
     * Var-hours (reactive energy).
     */
    varh,
    
    /**
     * kilovar-hours (reactive energy).
     */
    kvarh,
    
    /**
     * Watts (power).
     */
    W,
    
    /**
     * kilowatts (power).
     */
    kW,
    
    /**
     * VoltAmpere (apparent power).
     */
    VA,
    
    /**
     * kiloVolt Ampere (apparent power).
     */
    kVA,
    
    /**
     * Vars (reactive power).
     */
    var,
    
    /**
     * kilovars (reactive power).
     */
    kvar,
    
    /**
     * Amperes (current).
     */
    A,
    
    /**
     * Voltage (r.m.s. AC).
     */
    V,
    
    /**
     * Degrees (temperature).
     */
    Celsius,
    
    /**
     * Degrees (temperature).
     */
    Fahrenheit,
    
    /**
     * Degrees Kelvin (temperature).
     */
    K,
    
    /**
     * Percentage.
     */
    Percent,
    ;
}