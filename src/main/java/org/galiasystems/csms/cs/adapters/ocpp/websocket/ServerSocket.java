package org.galiasystems.csms.cs.adapters.ocpp.websocket;

import java.util.HashMap;
import java.util.Map;

import io.quarkus.websockets.next.*;
import jakarta.annotation.security.RolesAllowed;

import org.galiasystems.csms.cs.adapters.ocpp.OCPPVersion;
import org.galiasystems.csms.cs.adapters.ocpp.OcppAdapter;
import org.galiasystems.csms.cs.adapters.ocpp.messages.Message;
import org.galiasystems.csms.cs.adapters.ocpp.messages.SendingCallMessage;
import org.galiasystems.csms.cs.adapters.ocpp.messages.SendingCallResultMessage;
import org.galiasystems.csms.cs.adapters.ocpp.v1_6.Ocpp16Adapter;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.Ocpp201Adapter;
import org.galiasystems.csms.management.model.enums.Role;
import org.jboss.logging.Logger;

import io.quarkus.security.identity.SecurityIdentity;
import io.smallrye.mutiny.Uni;
import io.vertx.core.buffer.Buffer;
import jakarta.inject.Inject;

@RolesAllowed(Role.RoleNames.ROLE_CHARGING_STATION)
@WebSocket(path = ServerSocket.BASE_URI + "/{" + ServerSocket.CHARGING_STATION_IDENTITY_PATH_PARAMETER + "}")
public class ServerSocket {

	public static final String BASE_URI = "/websocket/ocpp";
	
	public static final String CHARGING_STATION_IDENTITY_PATH_PARAMETER = "chargingStationIdentityPathParameter";

	private final Map<Long, WebSocketConnection> webSocketConnections = new HashMap<>();
	
  	@Inject
    Logger log;
  	
  	@Inject
	MessageCodec messageCodec;
  	
  	@Inject
    Ocpp201Adapter ocpp201Adapter;

	@Inject
	Ocpp16Adapter ocpp16Adapter;
  	
  	@Inject
    SecurityIdentity currentIdentity;

	@OnOpen
	public Uni<Void> onOpen(final WebSocketConnection session) {
		final long chargingStationIdentity = Long.parseLong(session.pathParam(CHARGING_STATION_IDENTITY_PATH_PARAMETER));

		if (this.log.isDebugEnabled()) {
			this.log.debug("Connection opened - CHARGING_STATION_IDENTITY: " + chargingStationIdentity);
			this.log.debug("Connection opened - SecurityIdentity: " + this.currentIdentity.getPrincipal().getName());
		}

		webSocketConnections.put(chargingStationIdentity, session);

		return getAdapterForVersion(session).adapterConnectionOpened(chargingStationIdentity, SecurityProfile.Profile1)
				.onItem()
				.transformToUni((chargingStation) -> Uni.createFrom().nullItem());
	}

	@OnTextMessage(codec = MessageCodec.class)
	public Uni<Void> onTextMessage(final WebSocketConnection session, final Message message) {
		final long chargingStationIdentity = Long.parseLong(session.pathParam(CHARGING_STATION_IDENTITY_PATH_PARAMETER));
		return getAdapterForVersion(session).messageReceived(chargingStationIdentity, message);
	}
	
	@OnClose
	public Uni<Void> onClose(final WebSocketConnection session) {
		final long chargingStationIdentity = Long.parseLong(session.pathParam(CHARGING_STATION_IDENTITY_PATH_PARAMETER));
		
		if (this.log.isDebugEnabled()) {
			this.log.debug("Connection closed - CHARGING_STATION_IDENTITY: " + chargingStationIdentity);
		}
		
		webSocketConnections.remove(chargingStationIdentity);		
		
		return getAdapterForVersion(session).adapterConnectionClosed(chargingStationIdentity)
				.onItem()
				.transformToUni((chargingStation) -> Uni.createFrom().nullItem());
	}

	@OnPongMessage
  	public Uni<Void> pong(final Buffer data) {
  		if (this.log.isDebugEnabled()) {
			this.log.debug("Pong message received! ");
		}
  		
  	    return Uni.createFrom().voidItem();
  	}

	@OnError
    public String error(final Throwable t) { 
  		if (this.log.isDebugEnabled()) {
			this.log.debug("On error called! " + t.getMessage());
		}
		return "Error occurred :" + t.getMessage();
    }
	
	private OcppAdapter getAdapterForVersion(final WebSocketConnection session) {
		var negotiatedProtocol = session.subprotocol();
		if (OCPPVersion.OCPP_2_0_1.getVersion().equals(negotiatedProtocol)) {
			return ocpp201Adapter;
		} else {
			return ocpp16Adapter;
		}
	}
	

	public Uni<Void> sendMessage(final Long chargingStationIdentity, final Message message) {
		final WebSocketConnection webSocketConnection = webSocketConnections.get(chargingStationIdentity);
		
		if (webSocketConnection == null) {
			throw new IllegalStateException("Connection to Charging Station '" + chargingStationIdentity + "' not found!");
		}
		
		if (!webSocketConnection.isOpen()) {
			throw new IllegalStateException("Connection to Charging Station '" + chargingStationIdentity + "' is not opened!");
		}
		
		final Object[] messageToSend = this.createArrayFromMessage(message);
		
		return webSocketConnection.sendText(this.messageCodec.encode(messageToSend));
	}
	
  	private Object[] createArrayFromMessage(final Message message) {
		if (message instanceof SendingCallMessage sendingCallMessage) {

			return new Object[]{sendingCallMessage.getMessageType().getMessageTypeId(),
  					sendingCallMessage.getMessageId(), sendingCallMessage.getAction(), sendingCallMessage.getPayload()};
  		}

		if (message instanceof SendingCallResultMessage sendingCallResultMessage) {

			return new Object[]{sendingCallResultMessage.getMessageType().getMessageTypeId(),
  					sendingCallResultMessage.getMessageId(), sendingCallResultMessage.getPayload()};
  		}
  		
  		throw new IllegalStateException("Unknown message type: "+ message.getClass().getName());
  	}
}