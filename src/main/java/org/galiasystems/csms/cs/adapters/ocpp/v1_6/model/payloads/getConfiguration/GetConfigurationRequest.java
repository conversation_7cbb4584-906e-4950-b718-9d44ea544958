package org.galiasystems.csms.cs.adapters.ocpp.v1_6.model.payloads.getConfiguration;

import java.util.Collection;

import org.galiasystems.csms.cs.adapters.ocpp.payloads.CSMSRequest;

/**
 *  GetConfiguration.req PDU sent by the Central System to the Charge Point.
 *  
 *  @param key    	Optional. List of keys for which the configuration value is requested.
 *                  <p>
 *                  Field Type: CiString50Type (case-insensitive string[50])
 *                  Cardinality: 0..* 
 */
public record GetConfigurationRequest(Collection<String> key) implements CSMSRequest {

}
