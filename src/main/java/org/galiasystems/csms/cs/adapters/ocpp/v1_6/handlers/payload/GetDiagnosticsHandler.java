package org.galiasystems.csms.cs.adapters.ocpp.v1_6.handlers.payload;

import io.smallrye.mutiny.Uni;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import org.galiasystems.csms.cs.ChargingStationAdapterProxy;
import org.galiasystems.csms.cs.adapters.ocpp.handlers.payload.ResponsePayloadHandler;
import org.galiasystems.csms.cs.adapters.ocpp.v1_6.model.payloads.diagnostics.GetDiagnosticsRequest;
import org.galiasystems.csms.cs.adapters.ocpp.v1_6.model.payloads.diagnostics.GetDiagnosticsResponse;
import org.galiasystems.csms.management.types.GetDiagnosticsResult;

@Singleton
public class GetDiagnosticsHandler extends ResponsePayloadHandler<GetDiagnosticsRequest, GetDiagnosticsResponse> {

    @Inject
    private ChargingStationAdapterProxy chargingStationAdapterProxy;

    @Override
    protected Class<GetDiagnosticsRequest> getRequestPayloadType() {
        return GetDiagnosticsRequest.class;
    }

    @Override
    protected Class<GetDiagnosticsResponse> getResponsePayloadType() {
        return GetDiagnosticsResponse.class;
    }

    @Override
    protected Uni<Void> onMessage(final long chargingStationId, final String messageId, final GetDiagnosticsResponse responsePayload,
                                  final GetDiagnosticsRequest requestPayload) {

        final String fileName = responsePayload.fileName();


        final GetDiagnosticsResult getDiagnosticsResult = new GetDiagnosticsResult(fileName);

        return this.chargingStationAdapterProxy.getDiagnosticsResultReceived(chargingStationId, getDiagnosticsResult)
                .invoke((ignored) -> super.sendResponseMessage(chargingStationId, messageId, getDiagnosticsResult));
    }

}