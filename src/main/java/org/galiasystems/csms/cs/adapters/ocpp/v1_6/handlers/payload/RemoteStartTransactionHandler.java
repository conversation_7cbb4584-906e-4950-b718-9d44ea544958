package org.galiasystems.csms.cs.adapters.ocpp.v1_6.handlers.payload;

import io.smallrye.mutiny.Uni;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import org.galiasystems.csms.cs.ChargingStationAdapterProxy;
import org.galiasystems.csms.cs.adapters.ocpp.handlers.payload.ResponsePayloadHandler;
import org.galiasystems.csms.cs.adapters.ocpp.v1_6.model.payloads.remote.startTransaction.RemoteStartTransactionRequest;
import org.galiasystems.csms.cs.adapters.ocpp.v1_6.model.payloads.remote.startTransaction.RemoteStartTransactionResponse;
import org.galiasystems.csms.cs.adapters.ocpp.v1_6.utils.EnumConverter;
import org.galiasystems.csms.management.types.RequestStartTransactionResult;
import org.galiasystems.csms.management.types.enums.RequestStartStopTransactionStatus;

@Singleton
public class RemoteStartTransactionHandler extends ResponsePayloadHandler<RemoteStartTransactionRequest, RemoteStartTransactionResponse> {

    @Inject
    private ChargingStationAdapterProxy chargingStationAdapterProxy;

    @Override
    protected Class<RemoteStartTransactionRequest> getRequestPayloadType() {
        return RemoteStartTransactionRequest.class;
    }

    @Override
    protected Class<RemoteStartTransactionResponse> getResponsePayloadType() {
        return RemoteStartTransactionResponse.class;
    }

    @Override
    protected Uni<Void> onMessage(long chargingStationId, String messageId, RemoteStartTransactionResponse responsePayload, RemoteStartTransactionRequest requestPayload) {
        
    	final RequestStartStopTransactionStatus remoteStartStopStatus = EnumConverter.convertRemoteStartStopStatus(responsePayload.status());
        final RequestStartTransactionResult requestStartTransactionResult = new RequestStartTransactionResult(remoteStartStopStatus);
        
        return this.chargingStationAdapterProxy.requestStartTransactionResultReceived(chargingStationId, requestStartTransactionResult)
                .invoke((ignored) -> super.sendResponseMessage(chargingStationId, messageId, requestStartTransactionResult));

    }
}