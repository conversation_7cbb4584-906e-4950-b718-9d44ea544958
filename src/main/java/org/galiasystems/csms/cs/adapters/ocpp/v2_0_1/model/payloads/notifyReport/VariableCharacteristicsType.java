package org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.notifyReport;

import java.math.BigDecimal;

/**
 * Fixed read-only parameters of a variable.
 * 
 * @param unit               Optional. Unit of the variable. 
 *                           When the transmitted value has a unit, this field SHALL be included.
 *                           <p>
 *                           Field Type: string[0..16]
 *                           Cardinality: 0..1
 * 
 * @param dataType           Required. Data type of this variable.
 *                           <p>
 *                           Field Type: DataEnumType
 *                           Cardinality: 1..1
 * 
 * @param minLimit           Optional. Minimum possible value of this variable.
 *                           <p>
 *                           Field Type: decimal
 *                           Cardinality: 0..1
 * 
 * @param maxLimit           Optional. Maximum possible value of this variable. 
 *                           When the datatype of this Variable is String, OptionList, 
 *                           SequenceList or MemberList, this field defines the maximum length 
 *                           of the (CSV) string.
 *                           <p>
 *                           Field Type: decimal
 *                           Cardinality: 0..1
 * 
 * @param valuesList         Optional. Allowed values when variable is Option/Member/SequenceList.
 * 
 *                           * OptionList: The (Actual) Variable value must be a single value 
 *                           from the reported (CSV) enumeration list.
 *                            
 *                           * MemberList: The (Actual) Variable value may be an (unordered) 
 *                           (sub-)set of the reported (CSV) valid values list.
 *                           
 *                           * SequenceList: The (Actual) Variable value may be an ordered (priority, etc) 
 *                           (sub-)set of the reported (CSV) valid values.
 *                           
 *                           This is a comma separated list.
 *                           
 *                           The Configuration Variable ConfigurationValueSize can be used 
 *                           to limit SetVariableData.attributeValue and VariableCharacteristics.valueList. 
 *                           The max size of these values will always remain equal.
 *                           <p>
 *                           Field Type: string[0..1000]
 *                           Cardinality: 0..1
 * 
 * @param supportsMonitoring Required. Flag indicating if this variable supports monitoring.
 *                           <p>
 *                           Field Type: boolean
 *                           Cardinality: 1..1
 */
public record VariableCharacteristicsType(String unit, DataEnumType dataType, BigDecimal minLimit,
		BigDecimal maxLimit, String valuesList, boolean supportsMonitoring) {
}