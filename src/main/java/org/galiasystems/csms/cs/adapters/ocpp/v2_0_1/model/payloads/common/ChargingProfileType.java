package org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.common;

import java.time.ZonedDateTime;

/**
 * A ChargingProfile consists of ChargingSchedule, describing the amount of power or current that can be delivered per time interval.
 * 
 * @param id	 					Required. Id of ChargingProfile.
 *                        	 		<p>
 *                        	 		Field Type: integer
 *                        	 		Cardinality: 1..1
 *                        
 * @param stackLevel	            Required. Value determining level in hierarchy stack of profiles. 
 * 									Higher values have precedence over lower values. 
 * 									Lowest level is 0.
 *                        	 		<p>
 *                        	 		Field Type: integer
 *                        	 		Cardinality: 1..1  
 *                        
 * @param chargingProfilePurpose	Required. Defines the purpose of the schedule transferred by this profile.
 *                        	 		<p>
 *                        	 		Field Type: ChargingProfilePurposeEnumType
 *                        	 		Cardinality: 1..1
 *                        
 * @param chargingProfileKind		Required. Indicates the kind of schedule.
 *                        	 		<p>
 *                        	 		Field Type: ChargingProfileKindEnumType
 *                        	 		Cardinality: 1..1
 *                        
 * @param recurrencyKind			Optional. Indicates the start point of a recurrence.
 *                        	 		<p>
 *                        	 		Field Type: RecurrencyKindEnumType
 *                        	 		Cardinality: 0..1
 *                        
 * @param validFrom					Optional. Point in time at which the profile starts to be valid. 
 * 									If absent, the profile is valid as soon as it is received by the Charging Station.
 *                        	 		<p>
 *                        	 		Field Type: dateTime
 *                        	 		Cardinality: 0..1
 *                        
 * @param validTo					Optional. Point in time at which the profile stops to be valid. 
 * 									If absent, the profile is valid until it is replaced by another profile.
 *                        	 		<p>
 *                        	 		Field Type: dateTime
 *                        	 		Cardinality: 0..1
 *                        
 * @param transactionId				Optional. SHALL only be included when ChargingProfilePurpose is set 
 * 									to TxProfile in a SetChargingProfileRequest. 
 * 									The transactionId is used to match the profile to a specific transaction.
 *                        	 		<p>
 *                        	 		Field Type: identifierString[0..36] 
 *                        	 		Cardinality: 0..1
 *                        
 * @param chargingSchedule			Required. Schedule that contains limits for the available power or current over time. 
 * 									In order to support ISO 15118 schedule negotiation, it supports at most three schedules 
 * 									with associated tariff to choose from. 
 * 									Having multiple chargingSchedules is only allowed for charging profiles 
 * 									of purpose TxProfile in the context of an ISO 15118 charging session.
 *                        	 		<p>
 *                        	 		Field Type: ChargingScheduleType
 *                        	 		Cardinality: 1..3
 */
public record ChargingProfileType(int id, int stackLevel, ChargingProfilePurposeEnumType chargingProfilePurpose,
		ChargingProfileKindEnumType chargingProfileKind, RecurrencyKindEnumType recurrencyKind, 
		ZonedDateTime validFrom, ZonedDateTime validTo, String transactionId, ChargingScheduleType  chargingSchedule) {
}