package org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.common;

import com.fasterxml.jackson.annotation.JsonValue;

/**
 * Allowable values of the optional "measurand" field of a Value element, 
 * as used in MeterValuesRequest and TransactionEventRequest with eventTypes Started, Ended and Updated. 
 * Default value of "measurand" is always "Energy.Active.Import.Register".
 *
 * Note 1: Two measurands (Current.Offered and Power.Offered) are available 
 * that are strictly speaking no measured values. 
 * They indicate the maximum amount of current/power that is being offered to the EV 
 * and are intended for use in smart charging applications.
 *
 * Note 2: Import is energy flow from the Grid to the Charging Station, EV or other load. 
 * Export is energy flow from the EV to the Charging Station and/or from the Charging Station to the Grid. 
 * Except in the case of a meter replacement, all "Register" values relating to a single charging transaction, 
 * or a non-transactional consumer (e.g. Charging Station internal power supply, overall supply) MUST be monotonically increasing in time.
 *
 * Note 3: The actual quantity of energy corresponding to a reported ".Register" value is computed 
 * as the register value in question minus the register value recorded/reported at the start of the transaction 
 * or other relevant starting reference point in time. 
 * For improved auditability, ".Register" values SHOULD be reported exactly as they are directly read 
 * from a non-volatile register in the electrical metering hardware, and SHOULD NOT be re-based to zero at the start of transactions. 
 * This allows any "missing energy" between sequential transactions, due to hardware fault, meter replacement, mis-wiring, 
 * fraud, etc. to be identified, by allowing the CSMS to confirm that the starting register value of any transaction 
 * is identical to the finishing register value of the preceding transaction on the same connector.
 *
 */
public enum MeasurandEnumType {
	
	/**
	 * Instantaneous current flow from EV.
	 */
	Current_Export("Current.Export"),
	
	/**
	 * Instantaneous current flow to EV.
	 */
	Current_Import("Current.Import"),
	
	/**
	 * Maximum current offered to EV.
	 */
	Current_Offered("Current.Offered"),
	
	/**
	 * Numerical value read from the "active electrical energy" (Wh or kWh) 
	 * register of the (most authoritative) electrical meter measuring energy exported (to the grid).
	 */
	Energy_Active_Export_Register("Energy.Active.Export.Register"),
	
	/**
	 * Numerical value read from the "active electrical energy" (Wh or kWh) 
	 * register of the (most authoritative) electrical meter measuring energy imported (from the grid supply).
	 */
	Energy_Active_Import_Register("Energy.Active.Import.Register"),
	
	/**
	 * Numerical value read from the "reactive electrical energy" (varh or kvarh) 
	 * register of the (most authoritative) electrical meter measuring energy exported (to the grid).
	 */
	Energy_Reactive_Export_Register("Energy.Reactive.Export.Register"),
	
	/**
	 * Numerical value read from the "reactive electrical energy" (varh or kvarh) 
	 * register of the (most authoritative) electrical meter measuring energy imported (from the grid supply).
	 */
	Energy_Reactive_Import_Register("Energy.Reactive.Import.Register"),
	
	/**
	 * Absolute amount of "active electrical energy" (Wh or kWh) 
	 * exported (to the grid) during an associated time "interval", specified by a Metervalues ReadingContext, 
	 * and applicable interval duration configuration values (in seconds) for ClockAlignedDataInterval and TxnMeterValueSampleInterval.
	 */
	Energy_Active_Export_Interval("Energy.Active.Export.Interval"),
	
	/**
	 * Absolute amount of "active electrical energy" (Wh or kWh) 
	 * imported (from the grid supply) during an associated time "interval", specified by a Metervalues ReadingContext, 
	 * and applicable interval duration configuration values (in seconds) for ClockAlignedDataInterval and TxnMeterValueSampleInterval.
	 */
	Energy_Active_Import_Interval("Energy.Active.Import.Interval"),
	
	/**
	 * Numerical value read from the “net active electrical energy" (Wh or kWh) register.
	 */
	Energy_Active_Net("Energy.Active.Net"),
	
	/**
	 * Absolute amount of "reactive electrical energy" (varh or kvarh) 
	 * exported (to the grid) during an associated time "interval", specified by a Metervalues ReadingContext, 
	 * and applicable interval duration configuration values (in seconds) for ClockAlignedDataInterval and TxnMeterValueSampleInterval.
	 */
	Energy_Reactive_Export_Interval("Energy.Reactive.Export.Interval"),
	
	/**
	 * Absolute amount of "reactive electrical energy" (varh or kvarh) 
	 * imported (from the grid supply) during an associated time "interval", specified by a Metervalues ReadingContext, 
	 * and applicable interval duration configuration values (in seconds) for ClockAlignedDataInterval and TxnMeterValueSampleInterval.
	 */
	Energy_Reactive_Import_Interval("Energy.Reactive.Import.Interval"),
	
	/**
	 * Numerical value read from the “net reactive electrical energy" (varh or kvarh) register.
	 */
	Energy_Reactive_Net("Energy.Reactive.Net"),
	
	/**
	 * Numerical value read from the "apparent electrical energy" (VAh or kVAh) register.
	 */
	Energy_Apparent_Net("Energy.Apparent.Net"),
	
	/**
	 * Numerical value read from the "apparent electrical import energy" (VAh or kVAh) register.
	 */
	Energy_Apparent_Import("Energy.Apparent.Import"),
	
	/**
	 * Numerical value read from the "apparent electrical export energy" (VAh or kVAh) register.
	 */
	Energy_Apparent_Export("Energy.Apparent.Export"),
	
	/**
	 * Instantaneous reading of powerline frequency.
	 */
	Frequency("Frequency"),
	
	/**
	 * Instantaneous active power exported by EV. (W or kW)
	 */
	Power_Active_Export("Power.Active.Export"),
	
	/**
	 * Instantaneous active power imported by EV. (W or kW)
	 */
	Power_Active_Import("Power.Active.Import"),
	
	/**
	 * Instantaneous power factor of total energy flow.
	 */
	Power_Factor("Power.Factor"),
	
	/**
	 * Maximum power offered to EV.
	 */
	Power_Offered("Power.Offered"),
	
	/**
	 * Instantaneous reactive power exported by EV. (var or kvar)
	 */
	Power_Reactive_Export("Power.Reactive.Export"),
	
	/**
	 * Instantaneous reactive power imported by EV. (var or kvar)
	 */
	Power_Reactive_Import("Power.Reactive.Import"),
	
	/**
	 * State of charge of charging vehicle in percentage.
	 */
	SoC("SoC"),
	
	/**
	 * Instantaneous DC or AC RMS supply voltage.
	 * For location = Inlet and evseId = 0: voltage at charging station grid connection. 
	 * For location = Outlet and evseId > 0: voltage at EVSE outlet towards the EV.
	 */
	Voltage("Voltage"),
	;

	private String measurandType;
	
	private MeasurandEnumType(final String measurandType) {
		this.measurandType = measurandType;
	}
	
	@JsonValue
	public String getMeasurandType() {
		return measurandType;
	}
}