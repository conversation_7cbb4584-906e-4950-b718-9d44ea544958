package org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.common;

/**
 * Kind of the Cost in CostType. 
 */
public enum CostKindEnumType {

	/**
	 * Absolute value. Carbon Dioxide emissions, in grams per kWh.
	 */
	CarbonDioxideEmission,
	
	/**
	 * Relative value. 
	 * Price per kWh, as percentage relative to the maximum price stated 
	 * in any of all tariffs indicated to the EV.
	 */
	RelativePricePercentage,
	
	/**
	 * Relative value. Percentage of renewable generation within total generation.
	 */
	RenewableGenerationPercentage,
	;
}
