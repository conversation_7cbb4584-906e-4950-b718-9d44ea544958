package org.galiasystems.csms.cs.adapters.ocpp.v1_6.model.payloads.startTransaction;

import org.galiasystems.csms.cs.adapters.ocpp.payloads.CSRequest;

import java.time.ZonedDateTime;

/**
 * StartTransaction.req PDU sent by the Charge Point to the Central System.
 *
 * @param connectorId   Required. This identifies which connector of the Charge Point is used.
 *                      Field type:   integer
 *                      Cardinality:  1..1
 *                      Constraints:  connectorId > 0
 * @param idTag         Required. This contains the identifier for which a transaction has to be started.
 *                      Field type:   IdToken
 *                      Cardinality:  1..1
 * @param meterStart    Required. This contains the meter value in Wh for the connector at the start of the transaction.
 *                      Field type:   integer
 *                      Cardinality:  1..1
 * @param reservationId Optional. This contains the id of the reservation that terminates as a result of this transaction.
 *                      Field type:   integer
 *                      Cardinality:  0..1
 * @param timestamp     Required. This contains the date and time on which the transaction is started.
 *                      Field type:   dateTime
 *                      Cardinality:  1..1
 */
public record StartTransactionRequest(int connectorId, String idTag, int meterStart,
                                      Integer reservationId, ZonedDateTime timestamp) implements CSRequest {
}