package org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.common;

import java.math.BigDecimal;

/**
 * Charging schedule period structure defines a time period in a charging schedule.
 * 
 * @param startPeriod	 			Required. Start of the period, in seconds from the start of schedule. 
 * 									The value of StartPeriod also defines the stop time of the previous period.
 *                        	 		<p>
 *                        	 		Field Type: integer
 *                        	 		Cardinality: 1..1
 *                        
 * @param limit	 					Required. Charging rate limit during the schedule period,
 * 									in the applicable chargingRateUnit, for example in Amperes (A) or Watts (W). 
 * 									Accepts at most one digit fraction (e.g. 8.1).
 *                        	 		<p>
 *                        	 		Field Type: decimal
 *                        	 		Cardinality: 1..1
 *                        
 * @param numberPhases	 			Optional. The number of phases that can be used for charging.
 * 									For a DC EVSE this field should be omitted.
 * 									For an AC EVSE a default value of numberPhases = 3 will be assumed 
 * 									if the field is absent.
 *                        	 		<p>
 *                        	 		Field Type: integer
 *                        	 		Cardinality: 0..1
 *                        
 * @param phaseToUse	 			Optional. Values: 1..3, Used if numberPhases=1 and if the EVSE is capable 
 * 									of switching the phase connected to the EV, i.e. 
 * 									ACPhaseSwitchingSupported is defined and true.
 * 									It’s not allowed unless both conditions above are true. 
 * 									If both conditions are true, and phaseToUse is omitted, 
 * 									the Charging Station / EVSE will make the selection on its own.
 *                        	 		<p>
 *                        	 		Field Type: integer
 *                        	 		Cardinality: 0..1
 */
public record ChargingSchedulePeriodType(int startPeriod, BigDecimal limit, Integer numberPhases, 
		Integer phaseToUse) {
}