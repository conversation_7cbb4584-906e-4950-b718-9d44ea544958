package org.galiasystems.csms.cs.adapters.ocpp.v1_6.model.payloads.common;

/**
 * Single sampled value in MeterValues. Each value can be accompanied by optional fields.
 *
 * @param value    		Required. Value as a “Raw” (decimal) number or “SignedData”. 
 * 						Field Type is “string” to allow for digitally signed data readings. 
 * 						Decimal numeric values are also acceptable to allow fractional values for measurands 
 * 						such as Temperature and Current.
 *                     	Field type: String
 *                     	Cardinality: 1..1
 *                     
 * @param context 		Optional. Type of detail value: start, end or sample. 
 * 						Default = “Sample.Periodic”.
 *                     	Field type: ReadingContext
 *                     	Cardinality: 0..1
 *                     
 * @param format 		Optional. Raw or signed data. 
 * 						Default = “Raw”.
 *                     	Field type: ValueFormat
 *                     	Cardinality: 0..1
 *                     
 * @param measurand 	Optional. Type of measurement. 
 * 						Default = “Energy.Active.Import.Register”.
 *                     	Field type: Measurand
 *                     	Cardinality: 0..1
 *                     
 * @param phase 		Optional. indicates how the measured value is to be interpreted. 
 * 						For instance between L1 and neutral (L1-N) Please 
 * 						note that not all values of phase are applicable to all Measurands. 
 * 						When phase is absent, the measured value is interpreted as an overall value.
 *                     	Field type: Phase
 *                     	Cardinality: 0..1
 *                     
 * @param location 		Optional. Location of measurement. 
 * 						Default=”Outlet”.
 *                     	Field type: Location
 *                     	Cardinality: 0..1
 *                     
 * @param unit 			Optional. Unit of the value. Default = “Wh” if the (default) measurand is an “Energy” type.
 *                     	Field type: UnitOfMeasure
 *                     	Cardinality: 0..1
 */
public record SampledValue(String value, ReadingContext context, ValueFormat format, Measurand measurand, Phase phase,
                           Location location, UnitOfMeasure unit) {
}