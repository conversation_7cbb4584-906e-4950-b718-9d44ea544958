package org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.handlers.payload;

import io.smallrye.mutiny.Uni;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import org.galiasystems.csms.cs.ChargingStationAdapterProxy;
import org.galiasystems.csms.cs.adapters.ocpp.handlers.payload.RequestPayloadHandler;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.authorize.AuthorizeRequest;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.authorize.AuthorizeResponse;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.authorize.AuthorizeCertificateStatusEnumType;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.common.AuthorizationStatusEnumType;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.common.IdTokenInfoType;

import java.time.ZonedDateTime;
import java.util.List;

@Singleton
public class AuthorizeHandler extends RequestPayloadHandler<AuthorizeRequest, AuthorizeResponse> {

    @Inject
    private ChargingStationAdapterProxy chargingStationAdapterProxy;

    @Override
    protected Class<AuthorizeRequest> getRequestPayloadType() {
        return AuthorizeRequest.class;
    }

    @Override
    protected Uni<AuthorizeResponse> onMessage(final long chargingStationId, final String messageId,
                                               final AuthorizeRequest requestPayload) {

        // TODO: implement relevant authorization logic including certificate validation
        IdTokenInfoType idTokenInfo = new IdTokenInfoType(
                AuthorizationStatusEnumType.Accepted,
                ZonedDateTime.now().plusDays(1L),
                1,
                null,
                List.of(1),
                null,
                requestPayload.idToken(),
                null
        );

        AuthorizeCertificateStatusEnumType certificateStatus = null;
        if (requestPayload.certificate() != null || requestPayload.iso15118CertificateHashData() != null) {
            // TODO: implement certificate validation logic
            certificateStatus = AuthorizeCertificateStatusEnumType.Accepted;
        }

        return Uni.createFrom().item(
                new AuthorizeResponse(certificateStatus, idTokenInfo)
        );
    }
}