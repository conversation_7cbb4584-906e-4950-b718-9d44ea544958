package org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.authorize;

import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.common.HashAlgorithmEnumType;

/**
 * @param hashAlgorithm  Required. Used algorithms for the hashes provided.
 *                       Field type: HashAlgorithmEnumType
 *                       Cardinality: 1..1
 * @param issuerNameHash Required. The hash of the issuer’s distinguished name
 *                       (DN), that must be calculated over the DER encoding of
 *                       the issuer’s name field in the certificate being checked.
 *                       Field type:  identifierString[0..128]
 *                       Cardinality: 1..1
 * @param issuerKeyHash  Required. The hash of the DER encoded public key: the
 *                       value (excluding tag and length) of the subject public key
 *                       field in the issuer’s certificate.
 *                       Field type:  string[0..128]
 *                       Cardinality: 1..1
 * @param serialNumber   Required. The string representation of the hexadecimal
 *                       value of the serial number without the prefix "0x" and
 *                       without leading zeroes.
 *                       Field type:  identifierString[0..40]
 *                       Cardinality: 1..1
 * @param responderURL   Required. This contains the responder URL (Case
 *                       insensitive).
 *                       Field type: string[0..512]
 *                       Cardinality: 1..1
 */
public record OCSPRequestDataType(HashAlgorithmEnumType hashAlgorithm, String issuerNameHash, String issuerKeyHash,
                                  String serialNumber, String responderURL) {
}