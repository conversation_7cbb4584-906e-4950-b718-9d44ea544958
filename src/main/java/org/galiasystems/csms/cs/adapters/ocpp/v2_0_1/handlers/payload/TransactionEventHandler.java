package org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.handlers.payload;

import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Collection;
import java.util.Date;

import org.galiasystems.csms.cs.ChargingStationAdapterProxy;
import org.galiasystems.csms.cs.adapters.ocpp.handlers.payload.RequestPayloadHandler;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.common.AdditionalInfoType;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.common.AuthorizationStatusEnumType;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.common.EVSEType;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.common.IdTokenEnumType;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.common.IdTokenInfoType;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.common.IdTokenType;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.common.MessageContentType;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.common.MessageFormatEnumType;

import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.transactionEvent.TransactionEventRequest;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.transactionEvent.TransactionEventResponse;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.transactionEvent.TransactionType;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.utils.EnumConverter;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.utils.MeterValuesConverter;
import org.galiasystems.csms.management.model.AdditionalIdToken;
import org.galiasystems.csms.management.model.EvseTransactionEvent;
import org.galiasystems.csms.management.model.IdToken;
import org.galiasystems.csms.management.model.MeterValue;
import org.galiasystems.csms.management.model.enums.*;
import org.galiasystems.csms.management.types.IdTokenInfo;
import org.galiasystems.csms.management.types.Message;
import org.galiasystems.csms.management.types.TransactionEventResult;

import io.smallrye.mutiny.Uni;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;

@Singleton
public class TransactionEventHandler extends RequestPayloadHandler<TransactionEventRequest, TransactionEventResponse> {

	@Inject
	private ChargingStationAdapterProxy chargingStationAdapterProxy;
	
	@Override
	protected Class<TransactionEventRequest> getRequestPayloadType() {
		return TransactionEventRequest.class;
	}

	@Override
	protected Uni<TransactionEventResponse> onMessage(long chargingStationId, String messageId,
			TransactionEventRequest requestPayload) {
		
		final EVSEType evse = requestPayload.evse();
		final Integer chargingStationEvseId; 
		final Integer evseConnectorId;
		if (evse != null) {
			chargingStationEvseId = evse.id();
			evseConnectorId = evse.connectorId();
		} else {
			chargingStationEvseId = null;
			evseConnectorId = null;
		}
		
		final String evseTransactionId = requestPayload.transactionInfo().transactionId();
		
		final EvseTransactionEvent transactionEvent = this.createTransactionEvent(requestPayload);
		
		final Uni<TransactionEventResult> result = chargingStationAdapterProxy.transactionEventReceived(chargingStationId, chargingStationEvseId, evseConnectorId, 
				evseTransactionId, transactionEvent);
		
		return result
				.onItem()
				.transform(this::createTransactionEventResponse);
	}

	private EvseTransactionEvent createTransactionEvent(final TransactionEventRequest transactionEventRequest) {
		
		final TransactionEventType eventType = EnumConverter.convertTransactionEventEnumType(transactionEventRequest.eventType());
		final Date timestamp = Date.from(transactionEventRequest.timestamp().toInstant());
		final TransactionEventTriggerReason triggerReason = EnumConverter.convertTriggerReasonEnumType(transactionEventRequest.triggerReason());
		
		final TransactionType transactionInfo = transactionEventRequest.transactionInfo();
		final TransactionChargingState chargingState = EnumConverter.convertChargingStateEnumType(transactionInfo.chargingState());
		final TransactionReason stoppedReason = EnumConverter.convertReasonEnumType(transactionInfo.stoppedReason());
				
		final IdToken idToken = this.createIdToken(transactionEventRequest.idToken());
		
		final Collection<MeterValue> meterValue = MeterValuesConverter.createMeterValues(transactionEventRequest.meterValue());
		
		return new EvseTransactionEvent(null, eventType, timestamp, triggerReason, transactionEventRequest.seqNo(),
				transactionEventRequest.offline(), transactionEventRequest.numberOfPhasesUsed(), transactionEventRequest.cableMaxCurrent(),
				transactionEventRequest.reservationId(), chargingState, transactionInfo.timeSpentCharging(), stoppedReason,
				transactionInfo.remoteStartId(), idToken, meterValue);
	}
	
	private IdToken createIdToken(final IdTokenType idToken) {
		
		if(idToken == null) {
			return null;
		}
		
		final org.galiasystems.csms.management.model.enums.IdTokenType type = EnumConverter.convertIdTokenEnumType(idToken.type());
		
		Collection<AdditionalIdToken> additionalIdTokens = createAdditionalIdTokens(idToken.additionalInfo());

		return new IdToken(idToken.idToken(), type, additionalIdTokens);
	}
	
	private Collection<AdditionalIdToken> createAdditionalIdTokens(Collection<AdditionalInfoType> additionalInfos) {
		
		if(additionalInfos == null) {
			return null;
		}
		
		return additionalInfos.stream().map((additionalInfo) 
				-> new AdditionalIdToken(additionalInfo.additionalIdToken(), additionalInfo.type())).toList();
	}

	private TransactionEventResponse createTransactionEventResponse(final TransactionEventResult transactionEventResult) {
		
		final IdTokenInfoType idTokenInfo = this.createIdTokenInfoType(transactionEventResult.idTokenInfo());
		final MessageContentType updatedPersonalMessage = this.createMessageContentType(transactionEventResult.message());
		
		return new TransactionEventResponse(transactionEventResult.totalCost(), transactionEventResult.chargingPriority(), 
				idTokenInfo, updatedPersonalMessage);
	}

	private IdTokenInfoType createIdTokenInfoType(final IdTokenInfo idTokenInfo) {
		
		if(idTokenInfo == null) {
			return null;
		}
		
		final AuthorizationStatusEnumType status = EnumConverter.convertAuthorizationStatus(idTokenInfo.status());
		
		final Date cacheExpiryDateTimeAsDate = idTokenInfo.cacheExpiryDateTime();
		final ZonedDateTime cacheExpiryDateTime;
		if (cacheExpiryDateTimeAsDate != null) {
			cacheExpiryDateTime = ZonedDateTime.ofInstant(cacheExpiryDateTimeAsDate.toInstant(),
	                ZoneId.systemDefault());
		} else {
			cacheExpiryDateTime = null;
		}
		
		final IdTokenType groupIdToken = this.createIdTokenType(idTokenInfo.idToken());
		final MessageContentType personalMessage = this.createMessageContentType(idTokenInfo.message());
		
		return new IdTokenInfoType(status, cacheExpiryDateTime, idTokenInfo.chargingPriority(), idTokenInfo.language1(),
				idTokenInfo.evseIds(), idTokenInfo.language2(), groupIdToken, personalMessage);
	}

	private IdTokenType createIdTokenType(final IdToken idToken) {
		
		if(idToken == null) {
			return null;
		}
		
		final IdTokenEnumType type = EnumConverter.convertIdTokenType(idToken.type());
		final Collection<AdditionalInfoType> additionalInfo = this.createAdditionalInfoTypes(idToken.additionalIdTokens());
		
		return new IdTokenType(idToken.idToken(), type, additionalInfo);
	}

	private Collection<AdditionalInfoType> createAdditionalInfoTypes(Collection<AdditionalIdToken> additionalIdTokens) {
		
		if (additionalIdTokens == null) {
			return null;
		}
		
		return additionalIdTokens.stream().map((additionalIdToken) ->
			new AdditionalInfoType(additionalIdToken.idToken(), additionalIdToken.type())
		).toList();		
	}

	private MessageContentType createMessageContentType(final Message message) {
		
		if(message == null) {
			return null;
		}
		
		final MessageFormatEnumType format = EnumConverter.convertMessageFormat(message.format());
		
		return new MessageContentType(format, message.language(), message.content());
	}
}