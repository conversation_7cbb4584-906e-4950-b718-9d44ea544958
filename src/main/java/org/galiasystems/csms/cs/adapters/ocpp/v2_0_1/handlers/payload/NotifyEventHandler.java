package org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.handlers.payload;

import java.util.List;

import org.galiasystems.csms.cs.ChargingStationAdapterProxy;
import org.galiasystems.csms.cs.adapters.ocpp.handlers.payload.RequestPayloadHandler;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.common.ComponentName;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.common.ComponentType;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.common.EVSEType;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.common.VariableName;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.notifyEvent.EventDataType;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.notifyEvent.EventTriggerEnumType;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.notifyEvent.NotifyEventRequest;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.notifyEvent.NotifyEventResponse;
import org.galiasystems.csms.management.model.enums.ChargingStationStatus;
import org.galiasystems.csms.management.model.enums.ConnectorStatus;
import org.galiasystems.csms.management.model.enums.EvseStatus;
import org.jboss.logging.Logger.Level;

import io.smallrye.mutiny.Uni;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;

@Singleton
public class NotifyEventHandler extends RequestPayloadHandler<NotifyEventRequest, NotifyEventResponse> {

	@Inject
	private ChargingStationAdapterProxy chargingStationAdapterProxy;
	
	@Override
	protected Class<NotifyEventRequest> getRequestPayloadType() {
		return NotifyEventRequest.class;
	}

	@Override
	protected Uni<NotifyEventResponse> onMessage(final long chargingStationId, final String messageId,
			final NotifyEventRequest requestPayload) {
		
		final List<Uni<Void>> handleUnis = requestPayload.eventData().stream()
				.map((eventDataType) -> this.handleEventData(chargingStationId, eventDataType))
				.toList();

		return Uni.combine().all().unis(handleUnis)
			.usingConcurrencyOf(1)
			.collectFailures()
			.with((ignored) -> new NotifyEventResponse());
	}
	
	
	private Uni<Void> handleEventData(final long chargingStationId, final EventDataType eventDataType) {
		final EventTriggerEnumType triggerType = eventDataType.trigger();
		
		switch(triggerType) {
			case EventTriggerEnumType.Alerting:
				return handleAlerting(chargingStationId, eventDataType);
			case EventTriggerEnumType.Delta:
				return handleDelta(chargingStationId, eventDataType);
			case EventTriggerEnumType.Periodic:
				return handlePeriodic(chargingStationId, eventDataType);
			default: {
				throw new IllegalStateException("Invalid EventTriggerEnumType type: " + triggerType.name());
			}
				
		}
	}
	
	private Uni<Void> handleAlerting(final long chargingStationId, final EventDataType eventDataType) {
		if (this.log.isEnabled(Level.WARN)) {
			this.log.warn("Unhandled Alerting notify event: " + eventDataType.toString());
		}
		
		return Uni.createFrom().voidItem();
	}
	
	private Uni<Void> handleDelta(final long chargingStationId, final EventDataType eventDataType) {
		
		final ComponentType component = eventDataType.component();
		final EVSEType eVSE = component.evse();
		
		final String componentName = component.name();
		final String variableName = eventDataType.variable().name();

		if (ComponentName.ChargingStation.isOf(componentName)) {
			if (VariableName.AvailabilityState.isOf(variableName)) {
			
				final ChargingStationStatus chargingStationStatus = ChargingStationStatus.valueOf(eventDataType.actualValue());
				return this.chargingStationAdapterProxy.chargingStationStatusReceived(chargingStationId, chargingStationStatus,
						eventDataType.timestamp(), eventDataType.techCode(), eventDataType.techInfo(), null, null);
			}	
		} else if (ComponentName.EVSE.isOf(componentName)) {
			if (VariableName.AvailabilityState.isOf(variableName)) {
				
				final EvseStatus evseStatus = EvseStatus.valueOf(eventDataType.actualValue());
				return this.chargingStationAdapterProxy.evseStatusReceived(chargingStationId, eVSE.id(), evseStatus);
			}	
		} else if (ComponentName.Connector.isOf(componentName)) {
			if (VariableName.AvailabilityState.isOf(variableName)) {
				
				final ConnectorStatus connectorStatus = ConnectorStatus.valueOf(eventDataType.actualValue());
				return this.chargingStationAdapterProxy.connectorStatusReceived(chargingStationId, eVSE.id(), eVSE.connectorId(), connectorStatus,
						eventDataType.timestamp(), eventDataType.techCode(), eventDataType.techInfo(), null, null);
			}
		}
		
		if (this.log.isEnabled(Level.WARN)) {
			this.log.warn("Unhandled Delta notify event: " + eventDataType.toString());
		}
		return Uni.createFrom().voidItem();
	}

	private Uni<Void> handlePeriodic(final long chargingStationId, final EventDataType eventDataType) {
		if (this.log.isEnabled(Level.WARN)) {
			this.log.warn("Unhandled Periodic notify event: " + eventDataType.toString());
		}
		
		return Uni.createFrom().voidItem();
	}
}