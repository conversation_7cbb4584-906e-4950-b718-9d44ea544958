package org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.handlers.payload;

import org.galiasystems.csms.cs.ChargingStationAdapterProxy;
import org.galiasystems.csms.cs.adapters.ocpp.handlers.payload.ResponsePayloadHandler;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.requestStopTransaction.RequestStopTransactionRequest;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.requestStopTransaction.RequestStopTransactionResponse;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.utils.EnumConverter;
import org.galiasystems.csms.management.types.RequestStopTransactionResult;
import org.galiasystems.csms.management.types.enums.RequestStartStopTransactionStatus;

import io.smallrye.mutiny.Uni;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;

@Singleton
public class RequestStopTransactionHandler extends ResponsePayloadHandler<RequestStopTransactionRequest, RequestStopTransactionResponse> {

	@Inject
	private ChargingStationAdapterProxy chargingStationAdapterProxy;
	
	@Override
	protected Class<RequestStopTransactionRequest> getRequestPayloadType() {
		return RequestStopTransactionRequest.class;
	}

	@Override
	protected Class<RequestStopTransactionResponse> getResponsePayloadType() {
		return RequestStopTransactionResponse.class;
	}

	@Override
	protected Uni<Void> onMessage(final long chargingStationId, final String messageId, 
			final RequestStopTransactionResponse responsePayload, final RequestStopTransactionRequest requestPayload) {
		
		final RequestStartStopTransactionStatus status = EnumConverter.convertRequestStartStopStatusEnumType(responsePayload.status());
        final RequestStopTransactionResult requestStopTransactionResult = new RequestStopTransactionResult(status);
		
		return this.chargingStationAdapterProxy.requestStopTransactionResultReceived(chargingStationId, requestStopTransactionResult)
                .invoke((ignored) -> super.sendResponseMessage(chargingStationId, messageId, requestStopTransactionResult));
	}
}