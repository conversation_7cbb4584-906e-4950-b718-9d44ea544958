package org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.transactionEvent;

import java.time.ZonedDateTime;
import java.util.Collection;

import org.galiasystems.csms.cs.adapters.ocpp.payloads.CSRequest;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.common.EVSEType;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.common.IdTokenType;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.common.MeterValueType;

/**
 * TransactionEventRequest PDU sent by the Charging Station to the CSMS. 
 * For each of the eventTypes; Started, Updated and Ended, the corresponding cardinality is specified.
 * 
 * @param eventType       	  Required. This contains the type of this event. 
 * 						  	  The first TransactionEvent of a transaction SHALL contain: "Started" 
 * 						  	  The last TransactionEvent of a transaction SHALL contain: "Ended" 
 * 						  	  All others SHALL contain: "Updated"
 * 						  	  <p>
 * 						  	  Field Type: TransactionEventEnumType
 * 						  	  Cardinality: 1..1
 *                        
 * @param timestamp       	  Required. The date and time at which this transaction event occurred.
 * 						  	  <p>
 * 						  	  Field Type: dateTime
 * 						  	  Cardinality: 1..1
 *                        
 * @param triggerReason   	  Required. Reason the Charging Station sends this message to the CSMS.
 * 						  	  <p>
 * 						  	  Field Type: TriggerReasonEnumType
 * 						  	  Cardinality: 1..1
 *                        
 * @param seqNo    		  	  Required. Incremental sequence number, 
 * 						  	  helps with determining if all messages of a transaction have been received.
 * 						  	  <p>
 * 						  	  Field Type: integer
 * 						  	  Cardinality: 1..1
 *                        
 * @param offline    	  	  Optional. Indication that this transaction event happened when the Charging Station was offline. 
 * 						  	  Default = false, meaning: the event occurred when the Charging Station was online.
 *                        	  <p>
 *                        	  Field Type: boolean
 *                        	  Cardinality: 0..1
 *                        
 * @param numberOfPhasesUsed  Optional. If the Charging Station is able to report the number of phases used, 
 * 							  then it SHALL provide it. When omitted the CSMS may be able to determine 
 * 							  the number of phases used via device management.
 *                            <p>
 *                            Field Type: integer
 *                            Cardinality: 0..1
 *                            
 * @param cableMaxCurrent  	  Optional. The maximum current of the connected cable in Ampere (A).
 *                            <p>
 *                            Field Type: integer
 *                            Cardinality: 0..1
 *                            
 * @param reservationId  	  Optional. This contains the Id of the reservation that terminates as a result of this transaction.
 *                            <p>
 *                            Field Type: integer
 *                            Cardinality: 0..1
 *                            
 * @param transactionInfo  	  Required. Contains transaction specific information.
 *                            <p>
 *                            Field Type: TransactionType
 *                            Cardinality: 1..1
 *                            
 * @param idToken  	  		  Optional. This contains the identifier for which a transaction is (or will be) started or stopped. 
 * 							  Is required when the EV Driver becomes authorized for this transaction 
 * 							  and when the EV Driver ends authorization. 
 * 							  The IdToken should only be sent once in a TransactionEventRequest 
 * 							  for every authorization (for starting or for stopping) done for this transaction, 
 * 							  so that CSMS can return the idTokenInfo in the TransactionEventResponse. 
 * 							  idToken should not be present in the TransactionEventRequest 
 * 							  when a transaction is ended by a RequestStopTransactionRequest or a ResetRequest.
 *                            <p>
 *                            Field Type: IdTokenType
 *                            Cardinality: 0..1 
 *                            
 * @param evse  	  		  Optional. This identifies which evse (and connector) of the Charging Station is used.
 *                            <p>
 *                            Field Type: EVSEType
 *                            Cardinality: 0..1
 *                            
 * @param meterValue  	  	  Optional. This contains the relevant meter values. 
 * 							  Depending on the EventType of this TransactionEvent the following Configuration Variable 
 * 							  is used to configure the content:
 * 							  Started: SampledDataTxStartedMeasurands
 * 							  Updated: SampledDataTxUpdatedMeasurands 
 * 							  Ended: SampledDataTxEndedMeasurands & AlignedDataTxEndedMeasurands
 *                            <p>
 *                            Field Type: MeterValueType
 *                            Cardinality: 0..*
 */
public record TransactionEventRequest(TransactionEventEnumType eventType, ZonedDateTime timestamp, TriggerReasonEnumType triggerReason,
		int seqNo, Boolean offline, Integer numberOfPhasesUsed, Integer cableMaxCurrent, Integer reservationId, TransactionType transactionInfo,
		IdTokenType idToken, EVSEType evse, Collection<MeterValueType> meterValue) implements CSRequest {
}