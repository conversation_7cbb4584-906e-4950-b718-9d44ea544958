package org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.notifyEvent;

import java.time.ZonedDateTime;

import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.common.ComponentType;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.common.VariableType;

/**
 * Class to report an event notification for a component-variable.
 * 
 * @param eventId                 Required. Identifies the event. This field can be referred to as a cause by other events.
 *                                <p>
 *                                Field Type: integer
 *                                Cardinality: 1..1
 * 
 * @param timestamp               Required. Timestamp of the moment the report was generated.
 *                                <p>
 *                                Field Type: dateTime
 *                                Cardinality: 1..1
 * 
 * @param trigger                 Required. Type of trigger for this event, e.g. exceeding a threshold value.
 *                                <p>
 *                                Field Type: EventTriggerEnumType
 *                                Cardinality: 1..1
 * 
 * @param cause                   Optional. Refers to the Id of an event that is considered to be the cause for this event.
 *                                <p>
 *                                Field Type: integer
 *                                Cardinality: 0..1
 *                                
 * @param actualValue             Required. Actual value (attributeType Actual) of the variable.
 *                                The Configuration Variable ReportingValueSize can be used to limit GetVariableResult.attributeValue, 
 *                                VariableAttribute.value and EventData.actualValue. The max size of these values will always remain equal.
 *                                <p>
 *                                Field Type: string[0..2500]
 *                                Cardinality: 1..1
 *
 * @param techCode                Optional. Technical (error) code as reported by component.
 *                                <p>
 *                                Field Type: string[0..50]
 *                                Cardinality: 0..1
 *
 * @param techInfo                Optional. Technical detail information as reported by component.
 *                                <p>
 *                                Field Type: string[0..500]
 *                                Cardinality: 0..1
 *
 * @param cleared                 Optional. Cleared is set to true to report the clearing of a monitored situation, i.e. a 'return to normal'.
 *                                <p>
 *                                Field Type: boolean
 *                                Cardinality: 0..1
 *
 * @param transactionId           Optional. If an event notification is linked to a specific transaction, 
 *                                this field can be used to specify its transactionId.
 *                                <p>
 *                                Field Type: identifierString[0..36]
 *                                Cardinality: 0..1
 *
 * @param variableMonitoringId    Optional. Identifies the VariableMonitoring which triggered the event.
 *                                <p>
 *                                Field Type: integer
 *                                Cardinality: 0..1
 *
 * @param eventNotificationType   Required. Specifies the event notification type of the message.
 *                                <p>
 *                                Field Type: EventNotificationEnumType
 *                                Cardinality: 1..1
 *
 * @param component               Required. Component for which event is notified.
 *                                <p>
 *                                Field Type: ComponentType
 *                                Cardinality: 1..1
 *                                
 * @param variable                Required. Variable for which event is notified.
 *                                <p>
 *                                Field Type: VariableType
 *                                Cardinality: 1..1
 */
public record EventDataType(int eventId, ZonedDateTime timestamp, EventTriggerEnumType trigger, Integer cause,
		String actualValue, String techCode, String techInfo, Boolean cleared, String transactionId,
		Integer variableMonitoringId, EventNotificationEnumType eventNotificationType,
		ComponentType component, VariableType variable) {
}