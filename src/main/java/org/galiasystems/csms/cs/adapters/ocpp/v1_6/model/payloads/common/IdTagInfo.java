package org.galiasystems.csms.cs.adapters.ocpp.v1_6.model.payloads.common;

import org.galiasystems.csms.management.types.enums.AuthorizationStatus;

import java.time.ZonedDateTime;

/**
 * Contains status information about an identifier. It is returned in Authorize, Start Transaction and Stop
 * Transaction responses.
 *
 * @param expiryDate  Optional. This contains the date at which idTag should be removed from the
 *                    Authorization Cache.
 *                    Field type:   ZonedDateTime
 *                    Cardinality:  0..1
 * @param parentIdTag Optional. This contains the parent-identifier.
 *                    Field type:   String
 *                    Cardinality:  0..1
 * @param status      Required. This contains whether the idTag has been accepted or not by the
 *                    Central System.
 *                    Field type:   AuthorizationStatus (enum)
 *                    Cardinality:  1..1
 */
public record IdTagInfo(ZonedDateTime expiryDate, String parentIdTag, AuthorizationStatus status) {
}