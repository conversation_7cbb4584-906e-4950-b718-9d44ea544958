package org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.common;

/**
 * The following table lists the values allowed for the TxStopPoint variable. 
 * These values represent logical steps or events that (may) occur during a charging session. 
 * When such an event occurs, and it is listed in in the TxStopPoint variable, then this marks the end of a transaction.
 * The values are the same as for TxStartPoint, but in this case the meaning is different, since it refers to the ending of the event,
 * rather than the start. 
 * For use with TxStopPoint each value should be interpreted as if it had "Not" prefixed to it.
 */
public enum TxStopPoint {
	
	/**
	 * An object (probably an EV) is no longer detected in the parking/charging bay.
	 */
	ParkingBayOccupancy,
	
	/**
	 * One or both ends of the Charging Cable have been disconnected 
	 * (if this can be detected, else detection of a cable being unplugged from the socket), 
	 * or for wireless charging: communication between EVSE and EV is lost.
	 */
	EVConnected,
	
	/**
	 * Driver or EV is no longer authorized, this can also be some form of anonymous authorization like a start button. 
	 * The end of authorization will cause the Charging Station to stop the energy transfer, 
	 * after which the TransactionEventRequest with eventType = Ended will be transmitted.
	 */
	Authorized,
	
	/**
	 * All preconditions for charging are no longer met. 
	 * This event is the logical OR of EVConnected and Authorized and should be used if a transaction is supposed to end 
	 * when EV is disconnected and/or deauthorized. 
	 * This will cause the Charging Station to stop the energy transfer, 
	 * after which the TransactionEventRequest with eventType = Ended will be transmitted. 
	 * It is exactly the same as having the values EVConnected, Authorized in TxStopPoint.
	 * Despite its name, this event is not related to the state of the power relay.
	 */
	PowerPathClosed,
	
	/**
	 * Energy is not being transferred between EV and EVSE.
	 * This is not recommended to use as a TxStopPoint, 
	 * because it will stop the transaction as soon as EV or EVSE (temporarily) suspend the charging.
	 */
	EnergyTransfer,
	
	/**
	 * This condition has no meaning as a TxStopPoint and should not be used as such.
	 */
	DataSigned,
	;
}
