package org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.transactionEvent;

/**
 * Transaction type of the TransactionEventRequest.
 * 
 * @param transactionId       Required. This contains the Id of the transaction.
 * 						  	  <p>
 * 						  	  Field Type: identifierString[0..36]
 * 						  	  Cardinality: 1..1
 * 
 * @param chargingState       Optional. Current charging state, is required when state has changed.
 * 						  	  <p>
 * 						  	  Field Type: ChargingStateEnumType
 * 						  	  Cardinality: 0..1
 * 
 * @param timeSpentCharging   Optional. Contains the total time that energy flowed from EVSE to EV during the transaction (in seconds). 
 * 							  Note that timeSpentCharging is smaller or equal to the duration of the transaction.
 * 						  	  <p>
 * 						  	  Field Type: integer
 * 						  	  Cardinality: 0..1
 * 
 * @param stoppedReason       Optional. The <i>stoppedReason </i>is the reason/event that initiated the process of stopping the transaction. 
 * 							  It will normally be the user stopping authorization via card (Local or MasterPass) or app (Remote), 
 * 							  but it can also be CSMS revoking authorization (DeAuthorized), 
 * 							  or disconnecting the EV when TxStopPoint = EVConnected (EVDisconnected). 
 * 							  Most other reasons are related to technical faults or energy limitations. 
 * 							  MAY only be omitted when <i>stoppedReason </i>is "Local"
 * 						  	  <p>
 * 						  	  Field Type: ReasonEnumType
 * 						  	  Cardinality: 0..1
 * 
 * @param remoteStartId       Optional. The ID given to remote start request (RequestStartTransactionRequest. 
 * 							  This enables to CSMS to match the started transaction to the given start request.
 * 						  	  <p>
 * 						  	  Field Type: integer
 * 						  	  Cardinality: 0..1
 */
public record TransactionType(String transactionId, ChargingStateEnumType chargingState, Integer timeSpentCharging, 
		ReasonEnumType stoppedReason, Integer remoteStartId) {
}