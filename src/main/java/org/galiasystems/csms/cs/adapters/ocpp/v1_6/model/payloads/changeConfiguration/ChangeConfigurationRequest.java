package org.galiasystems.csms.cs.adapters.ocpp.v1_6.model.payloads.changeConfiguration;

import org.galiasystems.csms.cs.adapters.ocpp.payloads.CSMSRequest;

/**
 * sent by Central System to Charge Point. It
 * is RECOMMENDED that the content and meaning of the 'key' and 'value' fields is agreed upon between Charge
 * Point and Central System
 *
 * @param key   Required. The name of the configuration setting to change.
 *              See for standard configuration key names and associated values
 *              Field type: String (length 50)
 *              Cardinality: 1..1
 *              
 * @param value Required. The new value as string for the setting.
 *              See for standard configuration key names and associated values
 *              Field type: String (length 500)
 *              Cardinality: 1..1
 */
public record ChangeConfigurationRequest(String key, String value) implements CSMSRequest {
}