package org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.messages;

import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.bootNotification.BootNotificationRequest;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.changeAvailability.ChangeAvailabilityRequest;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.getBaseReport.GetBaseReportRequest;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.getVariables.GetVariablesRequest;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.heartbeat.HeartbeatRequest;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.meterValues.MeterValuesRequest;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.notifyEvent.NotifyEventRequest;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.notifyReport.NotifyReportRequest;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.requestStartTransaction.RequestStartTransactionRequest;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.requestStopTransaction.RequestStopTransactionRequest;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.reset.ResetRequest;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.setVariables.SetVariablesRequest;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.statusNotification.StatusNotificationRequest;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.transactionEvent.TransactionEventRequest;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.authorize.AuthorizeRequest;

public enum Action {
	BootNotification(BootNotificationRequest.class),
	Heartbeat(HeartbeatRequest.class),
	NotifyEvent(NotifyEventRequest.class),
	SetVariables(SetVariablesRequest.class),
	GetVariables(GetVariablesRequest.class),
	GetBaseReport(GetBaseReportRequest.class),
	NotifyReport(NotifyReportRequest.class),
	StatusNotification(StatusNotificationRequest.class),
	ChangeAvailability(ChangeAvailabilityRequest.class),
	Reset(ResetRequest.class),
	TransactionEvent(TransactionEventRequest.class),
	Authorize(AuthorizeRequest.class),
	MeterValues(MeterValuesRequest.class),
	RequestStartTransaction(RequestStartTransactionRequest.class),
	RequestStopTransaction(RequestStopTransactionRequest.class),
	;
	
	private final Class<?> payloadClass;
	
	private Action(final Class<?> payloadClass) {
		this.payloadClass = payloadClass;
	}
	
	public Class<?> getPayloadClass() {
		return payloadClass;
	}
}
