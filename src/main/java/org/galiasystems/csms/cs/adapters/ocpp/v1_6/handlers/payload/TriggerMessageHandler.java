package org.galiasystems.csms.cs.adapters.ocpp.v1_6.handlers.payload;

import io.smallrye.mutiny.Uni;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import org.galiasystems.csms.cs.ChargingStationAdapterProxy;
import org.galiasystems.csms.cs.adapters.ocpp.handlers.payload.ResponsePayloadHandler;
import org.galiasystems.csms.cs.adapters.ocpp.v1_6.model.payloads.triggerMessage.TriggerMessageRequest;
import org.galiasystems.csms.cs.adapters.ocpp.v1_6.model.payloads.triggerMessage.TriggerMessageResponse;
import org.galiasystems.csms.cs.adapters.ocpp.v1_6.utils.EnumConverter;
import org.galiasystems.csms.management.types.TriggerMessageResult;

@Singleton
public class TriggerMessageHandler extends ResponsePayloadHandler<TriggerMessageRequest, TriggerMessageResponse> {

    @Inject
    private ChargingStationAdapterProxy chargingStationAdapterProxy;

    @Override
    protected Class<TriggerMessageRequest> getRequestPayloadType() {
        return TriggerMessageRequest.class;
    }

    @Override
    protected Class<TriggerMessageResponse> getResponsePayloadType() {
        return TriggerMessageResponse.class;
    }

    @Override
    protected Uni<Void> onMessage(long chargingStationId, String messageId, TriggerMessageResponse responsePayload, TriggerMessageRequest requestPayload) {
        final TriggerMessageResult triggerMessageResult = new TriggerMessageResult(EnumConverter.convertTriggerMessageStatus(responsePayload.status()));

        return chargingStationAdapterProxy.triggerMessageReceived(chargingStationId, triggerMessageResult)
                .invoke((ignored) -> super.sendResponseMessage(chargingStationId, messageId, triggerMessageResult));
    }
}