package org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.bootNotification;

/**
 * The physical system where an Electrical Vehicle (EV) can be charged.
 *
 * @param serialNumber    Optional. Vendor-specific device identifier.
 *                        <p>
 *                        Field Type: string[0..25]
 *                        Cardinality: 0..1
 *                        
 * @param model           Required. Defines the model of the device.
 *                        <p>
 *                        Field Type: string[0..20]
 *                        Cardinality: 1..1
 *                        
 * @param vendorName      Required. Identifies the vendor (not necessarily in a unique manner).
 *                        <p>
 *                        Field Type: string[0..50]
 *                        Cardinality: 1..1
 *                        
 * @param firmwareVersion Optional. This contains the firmware version of the Charging Station.
 *                        <p>
 *                        Field Type: string[0..50]
 *                        Cardinality: 0..1
 *                        
 * @param modem           Optional. Defines the functional parameters of a communication link.
 *                        <p>
 *                        Field Type: ModemType
 *                        Cardinality: 0..1
 */
public record ChargingStationType(String serialNumber, String model, String vendorName, String firmwareVersion,
                                  ModemType modem) {
}