package org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.handlers.payload;

import java.time.ZonedDateTime;

import org.galiasystems.csms.cs.ChargingStationAdapterProxy;
import org.galiasystems.csms.cs.adapters.ocpp.handlers.payload.RequestPayloadHandler;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.bootNotification.BootNotificationRequest;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.bootNotification.BootNotificationResponse;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.bootNotification.RegistrationStatusEnumType;

import io.smallrye.mutiny.Uni;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import org.galiasystems.csms.management.model.ChargingStation;

@Singleton
public class BootNotificationHandler extends RequestPayloadHandler<BootNotificationRequest, BootNotificationResponse> {
	
	@Inject
	private ChargingStationAdapterProxy chargingStationAdapterProxy;

	@Override
	protected Class<BootNotificationRequest> getRequestPayloadType() {
		return BootNotificationRequest.class;
	}

	@Override
	protected Uni<BootNotificationResponse> onMessage(final long chargingStationId, final String messageId,
			final BootNotificationRequest requestPayload) {
		final ChargingStation bootedChargingStation =
				new ChargingStation.Builder()
						.id(chargingStationId)
						.chargePointSerialNumber(requestPayload.chargingStation().serialNumber())
						.chargePointVendor(requestPayload.chargingStation().vendorName())
						.chargePointModel(requestPayload.chargingStation().model())
						.iccid(requestPayload.chargingStation().modem().iccid())
						.firmwareVersion(requestPayload.chargingStation().firmwareVersion())
						.imsi(requestPayload.chargingStation().modem().imsi())
						.build();
		final var chargingStationBootedResult = chargingStationAdapterProxy.chargingStationBooted(bootedChargingStation);
		
		return chargingStationBootedResult
				.onItem()
				.transform((chargingStation) -> new BootNotificationResponse(ZonedDateTime.now(), 10, RegistrationStatusEnumType.Accepted, null));
	}

	

}