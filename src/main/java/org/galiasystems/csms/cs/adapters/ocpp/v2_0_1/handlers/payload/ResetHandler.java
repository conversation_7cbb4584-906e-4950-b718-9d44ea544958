package org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.handlers.payload;

import org.galiasystems.csms.cs.ChargingStationAdapterProxy;
import org.galiasystems.csms.cs.adapters.ocpp.handlers.payload.ResponsePayloadHandler;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.common.StatusInfoType;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.reset.ResetRequest;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.reset.ResetResponse;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.utils.EnumConverter;
import org.galiasystems.csms.management.types.ResetResult;
import org.galiasystems.csms.management.types.enums.ResetStatus;

import io.smallrye.mutiny.Uni;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;

@Singleton
public class ResetHandler extends ResponsePayloadHandler<ResetRequest, ResetResponse> {

	@Inject
	private ChargingStationAdapterProxy chargingStationAdapterProxy;
	
	@Override
	protected Class<ResetRequest> getRequestPayloadType() {
		return ResetRequest.class;
	}

	@Override
	protected Class<ResetResponse> getResponsePayloadType() {
		return ResetResponse.class;
	}

	@Override
	protected Uni<Void> onMessage(final long chargingStationId, final String messageId, final ResetResponse responsePayload,
			final ResetRequest requestPayload) {
		
		final ResetStatus resetStatus = EnumConverter.convertResetStatusEnumType(responsePayload.status());
		
		final StatusInfoType statusInfo = responsePayload.statusInfo();
		final String reasonCode;
		final String additionalInfo;
		if (statusInfo != null) {
			reasonCode = statusInfo.reasonCode();
			additionalInfo = statusInfo.additionalInfo();
		} else {
			reasonCode = null;
			additionalInfo = null;
		}
		
		final ResetResult resetResult = new ResetResult(resetStatus, reasonCode, additionalInfo);
		
		return this.chargingStationAdapterProxy.resetResultReceived(chargingStationId, resetResult)
				.invoke((ignored) -> super.sendResponseMessage(chargingStationId, messageId, resetResult));
	}
}