package org.galiasystems.csms.cs.adapters.ocpp.v1_6.model.payloads.remote.startTransaction;

import java.time.ZonedDateTime;

/**
 * defines a list of charging periods, as used in: GetCompositeSchedule.conf and
 * ChargingProfile.
 *
 * @param duration               Optional. Duration of the charging schedule in seconds. If the
 *                               duration is left empty, the last period will continue indefinitely or
 *                               until end of the transaction in case startSchedule is absent.
 *                               Field type: integer
 *                               Cardinality: 0..1
 * @param startSchedule          Optional. Starting point of an absolute schedule. If absent the
 *                               schedule will be relative to start of charging.
 *                               Field type: dateTime
 *                               Cardinality: 0..1
 * @param chargingRateUnitType   Required. The unit of measure Limit is expressed in.
 *                               Field type: ChargeRateUnitType
 *                               Cardinality: 1..1
 * @param chargingSchedulePeriod Required. List of ChargingSchedulePeriod elements defining
 *                               maximum power or current usage over time. The startSchedule of
 *                               the first ChargingSchedulePeriod SHALL always be 0.
 *                               Field type: ChargeSchedulePeriod
 *                               Cardinality: 1..*
 * @param minChargingRate        Optional. Minimum charging rate supported by the electric
 *                               vehicle. The unit of measure is defined by the chargingRateUnit.
 *                               This parameter is intended to be used by a local smart charging
 *                               algorithm to optimize the power allocation for in the case a
 *                               charging process is inefficient at lower charging rates. Accepts at
 *                               most one digit fraction (e.g. 8.1)
 *                               Field type: decimal
 *                               Cardinality: 0..1
 */
public record ChargingSchedule(Integer duration, ZonedDateTime startSchedule, ChargingRateUnitType chargingRateUnitType,
                               ChargingSchedulePeriod chargingSchedulePeriod, Float minChargingRate) {
}