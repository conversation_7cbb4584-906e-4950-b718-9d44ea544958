package org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.reset;

import org.galiasystems.csms.cs.adapters.ocpp.payloads.CSResponse;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.common.StatusInfoType;

/**
 * ResetResponse PDU sent by the Charging Station to the CSMS in response to ResetRequest.
 * 
 * @param status            Required. This indicates whether the Charging Station is able to perform the reset.
 *                          <p>
 *                          Field Type: ResetStatusEnumType
 *                          Cardinality: 1..1
 *                        
 * @param statusInfo        Optional. Detailed status information.
 *                          <p>
 *                          Field Type: StatusInfoType
 *                          Cardinality: 0..1
 */
public record ResetResponse(ResetStatusEnumType status, StatusInfoType statusInfo) implements CSResponse {
}