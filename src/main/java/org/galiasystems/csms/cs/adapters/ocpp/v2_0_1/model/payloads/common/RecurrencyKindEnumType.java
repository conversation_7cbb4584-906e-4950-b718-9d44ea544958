package org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.common;

/**
 * Kind of the Recurrency of the charging profile.
 */
public enum RecurrencyKindEnumType {

	/**
	 * The schedule restarts every 24 hours, at the same time as in the startSchedule.
	 */
	Daily,
	
	/**
	 * The schedule restarts every 7 days, at the same time and day-of-the-week as in the startSchedule.
	 */
	Weekly,
	;
}
