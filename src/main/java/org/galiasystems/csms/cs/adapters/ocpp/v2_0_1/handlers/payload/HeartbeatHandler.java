package org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.handlers.payload;

import java.time.ZonedDateTime;

import org.galiasystems.csms.cs.adapters.ocpp.handlers.payload.RequestPayloadHandler;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.heartbeat.HeartbeatRequest;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.heartbeat.HeartbeatResponse;

import io.smallrye.mutiny.Uni;
import jakarta.inject.Singleton;


@Singleton
public class HeartbeatHandler extends RequestPayloadHandler<HeartbeatRequest, HeartbeatResponse> {

	@Override
	protected Class<HeartbeatRequest> getRequestPayloadType() {
		return HeartbeatRequest.class;
	}

	@Override
	protected Uni<HeartbeatResponse> onMessage(final long chargingStationId,
			final String messageId, final HeartbeatRequest requestPayload) {
		
		return Uni.createFrom().item(new HeartbeatResponse(ZonedDateTime.now()));
	}

}