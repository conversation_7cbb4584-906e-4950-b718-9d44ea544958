package org.galiasystems.csms.cs.adapters.ocpp.messages;

/**
 * Request message.
 * 
 * A CALL always consists of 4 elements: The standard elements MessageTypeId and MessageId, 
 * a specific Action that is required on the other side and a payload, 
 * the arguments to the Action. 
 * 
 * The syntax of a CALL looks like this: 
 * [<MessageTypeId>, "<MessageId>", "<Action>", {<Payload>}]
 */
public class CallMessage<P, A> extends MessageWithPayload<P> {
	
	/**
	 * The name of the remote procedure or action. 
	 * This field SHALL contain a case-sensitive string.
	 * The field SHALL contain the OCPP Message name without the "Request" suffix. 
	 * For example: For a "BootNotificationRequest", this field shall be set to "BootNotification".
	 * 
	 * Datatype: string
	 */
	private final A action;

	public CallMessage(final String messageId, final A action, final P payload) {
		super(MessageType.CallMessage, messageId, payload);
		this.action = action;
	}

	public A getAction() {
		return action;
	}

	@Override
	public String toString() {
		return "CallMessage [action=" + action + "] " + super.toString();
	}
}