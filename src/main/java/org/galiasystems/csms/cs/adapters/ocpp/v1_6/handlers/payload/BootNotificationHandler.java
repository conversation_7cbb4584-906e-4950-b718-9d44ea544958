package org.galiasystems.csms.cs.adapters.ocpp.v1_6.handlers.payload;

import io.smallrye.mutiny.Uni;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import org.galiasystems.csms.cs.ChargingStationAdapterProxy;
import org.galiasystems.csms.cs.adapters.ocpp.handlers.payload.RequestPayloadHandler;
import org.galiasystems.csms.cs.adapters.ocpp.v1_6.model.payloads.bootNotification.BootNotificationRequest;
import org.galiasystems.csms.cs.adapters.ocpp.v1_6.model.payloads.bootNotification.BootNotificationResponse;
import org.galiasystems.csms.cs.adapters.ocpp.v1_6.model.payloads.bootNotification.RegistrationStatus;
import org.galiasystems.csms.management.model.ChargingStation;


import java.time.ZonedDateTime;


@Singleton
public class BootNotificationHandler extends RequestPayloadHandler<BootNotificationRequest, BootNotificationResponse> {

    @Inject
    private ChargingStationAdapterProxy chargingStationAdapterProxy;


    @Override
    protected Class<BootNotificationRequest> getRequestPayloadType() {
        return BootNotificationRequest.class;
    }

    @Override
    protected Uni<BootNotificationResponse> onMessage(final long chargingStationId, final String messageId, final BootNotificationRequest requestPayload) {
        final ChargingStation bootedChargingStation =
                new ChargingStation.Builder()
                        .id(chargingStationId)
                        .chargePointSerialNumber(requestPayload.chargePointSerialNumber())
                        .chargePointVendor(requestPayload.chargePointVendor())
                        .meterType(requestPayload.meterType())
                        .meterSerialNumber(requestPayload.meterSerialNumber())
                        .chargePointModel(requestPayload.chargePointModel())
                        .iccid(requestPayload.iccid())
                        .chargeBoxSerialNumber(requestPayload.chargeBoxSerialNumber())
                        .firmwareVersion(requestPayload.firmwareVersion())
                        .imsi(requestPayload.imsi())
                        .build();
        final var chargingStationBootedResult = chargingStationAdapterProxy.chargingStationBooted(bootedChargingStation);

        return chargingStationBootedResult
                .onItem()
                .transform((chargingStation) -> new org.galiasystems.csms.cs.adapters.ocpp.v1_6.model.payloads.bootNotification.BootNotificationResponse(ZonedDateTime.now(), 10, RegistrationStatus.Accepted));

    }
}