package org.galiasystems.csms.cs.adapters.ocpp.v1_6.model.payloads.changeAvailability;

/**
 * Status returned in response to ChangeAvailability.req.
 */
public enum AvailabilityStatus {
	
	/**
	 * Request has been accepted and will be executed.
	 */
	Accepted,
	
	/**
	 * Request has not been accepted and will not be executed.
	 */
	Rejected,
	
	/**
	 * Request has been accepted and will be executed when transaction(s) in progress have finished.
	 */
	Scheduled,
	;
}