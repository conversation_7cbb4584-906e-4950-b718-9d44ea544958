package org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.getVariables;

/**
 * Result status of getting the variable.
 */
public enum GetVariableStatusEnumType {
	
	/**
	 * Variable successfully get.
	 */
	Accepted, 

	/**
	 * Request is rejected.
	 */
	Rejected,
	
	/**
	 * Component is not known.
	 */
	UnknownComponent,
	
	/**
	 * Variable is not known.
	 */
	UnknownVariable,
	
	/**
	 * The AttributeType is not supported.
	 */
	NotSupportedAttributeType,
	;
}