package org.galiasystems.csms.cs.adapters.ocpp.v1_6.model.payloads.bootNotification;

import org.galiasystems.csms.cs.adapters.ocpp.payloads.CSMSResponse;

import java.time.ZonedDateTime;

/**
 * Heartbeat.conf PDU sent by the Central System to the Charge Point in response to a Heartbeat.req PDU.
 *
 * @param currentTime Required. This contains the Central System’s current time.
 *                    Field type: dateTime
 *                    Cardinality: 1..1
 *                    
 * @param interval    Required. When RegistrationStatus is Accepted, this contains the heartbeat
 *                    interval in seconds. If the Central System returns something other than
 *                    Accepted, the value of the interval field indicates the minimum wait time before
 *                    sending a next BootNotification request.
 *                    Field type: integer
 *                    Cardinality: 1..1
 *                    
 * @param status      Required. This contains whether the Charge Point has been registered within the
 *                    System Central.
 *                    Field type: {@link RegistrationStatus}
 *                    Cardinality: 1..1
 */
public record BootNotificationResponse(ZonedDateTime currentTime, int interval,
                                       RegistrationStatus status) implements CSMSResponse {
}