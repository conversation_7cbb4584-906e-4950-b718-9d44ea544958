package org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.requestStartTransaction;

import org.galiasystems.csms.cs.adapters.ocpp.payloads.CSMSRequest;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.common.ChargingProfileType;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.common.IdTokenType;

/**
 * RequestStartTransactionRequest PDU sent by the CSMS to the Charging Station.
 * 
 * @param evseId 			Optional. Number of the EVSE on which to start the transaction. 
 * 							EvseId SHALL be > 0
 *                          <p>
 *                          Field Type: integer
 *                          Cardinality: 0..1
 *                        
 * @param remoteStartId     Required. Id given by the server to this start request. 
 * 							The Charging Station will return this in the TransactionEventRequest, 
 * 							letting the server know which transaction was started for this request.
 *                          <p>
 *                          Field Type: integer
 *                          Cardinality: 1..1
 *                          
 * @param idToken           Required. The identifier that the Charging Station must use to start a transaction.
 *                          <p>
 *                          Field Type: IdTokenType
 *                          Cardinality: 1..1
 *                          
 * @param chargingProfile   Optional. Charging Profile to be used by the Charging Station for the requested transaction.
 * 							ChargingProfilePurpose MUST be set to TxProfile
 *                          <p>
 *                          Field Type: ChargingProfileType
 *                          Cardinality: 0..1
 *                          
 * @param groupIdToken      Optional. The groupIdToken is only relevant when the transaction is to be started on an EVSE 
 *							for which a reservation for groupIdToken is active, and the configuration variable AuthorizeRemoteStart = false
 *							(otherwise the AuthorizeResponse could return the groupIdToken).
 *                          <p>
 *                          Field Type: IdTokenType
 *                          Cardinality: 0..1
 */
public record RequestStartTransactionRequest(Integer evseId, long remoteStartId, IdTokenType idToken, ChargingProfileType chargingProfile,
		IdTokenType groupIdToken) implements CSMSRequest {
}