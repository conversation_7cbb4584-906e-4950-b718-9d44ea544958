package org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.common;

/**
 * RelativeTimeIntervalType is used in SalesTariffEntryType.
 * 
 * @param start	 			Required. Start of the interval, in seconds from NOW.
 *                        	<p>
 *                        	Field Type: integer
 *                        	Cardinality: 1..1
 *                        
 * @param duration			Optional. Duration of the interval, in seconds.
 *                        	<p>
 *                        	Field Type: integer
 *                        	Cardinality: 0..1
 */
public record RelativeTimeIntervalType(int start, Integer duration) {
}