package org.galiasystems.csms.cs.adapters.ocpp.v1_6.handlers.payload;

import org.galiasystems.csms.cs.ChargingStationAdapterProxy;
import org.galiasystems.csms.cs.adapters.ocpp.handlers.payload.ResponsePayloadHandler;
import org.galiasystems.csms.cs.adapters.ocpp.v1_6.model.payloads.reset.ResetRequest;
import org.galiasystems.csms.cs.adapters.ocpp.v1_6.model.payloads.reset.ResetResponse;
import org.galiasystems.csms.cs.adapters.ocpp.v1_6.utils.EnumConverter;
import org.galiasystems.csms.management.types.ResetResult;
import org.galiasystems.csms.management.types.enums.ResetStatus;

import io.smallrye.mutiny.Uni;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;

@Singleton
public class ResetHandler extends ResponsePayloadHandler<ResetRequest, ResetResponse> {

	@Inject
	private ChargingStationAdapterProxy chargingStationAdapterProxy;
	
	@Override
	protected Class<ResetRequest> getRequestPayloadType() {
		return ResetRequest.class;
	}

	@Override
	protected Class<ResetResponse> getResponsePayloadType() {
		return ResetResponse.class;
	}

	@Override
	protected Uni<Void> onMessage(final long chargingStationId, final String messageId, final ResetResponse responsePayload,
			final ResetRequest requestPayload) {

		final ResetStatus resetStatus = EnumConverter.convertResetStatus(responsePayload.status());
		
		final ResetResult resetResult = new ResetResult(resetStatus, null, null);
		
		return this.chargingStationAdapterProxy.resetResultReceived(chargingStationId, resetResult)
				.invoke((ignored) -> super.sendResponseMessage(chargingStationId, messageId, resetResult));
	}
}