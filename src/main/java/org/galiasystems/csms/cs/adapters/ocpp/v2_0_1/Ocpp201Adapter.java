package org.galiasystems.csms.cs.adapters.ocpp.v2_0_1;

import io.smallrye.mutiny.Uni;
import io.vertx.mutiny.core.eventbus.EventBus;
import io.vertx.mutiny.core.eventbus.MessageConsumer;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import org.galiasystems.csms.cs.ChargingStationAdapterProxy;
import org.galiasystems.csms.cs.ChargingStationAdapterType;
import org.galiasystems.csms.cs.adapters.ocpp.OcppAdapter;
import org.galiasystems.csms.cs.adapters.ocpp.messages.CallErrorMessage;
import org.galiasystems.csms.cs.adapters.ocpp.messages.Message;
import org.galiasystems.csms.cs.adapters.ocpp.messages.MessageType;
import org.galiasystems.csms.cs.adapters.ocpp.messages.ReceivedCallMessage;
import org.galiasystems.csms.cs.adapters.ocpp.messages.ReceivedCallResultMessage;
import org.galiasystems.csms.cs.adapters.ocpp.messages.SendingCallMessage;
import org.galiasystems.csms.cs.adapters.ocpp.payloads.CSMSRequest;
import org.galiasystems.csms.cs.adapters.ocpp.util.Utils;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.handlers.message.CallErrorMessageHandler;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.handlers.message.CallMessageHandler;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.handlers.message.CallResultMessageHandler;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.messages.*;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.changeAvailability.ChangeAvailabilityRequest;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.changeAvailability.OperationalStatusEnumType;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.common.AdditionalInfoType;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.common.AttributeEnumType;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.common.ComponentType;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.common.EVSEType;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.common.IdTokenEnumType;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.common.IdTokenType;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.common.VariableType;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.getBaseReport.GetBaseReportRequest;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.getBaseReport.ReportBaseEnumType;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.getVariables.GetVariableDataType;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.getVariables.GetVariablesRequest;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.requestStartTransaction.RequestStartTransactionRequest;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.requestStopTransaction.RequestStopTransactionRequest;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.reset.ResetEnumType;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.reset.ResetRequest;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.setVariables.SetVariableDataType;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.setVariables.SetVariablesRequest;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.utils.EnumConverter;
import org.galiasystems.csms.cs.adapters.ocpp.websocket.MessageCodec;
import org.galiasystems.csms.cs.adapters.ocpp.websocket.SecurityProfile;
import org.galiasystems.csms.cs.adapters.ocpp.websocket.ServerSocket;
import org.galiasystems.csms.management.model.AdditionalIdToken;
import org.galiasystems.csms.management.model.ChargingStation;
import org.galiasystems.csms.management.model.IdToken;
import org.galiasystems.csms.management.model.enums.AvailabilityStatus;
import org.galiasystems.csms.management.model.enums.ReportType;
import org.galiasystems.csms.management.model.enums.ResetType;
import org.galiasystems.csms.management.types.*;
import org.galiasystems.csms.management.types.enums.MessageTrigger;
import org.jboss.logging.Logger;

import java.time.Duration;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.function.Function;

@ApplicationScoped
public class Ocpp201Adapter implements OcppAdapter {

	private static final long CALL_MESSAGE_RESPONSE_TIMEOUT = 30;

	private static final ChargingStationAdapterType CHARGING_STATION_ADAPTER_TYPE = ChargingStationAdapterType.OCPP_2_0_1;
    
    private static final Set<SecurityProfile> ENABLED_SECURITY_PROFILES = new HashSet<>();
    
    static {
    	ENABLED_SECURITY_PROFILES.add(SecurityProfile.Profile1);
    	ENABLED_SECURITY_PROFILES.add(SecurityProfile.Profile2);
    	ENABLED_SECURITY_PROFILES.add(SecurityProfile.Profile3);
    }
    
    @Inject
    private Logger log;
    
    @Inject
    private EventBus bus;
    
    @Inject
    private MessageCodec messageCodec;

    @Inject
    private CallMessageHandler callMessageHandler;

    @Inject
    private CallResultMessageHandler callResultMessageHandler;

    @Inject
    private CallErrorMessageHandler callErrorMessageHandler;

    @Inject
    private ChargingStationAdapterProxy chargingStationAdapterProxy;

	@Inject
	private ServerSocket serverSocket;

    //TODO: concurrent map + timeout
    private Map<Long, Map<String, CSMSRequestWithAction>> csmsRequests = new HashMap<>();

    private Map<Long, SecurityProfile> csWebSocketSecProfiles = new HashMap<>();


	@Override
    public Uni<ChargingStation> adapterConnectionOpened(final long chargingStationId, final SecurityProfile securityProfile) {
		log.debug("ocpp 2.0.1 connection opened");
		csWebSocketSecProfiles.put(chargingStationId, securityProfile);
        return chargingStationAdapterProxy.adapterConnectionOpened(CHARGING_STATION_ADAPTER_TYPE, chargingStationId);
    }

	@Override
    public Uni<Void> messageReceived(final long chargingStationId, final Message message) {
        final var messageType = message.getMessageType();

        switch (messageType) {
            case MessageType.CallMessage: {
            	return this.callMessageHandler.onMessage(chargingStationId, (ReceivedCallMessage) message)
	            	.onItem()
	            	.transformToUni((messageToSend) -> serverSocket.sendMessage(chargingStationId, messageToSend))
	            	.onFailure()
	            	.recoverWithNull();
            }
            case MessageType.CallResultMessage: {
                final var cSMSRequest = csmsRequests.get(chargingStationId).get(message.getMessageId());

                return this.callResultMessageHandler.onMessage(chargingStationId, (ReceivedCallResultMessage) message,
                        cSMSRequest.action(), cSMSRequest.request())
                		.onFailure()
    	            	.recoverWithNull();
            }
            case MessageType.CallErrorMessage: {
                final var cSMSRequest = csmsRequests.get(chargingStationId).get(message.getMessageId());

                return this.callErrorMessageHandler.onMessage(chargingStationId, (CallErrorMessage) message,
                        cSMSRequest.action(), cSMSRequest.request());
            }
            default:
                throw new IllegalStateException("Unknown message type! " + messageType);
        }
    }

	@Override
    public Uni<ChargingStation> adapterConnectionClosed(final long chargingStationId) {
        return chargingStationAdapterProxy.adapterConnectionClosed(chargingStationId);
    }

    @Override
    public Uni<Void> executeReport(final long chargingStationId, final long requestId,
    		final ReportType type) {
    	
    	final ReportBaseEnumType reportBaseEnumType = EnumConverter.convertReportType(type);
    	
    	return getBaseReport(chargingStationId, requestId, reportBaseEnumType);
    }

    @Override
    public Uni<List<SetVariableResult>> setVariables(final long chargingStationId, final List<VariableValueToSet> variableValuesToSet) {

    	final List<SetVariableDataType> setVariableDataList = variableValuesToSet.stream().map((variableValueToSet) -> {
    		
    		final AttributeEnumType attributeType = EnumConverter.convertVariableValueType(variableValueToSet.variableValueType());
    		final String attributeValue = variableValueToSet.variableValue();
    		
    		final EVSEType eVSEType = createEVSEType(variableValueToSet.evseId(), variableValueToSet.connectorId());
    		
    		final ComponentType componentType = new ComponentType(variableValueToSet.componentName(), 
    				variableValueToSet.componentInstance(), eVSEType);
    		
    		final VariableType variableType = new VariableType(variableValueToSet.variableName(), 
    				variableValueToSet.variableInstance());
    		
    		return new SetVariableDataType(attributeType, attributeValue, componentType, variableType);
    	}).toList();

        final SetVariablesRequest payload = new SetVariablesRequest(setVariableDataList);

        return sendCallMessageAndWaitForResponse(chargingStationId, Action.SetVariables, payload);
    }
    
	@Override
	public Uni<List<GetVariableResult>> getVariables(final long chargingStationId, final List<VariableValueToGet> variableValuesToGet) {
		
		final List<GetVariableDataType> getVariableDataList = variableValuesToGet.stream().map((variableValueToGet) -> {
    		
    		final AttributeEnumType attributeType = EnumConverter.convertVariableValueType(variableValueToGet.variableValueType());
    		
    		final EVSEType eVSEType = createEVSEType(variableValueToGet.evseId(), variableValueToGet.connectorId());
    		
    		final ComponentType componentType = new ComponentType(variableValueToGet.componentName(), 
    				variableValueToGet.componentInstance(), eVSEType);
    		
    		final VariableType variableType = new VariableType(variableValueToGet.variableName(), 
    				variableValueToGet.variableInstance());
    		
    		return new GetVariableDataType(attributeType, componentType, variableType);
    	}).toList();
		
		final GetVariablesRequest payload = new GetVariablesRequest(getVariableDataList);
		
		return sendCallMessageAndWaitForResponse(chargingStationId, Action.GetVariables, payload);
	}
	
	@Override
	public Uni<ChangeAvailabilityResult> changeAvailability(final long chargingStationId, final Integer evseId, final Integer connectorId, 
			final AvailabilityStatus availabilityStatus) {
		
		final OperationalStatusEnumType operationalStatusEnumType = EnumConverter.convertAvailabilityStatus(availabilityStatus);
		
		final EVSEType eVSEType = createEVSEType(evseId, connectorId);
		
		final ChangeAvailabilityRequest payload = new ChangeAvailabilityRequest(operationalStatusEnumType, eVSEType);
		
		return sendCallMessageAndWaitForResponse(chargingStationId, Action.ChangeAvailability, payload);
	}
	
	@Override
	public Uni<ResetResult> reset(final long chargingStationId, final ResetType resetType, 
			final Integer evseId) {
		
		final ResetEnumType resetEnumType = EnumConverter.convertResetType(resetType);
		
		final ResetRequest payload = new ResetRequest(resetEnumType, evseId);
		
		return sendCallMessageAndWaitForResponse(chargingStationId, Action.Reset, payload);
	}

	@Override
	public Uni<RequestStartTransactionResult> requestStartTransaction(long chargingStationId, final Integer evseId, final long remoteStartId, 
			final IdToken idToken) {
		
		// TODO: construct charging profile
		
		final IdTokenEnumType idTokenEnumType = EnumConverter.convertIdTokenType(idToken.type());
		final Collection<AdditionalInfoType> additionalInfo = idToken.additionalIdTokens().stream()
				.map((additionalIdToken) -> new AdditionalInfoType(additionalIdToken.idToken(), additionalIdToken.type()))
				.toList();

        final IdTokenType idTokenType = new IdTokenType(idToken.idToken(), idTokenEnumType, additionalInfo);
        final RequestStartTransactionRequest request = new RequestStartTransactionRequest(evseId, remoteStartId, idTokenType, null, null);

        return sendCallMessageAndWaitForResponse(chargingStationId, Action.RequestStartTransaction, request);
	}

	@Override
	public Uni<RequestStopTransactionResult> requestStopTransaction(long chargingStationId, String transactionId) {
		
		final RequestStopTransactionRequest request = new RequestStopTransactionRequest(transactionId);
        return sendCallMessageAndWaitForResponse(chargingStationId, Action.RequestStopTransaction, request);
	}

	@Override
	public Uni<GetDiagnosticsResult> getDiagnostics(long chargingStationId, String location, Integer retries, Integer retryInterval, ZonedDateTime startTime, ZonedDateTime stopTime) {
		throw new IllegalStateException("Not implemented in ocpp 2.0.1");
	}

	@Override
	public Uni<TriggerMessageResult> triggerMessage(long chargingStationId, MessageTrigger messageTrigger, Integer connectorId) {
		throw new IllegalStateException("Not implemented in ocpp 2.0.1");
	}

	private Uni<Void> getBaseReport(final long chargingStationId, final long requestId,
    		final ReportBaseEnumType reportBaseEnumType) {
    	
    	final var request = new GetBaseReportRequest(requestId, reportBaseEnumType);
    	
        return sendCallMessage(chargingStationId, Action.GetBaseReport, request)
        		.replaceWithVoid();
    }
    
    private EVSEType createEVSEType(final Integer evseId, final Integer connectorId) {
		if (evseId != null) {
			return new EVSEType(evseId, connectorId);
		} else {
			return null;
		}
    }
    
    private Uni<Void> sendCallMessage(final long chargingStationId, final Action action,
    		final CSMSRequest request) {
    	
    	return sendCallMessage(chargingStationId, action, request, 
    			(ignored) -> Uni.createFrom().voidItem());
    }

    private <T> Uni<T> sendCallMessageAndWaitForResponse(final long chargingStationId, final Action action,
    		final CSMSRequest request) {
 
    	return sendCallMessage(chargingStationId, action, request, 
    			(message) -> {
    				final Uni<T> responseUni = Uni.createFrom().emitter((emitter) -> {
						final MessageConsumer<T> consumer = this.bus.consumer(Utils.getSynchronousMessageId(chargingStationId, message.getMessageId()));
    					emitter.onTermination(() -> { 
        	    			consumer.unregisterAndForget();
        	    		});
        	    		consumer.handler((responseMessage) -> {
        	    			emitter.complete(responseMessage.body());
        	    		});
    				});
    				
    				return responseUni
    						.ifNoItem()
    						.after(Duration.ofSeconds(CALL_MESSAGE_RESPONSE_TIMEOUT))
    						.failWith(new RuntimeException("Call message response timeout!"));
    			});
    }
    
    private <T> Uni<T> sendCallMessage(final long chargingStationId, final Action action,
    		final CSMSRequest request, final Function<SendingCallMessage<Action>, Uni<T>> responseUniProvider) {
    	
    	
    	final String messageId =  UUID.randomUUID().toString();

        final SendingCallMessage<Action> message = new SendingCallMessage<Action>(messageId, action, request);
        
        csmsRequests.computeIfAbsent(chargingStationId,
                k -> new HashMap<>()).computeIfAbsent(messageId, k -> new CSMSRequestWithAction(action, messageCodec.encode(request)));
        
        final Uni<T> responseUni = responseUniProvider.apply(message);

		final Uni<Void> sendRequestUni = this.serverSocket.sendMessage(chargingStationId, message);
 
        return (Uni<T>) Uni.combine().all().unis(responseUni, sendRequestUni).usingConcurrencyOf(2).with((list) -> list.getFirst());
    }

	public Set<SecurityProfile> enabledSecurityProfiles(final String chargingStationId) {
		return ENABLED_SECURITY_PROFILES;
	}
	
	private record CSMSRequestWithAction(Action action, String request) {}
}