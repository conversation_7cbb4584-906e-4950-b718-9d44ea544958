package org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.handlers.message;

import org.galiasystems.csms.cs.adapters.ocpp.handlers.message.MessageHandler;
import org.galiasystems.csms.cs.adapters.ocpp.messages.ReceivedCallMessage;
import org.galiasystems.csms.cs.adapters.ocpp.messages.SendingCallResultMessage;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.handlers.payload.BootNotificationHandler;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.handlers.payload.HeartbeatHandler;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.handlers.payload.MeterValuesHandler;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.handlers.payload.NotifyEventHandler;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.handlers.payload.NotifyReportHandler;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.handlers.payload.StatusNotificationHandler;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.handlers.payload.TransactionEventHandler;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.messages.Action;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.handlers.payload.AuthorizeHandler;

import io.smallrye.mutiny.Uni;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;

@Singleton
public class CallMessageHandler extends MessageHandler<ReceivedCallMessage, SendingCallResultMessage>  {	
	
	@Inject
	private BootNotificationHandler bootNotificationHandler;
	
	@Inject
	private HeartbeatHandler heartbeatHandler;
	
	@Inject
	private NotifyEventHandler notifyEventHandler;
	
	@Inject
	private NotifyReportHandler notifyReportHandler;
	
	@Inject
	private StatusNotificationHandler statusNotificationHandler;
	
	@Inject
	private TransactionEventHandler transactionEventHandler;

	@Inject
	private AuthorizeHandler authorizeHandler;
	
	@Inject
	private MeterValuesHandler meterValuesHandler;
	
	public Uni<SendingCallResultMessage> onMessage(final Long chargingStationId, final ReceivedCallMessage message) {
		final Action action = Action.valueOf(message.getAction());
		
		final Uni<?> payloadResponse;
		switch(action) {
			case BootNotification: {
				payloadResponse = this.bootNotificationHandler.onMessage(chargingStationId, message.getMessageId(), message.getPayload());
				break;
			}
			case Heartbeat: {
				payloadResponse = this.heartbeatHandler.onMessage(chargingStationId, message.getMessageId(), message.getPayload());
				break;
			}
			case NotifyReport: {
				payloadResponse = this.notifyReportHandler.onMessage(chargingStationId, message.getMessageId(), message.getPayload());
				break;
			}
			case StatusNotification: {
				payloadResponse = this.statusNotificationHandler.onMessage(chargingStationId, message.getMessageId(), message.getPayload());
				break;
			}
			case NotifyEvent: {
				payloadResponse = this.notifyEventHandler.onMessage(chargingStationId, message.getMessageId(), message.getPayload());
				break;
			}
			case TransactionEvent: {
				payloadResponse = this.transactionEventHandler.onMessage(chargingStationId, message.getMessageId(), message.getPayload());
				break;
			}
			case Authorize: {
				payloadResponse = this.authorizeHandler.onMessage(chargingStationId, message.getMessageId(), message.getPayload());
				break;
			}
			case MeterValues: {
				payloadResponse = this.meterValuesHandler.onMessage(chargingStationId, message.getMessageId(), message.getPayload());
				break;
			}
			default: {
				throw new IllegalStateException("Unknown action: " + action);
			}
		}
		
		return payloadResponse
				.onItem()
				.transform(response -> new SendingCallResultMessage(message.getMessageId(), response)); 
	}
}