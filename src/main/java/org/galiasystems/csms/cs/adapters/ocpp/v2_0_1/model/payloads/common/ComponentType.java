package org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.common;

/**
 * A physical or logical component.
 *
 * @param name     Required. Name of the component.
 *                 Name should be taken from the list of standardized component names whenever possible.
 *                 Case Insensitive. strongly advised to use Camel Case.
 *                 <p>
 *                 Field Type: identifierString[0..50]
 *                 Cardinality: 1..1
 *                 
 * @param instance Optional. Name of instance in case the component exists as multiple instances.
 *                 Case Insensitive. strongly advised to use Camel Case.
 *                 <p>
 *                 Field Type: identifierString[0..50]
 *                 Cardinality: 0..1
 *                 
 * @param evse     Optional. Specifies the EVSE when component is located at EVSE level,
 *                 also specifies the connector when component is located at Connector level.
 *                 <p>
 *                 Field Type: EVSEType
 *                 Cardinality: 0..1
 */
public record ComponentType(String name, String instance, EVSEType evse) {
}