package org.galiasystems.csms.cs.adapters.ocpp.v1_6.model.payloads.remote.startTransaction;

/**
 * Charging schedule period structure defines a time period in a charging schedule, as used in: ChargingSchedule.
 *
 * @param startPeriod  Required. Start of the period, in seconds from the start of schedule. The value of
 *                     StartPeriod also defines the stop time of the previous period.
 *                     Field type: integer
 *                     Cardinality: 1..1
 * @param limit        Required. Charging rate limit during the schedule period, in the applicable
 *                     chargingRateUnit, for example in Amperes or Watts. Accepts at most one digit
 *                     fraction (e.g. 8.1).
 *                     Field type: decimal
 *                     Cardinality: 1..1
 * @param numberPhases Optional. The number of phases that can be used for charging. If a number of
 *                     phases is needed, numberPhases=3 will be assumed unless another number is
 *                     given.
 *                     Field type: integer
 *                     Cardinality: 0..1
 */
public record ChargingSchedulePeriod(int startPeriod, float limit, Integer numberPhases) {
}