package org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.common;

/**
 * Status of an authorization response.
 */
public enum AuthorizationStatusEnumType {

	/**
	 * Identifier is allowed for charging.
	 */
	Accepted,
	
	/**
	 * Identifier has been blocked. Not allowed for charging.
	 */
	Blocked,
	
	/**
	 * Identifier is already involved in another transaction and multiple transactions are not allowed. 
	 * (Only relevant for the response to a transactionEventRequest(eventType=Started).)
	 */
	ConcurrentTx,
	
	/**
	 * Identifier has expired. Not allowed for charging.
	 */
	Expired,
	
	/**
	 * Identifier is invalid. Not allowed for charging.
	 */
	Invalid,
	
	/**
	 * Identifier is valid, but EV Driver doesn’t have enough credit to start charging. 
	 * Not allowed for charging.
	 */
	NoCredit,
	
	/**
	 * Identifier is valid, but not allowed to charge at this type of EVSE.
	 */
	NotAllowedTypeEVSE,
	
	/**
	 * Identifier is valid, but not allowed to charge at this location.
	 */
	NotAtThisLocation,
	
	/**
	 * Identifier is valid, but not allowed to charge at this location at this time.
	 */
	NotAtThisTime,
	
	/**
	 * Identifier is unknown. Not allowed for charging.
	 */
	Unknown,
	;
}