package org.galiasystems.csms.cs.adapters.ocpp.v1_6.model.payloads.heartbeat;

import java.time.ZonedDateTime;

import org.galiasystems.csms.cs.adapters.ocpp.payloads.CSMSResponse;

/**
 * Heartbeat.conf PDU sent by the Central System to the Charge Point in response to a Heartbeat.req PDU.
 *
 * @param currentTime Required. This contains the current time of the Central System.
 *                    <p>
 *                    Field Type: dateTime
 *                    Cardinality: 1..1
 */
public record HeartbeatResponse(ZonedDateTime currentTime) implements CSMSResponse{
}