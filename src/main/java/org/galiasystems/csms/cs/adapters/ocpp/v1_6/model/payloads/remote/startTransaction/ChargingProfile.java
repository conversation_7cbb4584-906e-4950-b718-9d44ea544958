package org.galiasystems.csms.cs.adapters.ocpp.v1_6.model.payloads.remote.startTransaction;

import java.time.ZonedDateTime;

/**
 * consists of a ChargingSchedule, describing the amount of power or current that can be
 * delivered per time interval
 *
 * @param chargingProfileId          Required. Unique identifier for this profile.
 *                                   Field type: integer
 *                                   Cardinality: 1..1
 *                                   
 * @param transactionId              Optional. Only valid if ChargingProfilePurpose is set to TxProfile,
 *                                   the transactionId MAY be used to match the profile to a specific
 *                                   transaction.
 *                                   Field type: integer
 *                                   Cardinality: 0..1
 *                                   
 * @param stackLevel                 Required. Value determining level in hierarchy stack of profiles.
 *                                   Higher values have precedence over lower values. Lowest level is
 *                                   0.
 *                                   Field type: integer>=0
 *                                   Cardinality: 1..1
 *                                   
 * @param chargingProfilePurposeType Required. Defines the purpose of the schedule transferred by this
 *                                   message.
 *                                   Field type: ChargingProfilePurposeType
 *                                   Cardinality: 1..1
 *                                   
 * @param chargingProfileKingType    Required. Indicates the kind of schedule.
 *                                   Field type: ChargingProfileKingType
 *                                   Cardinality: 1..1
 *                                   
 * @param recurrencyKindType         Optional. Indicates the start point of a recurrence.
 *                                   Field type: RecurrencyKindType
 *                                   Cardinality: 0..1
 *                                   
 * @param validFrom                  Optional. Point in time at which the profile starts to be valid. If
 *                                   absent, the profile is valid as soon as it is received by the Charge
 *                                   Point.
 *                                   Field type: dateTime
 *                                   Cardinality: 0..1
 *                                   
 * @param validTo                    Optional. Point in time at which the profile stops to be valid. If
 *                                   absent, the profile is valid until it is replaced by another profile.
 *                                   Field type: dateTime
 *                                   Cardinality: 0..1
 *                                   
 * @param chargingSchedule           Required. Contains limits for the available power or current over
 *                                   time.
 *                                   Field type: ChargingSchedule
 *                                   Cardinality: 0..1
 */
public record ChargingProfile(int chargingProfileId, Long transactionId, int stackLevel,
                              ChargingProfilePurposeType chargingProfilePurposeType,
                              ChargingProfileKindType chargingProfileKingType, RecurrencyKindType recurrencyKindType,
                              ZonedDateTime validFrom, ZonedDateTime validTo, ChargingSchedule chargingSchedule) {
}