package org.galiasystems.csms.cs.adapters.ocpp.v1_6.model.payloads.authorize;

import org.galiasystems.csms.cs.adapters.ocpp.v1_6.model.payloads.common.IdTagInfo;

/**
 * sent by the Central System to the Charge Point in response to a Authorize.req PDU
 *
 * @param idTagInfo Required. This contains information about authorization status, expiry and
 *                  parent id.
 *                  Field type: IdTagInfo
 *                  Cardinality: 1..1
 */
public record AuthorizeResponse(IdTagInfo idTagInfo) {
}