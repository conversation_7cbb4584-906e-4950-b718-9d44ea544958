package org.galiasystems.csms.cs.adapters.ocpp.websocket;

import java.io.IOException;

import org.galiasystems.csms.cs.adapters.ocpp.messages.CallErrorMessage;
import org.galiasystems.csms.cs.adapters.ocpp.messages.Message;
import org.galiasystems.csms.cs.adapters.ocpp.messages.MessageType;
import org.galiasystems.csms.cs.adapters.ocpp.messages.ReceivedCallMessage;
import org.galiasystems.csms.cs.adapters.ocpp.messages.ReceivedCallResultMessage;
import org.jboss.logging.Logger;

import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.ObjectCodec;
import com.fasterxml.jackson.core.Version;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.deser.std.StdDeserializer;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.databind.node.ArrayNode;

import io.quarkus.jackson.ObjectMapperCustomizer;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;

@Singleton
public class RegisterMessageDeserializerModule implements ObjectMapperCustomizer  {

	@Inject
    private Logger log;
	
	
	@Override
	public void customize(final ObjectMapper objectMapper) {
		final SimpleModule module =
				  new SimpleModule("MessageDeserializer", new Version(1, 0, 0, null, null, null));
		objectMapper.setSerializationInclusion(Include.NON_NULL);
		module.addDeserializer(Message.class, new MessageDeserializer());
		
		objectMapper.registerModule(module);
	}
	
	private class MessageDeserializer extends StdDeserializer<Message> {
		
		private static final long serialVersionUID = 1L;
		
		private static final int INDEX_MESSAGE_TYPE_ID = 0;
		private static final int INDEX_MESSAGE_ID = 1;

		private static final int INDEX_CALL_ACTION = 2;
		private static final int INDEX_CALL_PAYLOAD = 3;

		private static final int INDEX_CALL_RESULT_PAYLOAD = 2;

		private static final int INDEX_CALL_ERROR_ERRORCODE = 2;
	  	private static final int INDEX_CALL_ERROR_DESCRIPTION = 3;
	  	private static final int INDEX_CALL_ERROR_PAYLOAD = 4;
		
		protected MessageDeserializer() {
	        super(Message.class);
	    }


		@Override
		public Message deserialize(final JsonParser parser, final DeserializationContext deserializer) throws IOException {
			final ObjectCodec codec = parser.getCodec();
	        final JsonNode node = codec.readTree(parser);
	        
	        if (!node.isArray()) {
	        	throw new IllegalStateException("Invalid message received! message = " + node.toString());
	        }
	        
	        final ArrayNode message = (ArrayNode) node;
	        
	        if (RegisterMessageDeserializerModule.this.log.isDebugEnabled()) {
	        	RegisterMessageDeserializerModule.this.log.debug("Websocket message received: " + message.toString());
	        }
	        
			final int messageTypeId = message.get(INDEX_MESSAGE_TYPE_ID).asInt();
			final String messageId = message.get(INDEX_MESSAGE_ID).asText();
			final MessageType messageType = MessageType.getMessageTypeById(messageTypeId);
			
			switch(messageType) {
				case MessageType.CallMessage: {
					final String actionAsString = message.get(INDEX_CALL_ACTION).asText();			
					final String payload = message.get(INDEX_CALL_PAYLOAD).toString();
					
					return new ReceivedCallMessage(messageId, actionAsString, payload);
				}
				case MessageType.CallResultMessage: {
					final String payload = message.get(INDEX_CALL_RESULT_PAYLOAD).toString();
					
					return new ReceivedCallResultMessage(messageId, payload);
				}
				case MessageType.CallErrorMessage: {
					final String errorCode = message.get(INDEX_CALL_ERROR_ERRORCODE).asText();
					final String errorDescription = message.get(INDEX_CALL_ERROR_DESCRIPTION).asText();
					final String errorDetails = message.get(INDEX_CALL_ERROR_PAYLOAD).toString();
					
					return new CallErrorMessage(messageId, errorCode, errorDescription, errorDetails);
				}
				default:
					throw new IllegalStateException("Invalid message type: " + messageType);
			}
		}
		
	}

}