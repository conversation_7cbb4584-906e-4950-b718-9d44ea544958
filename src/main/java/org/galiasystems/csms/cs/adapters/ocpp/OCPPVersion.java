package org.galiasystems.csms.cs.adapters.ocpp;

import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;

public enum OCPPVersion {

    OCPP_2_0_1("ocpp2.0.1"),
    OCPP_1_6("ocpp1.6");

    private final String version;

    OCPPVersion(String version) {
        this.version = version;
    }

    public String getVersion() {
        return version;
    }

    public static Optional<String> getLatestSupportedVersion(final List<String> versions) {
        return versions.stream()
                .filter(version -> Arrays.stream(OCPPVersion.values())
                        .anyMatch(ocppVersion -> ocppVersion.getVersion().equals(version)))
                .max(Comparator.naturalOrder());
    }


}