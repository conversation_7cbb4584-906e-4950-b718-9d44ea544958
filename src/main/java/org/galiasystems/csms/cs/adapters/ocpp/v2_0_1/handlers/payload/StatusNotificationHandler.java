package org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.handlers.payload;

import java.time.ZonedDateTime;

import org.galiasystems.csms.cs.ChargingStationAdapterProxy;
import org.galiasystems.csms.cs.adapters.ocpp.handlers.payload.RequestPayloadHandler;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.statusNotification.StatusNotificationRequest;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.statusNotification.StatusNotificationResponse;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.utils.EnumConverter;
import org.galiasystems.csms.management.model.enums.ConnectorStatus;

import io.smallrye.mutiny.Uni;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;

@Singleton
public class StatusNotificationHandler extends RequestPayloadHandler<StatusNotificationRequest, StatusNotificationResponse> {

	@Inject
	private ChargingStationAdapterProxy chargingStationAdapterProxy;
	
	@Override
	protected Class<StatusNotificationRequest> getRequestPayloadType() {
		return StatusNotificationRequest.class;
	}

	@Override
	protected Uni<StatusNotificationResponse> onMessage(final long chargingStationId, final String messageId,
			final StatusNotificationRequest requestPayload) {
		
		final ConnectorStatus connectorStatus = EnumConverter.convertConnectorStatusEnumType(requestPayload.connectorStatus());
		
		return chargingStationAdapterProxy.connectorStatusReceived(chargingStationId, 
					requestPayload.evseId(), requestPayload.connectorId(), connectorStatus, ZonedDateTime.now(), null, null, null, null)
				.onItem()
				.transform((ignored) -> new StatusNotificationResponse());
	}

}
