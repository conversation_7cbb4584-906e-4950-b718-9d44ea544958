package org.galiasystems.csms.cs.adapters.ocpp.messages;

public abstract class MessageWithPayload<P> extends Message {
	
	/** 
	 * JSON Payload of the action.
	 * see: JSON Payload for more information in OCPP-2.0.1_part4_ocpp-j-specification.pdf
	 * 
	 * Datatype: JSON
	 */
	private final P payload;

	protected MessageWithPayload(final MessageType messageType, final String messageId,
			final P payload) {
		super(messageType, messageId);
		this.payload = payload;
	}

	public P getPayload() {
		return payload;
	}

	@Override
	public String toString() {
		return "MessageWithPayload [payload=" + payload.toString() + " ]" + super.toString();
	}
}