package org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.common;

/**
 * Format of a message to be displayed on the display of the Charging Station.
 */
public enum MessageFormatEnumType {
	
	/**
	 * Message content is ASCII formatted, only printable ASCII allowed.
	 */
	ASCII,
	
	/**
	 * Message content is HTML formatted.
	 */
	HTML,
	
	/**
	 * Message content is URI that Charging Station should download and use to display. 
	 * for example a HTML page to be shown in a web-browser.
	 */
	URI,
	
	/**
	 * Message content is UTF-8 formatted.
	 */
	UTF8,
	;
}
