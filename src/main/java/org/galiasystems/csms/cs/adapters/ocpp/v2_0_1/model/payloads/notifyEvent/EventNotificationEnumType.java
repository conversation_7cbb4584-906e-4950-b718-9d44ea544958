package org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.notifyEvent;

/**
 * The event notification type of event data.
 */
public enum EventNotificationEnumType {

	/**
	 * The software implemented by the manufacturer triggered a hardwired notification.
	 */
	HardWiredNotification,
	
	/**
	 * Triggered by a monitor, which is hardwired by the manufacturer.
	 */
	HardWiredMonitor,
	
	/**
	 * Triggered by a monitor, which is preconfigured by the manufacturer.
	 */
	PreconfiguredMonitor,
	
	/**
	 * Triggered by a monitor, which is set with the setvariablemonitoringrequest message 
	 * by the Charging Station Operator.
	 */
	CustomMonitor,
	;
}