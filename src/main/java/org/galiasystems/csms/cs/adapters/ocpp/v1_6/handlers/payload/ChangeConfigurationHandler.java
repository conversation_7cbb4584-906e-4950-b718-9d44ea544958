package org.galiasystems.csms.cs.adapters.ocpp.v1_6.handlers.payload;

import io.smallrye.mutiny.Uni;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import java.util.List;

import org.galiasystems.csms.cs.ChargingStationAdapterProxy;
import org.galiasystems.csms.cs.adapters.ocpp.handlers.payload.ResponsePayloadHandler;
import org.galiasystems.csms.cs.adapters.ocpp.v1_6.model.payloads.changeConfiguration.ChangeConfigurationRequest;
import org.galiasystems.csms.cs.adapters.ocpp.v1_6.model.payloads.changeConfiguration.ChangeConfigurationResponse;
import org.galiasystems.csms.cs.adapters.ocpp.v1_6.utils.EnumConverter;
import org.galiasystems.csms.management.types.SetVariableResult;
import org.galiasystems.csms.management.types.enums.SetVariableResultStatus;

@Singleton
public class ChangeConfigurationHandler extends ResponsePayloadHandler<ChangeConfigurationRequest, ChangeConfigurationResponse> {

    @Inject
    private ChargingStationAdapterProxy chargingStationAdapterProxy;

    @Override
    protected Class<ChangeConfigurationRequest> getRequestPayloadType() {
        return ChangeConfigurationRequest.class;
    }

    @Override
    protected Class<ChangeConfigurationResponse> getResponsePayloadType() {
        return ChangeConfigurationResponse.class;
    }

    @Override
    protected Uni<Void> onMessage(final long chargingStationId, final String messageId, final ChangeConfigurationResponse responsePayload, 
    		final ChangeConfigurationRequest requestPayload) {

        final SetVariableResultStatus setVariableResultStatus = EnumConverter.convertConfigurationStatus(responsePayload.status());
        
        final SetVariableResult setVariableResult = new SetVariableResult(setVariableResultStatus, null, null, null, null, null, 
        		requestPayload.key(), null, null, null);

        return this.chargingStationAdapterProxy.setVariableResultReceived(chargingStationId, List.of(setVariableResult))
                .invoke((ignored) -> super.sendResponseMessage(chargingStationId, messageId, setVariableResult));
    }
}