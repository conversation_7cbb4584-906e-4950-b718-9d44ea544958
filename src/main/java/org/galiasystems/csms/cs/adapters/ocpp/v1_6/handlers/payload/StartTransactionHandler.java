package org.galiasystems.csms.cs.adapters.ocpp.v1_6.handlers.payload;

import io.smallrye.mutiny.Uni;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import org.galiasystems.csms.cs.ChargingStationAdapterProxy;
import org.galiasystems.csms.cs.adapters.ocpp.handlers.payload.RequestPayloadHandler;
import org.galiasystems.csms.cs.adapters.ocpp.v1_6.model.payloads.common.IdTagInfo;
import org.galiasystems.csms.cs.adapters.ocpp.v1_6.model.payloads.startTransaction.StartTransactionRequest;
import org.galiasystems.csms.cs.adapters.ocpp.v1_6.model.payloads.startTransaction.StartTransactionResponse;
import org.galiasystems.csms.cs.adapters.ocpp.v1_6.utils.Constants;
import org.galiasystems.csms.management.model.EvseTransactionEvent;
import org.galiasystems.csms.management.model.MeterValue;
import org.galiasystems.csms.management.model.SampledValue;
import org.galiasystems.csms.management.model.enums.TransactionChargingState;
import org.galiasystems.csms.management.model.enums.TransactionEventTriggerReason;
import org.galiasystems.csms.management.model.enums.TransactionEventType;

import org.galiasystems.csms.management.types.TransactionEventResult;
import org.galiasystems.csms.management.types.enums.AuthorizationStatus;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Date;
import java.util.Set;

@Singleton
public class StartTransactionHandler extends RequestPayloadHandler<StartTransactionRequest, StartTransactionResponse> {


    @Inject
    ChargingStationAdapterProxy chargingStationAdapterProxy;

    @Override
    protected Class<StartTransactionRequest> getRequestPayloadType() {
        return StartTransactionRequest.class;
    }

    @Override
    protected Uni<StartTransactionResponse> onMessage(long chargingStationId, String messageId, StartTransactionRequest requestPayload) {
        final int chargingStationEvseId = requestPayload.connectorId();

        return this.chargingStationAdapterProxy.getNextEvseTransactionId()
                .flatMap(transactionId ->
                        chargingStationAdapterProxy.transactionEventReceived(chargingStationId, chargingStationEvseId, 
                        		Constants.DEFAULT_EVSE_CONNECTOR_ID, String.valueOf(transactionId), this.createTransactionEvent(requestPayload))
                        .onItem().transform(transactionEventResult -> this.createTransactionEventResponse(transactionEventResult, transactionId))
                );
    }

    private EvseTransactionEvent createTransactionEvent(StartTransactionRequest requestPayload) {
    	
    	final SampledValue sampledValue = SampledValue.builder().value(BigDecimal.valueOf(requestPayload.meterStart())).build();
    	final MeterValue meterValue = new MeterValue(requestPayload.timestamp(), Set.of(sampledValue));
    	
        return EvseTransactionEvent.builder().eventType(TransactionEventType.Started)
                .timestamp(Date.from(requestPayload.timestamp().toInstant()))
                .triggerReason(TransactionEventTriggerReason.Authorized)
                .eventSeqNo(1)
                .offline(false)
                .chargingState(TransactionChargingState.Charging)
                .meterValues(Set.of(meterValue))
                .build();
    }

    private StartTransactionResponse createTransactionEventResponse(final TransactionEventResult transactionEventResult, long transactionId) {

        return new StartTransactionResponse(
                new IdTagInfo(ZonedDateTime.now(), "", AuthorizationStatus.Accepted),
                transactionId);
    }

}