package org.galiasystems.csms.cs.adapters.ocpp.v1_6.model.payloads.common;

/**
 * Format that specifies how the value element in SampledValue is to be interpreted.
 */
public enum ValueFormat {
    /**
     * Data is to be interpreted as integer/decimal numeric data.
     */
    Raw,
    
    /**
     * Data is represented as a signed binary data block, encoded as hex data.
     */
    SignedData,
    ;
}