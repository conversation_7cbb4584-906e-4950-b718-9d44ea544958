package org.galiasystems.csms.cs.adapters.ocpp.v1_6.model.payloads.reset;

import org.galiasystems.csms.cs.adapters.ocpp.payloads.CSMSRequest;

/**
 * Reset.req PDU sent by the Central System to the Charge Point.
 * 
 * @param type              Required. This contains the type of reset that the Charge Point should perform.
 *                          <p>
 *                          Field Type: ResetType
 *                          Cardinality: 1..1
 */
public record ResetRequest(ResetType type) implements CSMSRequest {
}