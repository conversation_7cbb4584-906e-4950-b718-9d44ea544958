package org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.handlers.payload;

import java.util.Collection;

import org.galiasystems.csms.cs.ChargingStationAdapterProxy;
import org.galiasystems.csms.cs.adapters.ocpp.handlers.payload.RequestPayloadHandler;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.meterValues.MeterValuesRequest;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.meterValues.MeterValuesResponse;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.utils.MeterValuesConverter;
import org.galiasystems.csms.management.model.MeterValue;

import io.smallrye.mutiny.Uni;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;

@Singleton
public class MeterValuesHandler extends RequestPayloadHandler<MeterValuesRequest, MeterValuesResponse> {

	@Inject
	private ChargingStationAdapterProxy chargingStationAdapterProxy;
	
	@Override
	protected Class<MeterValuesRequest> getRequestPayloadType() {
		return MeterValuesRequest.class;
	}

	@Override
	protected Uni<MeterValuesResponse> onMessage(final long chargingStationId, final String messageId,
			final MeterValuesRequest requestPayload) {
		
		final Collection<MeterValue> meterValues = MeterValuesConverter.createMeterValues(requestPayload.meterValue());
		
        return this.chargingStationAdapterProxy.meterValueReceived(chargingStationId, requestPayload.evseId(), meterValues)
        		.onItem()
        		.transform(unused -> new MeterValuesResponse());
	}
}