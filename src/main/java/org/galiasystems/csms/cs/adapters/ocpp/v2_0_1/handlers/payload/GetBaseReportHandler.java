package org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.handlers.payload;

import org.galiasystems.csms.cs.ChargingStationAdapterProxy;
import org.galiasystems.csms.cs.adapters.ocpp.handlers.payload.ResponsePayloadHandler;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.common.StatusInfoType;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.getBaseReport.GetBaseReportRequest;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.getBaseReport.GetBaseReportResponse;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.utils.EnumConverter;
import org.galiasystems.csms.management.types.ReportRequestResult;
import org.galiasystems.csms.management.types.enums.ReportResultStatus;

import io.smallrye.mutiny.Uni;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;

@Singleton
public class GetBaseReportHandler extends ResponsePayloadHandler<GetBaseReportRequest, GetBaseReportResponse> {
	
	@Inject
	private ChargingStationAdapterProxy chargingStationAdapterProxy;
	
	@Override
	protected Class<GetBaseReportRequest> getRequestPayloadType() {
		return GetBaseReportRequest.class;
	}
	
	@Override
	protected Class<GetBaseReportResponse> getResponsePayloadType() {
		return GetBaseReportResponse.class;
	}

	@Override
	protected Uni<Void> onMessage(final long chargingStationId, final String messageId,
			final GetBaseReportResponse responsePayload, final GetBaseReportRequest requestPayload) {
	
		final ReportResultStatus reportResultStatus = EnumConverter.convertGenericDeviceModelStatusEnumType(responsePayload.status());
		
		final StatusInfoType statusInfo = responsePayload.statusInfo();
		final String reasonCode;
		final String additionalInfo;
		if (statusInfo != null) {
			reasonCode = statusInfo.reasonCode();
			additionalInfo = statusInfo.additionalInfo();
		} else {
			reasonCode = null;
			additionalInfo = null;
		}
			
		final ReportRequestResult reportResult = new ReportRequestResult(reportResultStatus, reasonCode, additionalInfo);
		
		return this.chargingStationAdapterProxy.reportResponseReceived(chargingStationId, requestPayload.requestId(), reportResult);
		
	}
}