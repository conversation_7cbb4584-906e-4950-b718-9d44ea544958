package org.galiasystems.csms.cs.adapters.ocpp.v1_6.model.payloads.bootNotification;

public enum RegistrationStatus {
    /**
     * Charge point is accepted by Central System.
     */
    Accepted,
    /**
     * Central System is not yet ready to accept the Charge Point. Central System may send messages to retrieve information or
     * prepare the Charge Point.
     */
    Pending,
    /**
     * Charge point is not accepted by Central System. This may happen when the Charge Point id is not known by Central System.
     */
    Rejected
}