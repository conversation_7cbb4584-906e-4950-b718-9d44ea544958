package org.galiasystems.csms.cs.adapters.ocpp.v1_6.model.payloads.authorize;

import org.galiasystems.csms.cs.adapters.ocpp.payloads.CSRequest;

/**
 * sent by the Charge Point to the Central System
 *
 * @param idTag Required. This contains the identifier that needs to be authorized.
 *              Field type: IdToken (String, length 20, case insensitive)
 *              Cardinality: 1..1
 */
public record AuthorizeRequest(String idTag) implements CSRequest {
}