package org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.bootNotification;

import org.galiasystems.csms.cs.adapters.ocpp.payloads.CSRequest;

/**
 * BootNotificationRequest PDU sent by the Charging Station to the CSMS.
 *
 * @param reason          Required. This contains the reason for sending this message to the CSMS.
 *                        <p>
 *                        Field Type: BootReasonEnumType
 *                        Cardinality: 1..1
 *                        
 * @param chargingStation Required. Identifies the Charging Station
 *                        <p>
 *                        Field Type: ChargingStationType
 *                        Cardinality: 1..1
 */
public record BootNotificationRequest(BootReasonEnumType reason,
                                      ChargingStationType chargingStation) implements CSRequest {
}