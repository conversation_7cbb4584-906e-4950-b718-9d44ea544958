package org.galiasystems.csms.cs.adapters.ocpp.v1_6.model.payloads.common;

import java.time.ZonedDateTime;
import java.util.Collection;

/**
 * Collection of one or more sampled values in MeterValues.req and StopTransaction.req. All sampled values in a
 * MeterValue are sampled at the same point in time.
 *
 * @param timestamp    Required. Timestamp for measured value(s)
 *                     Field type: dateTime
 *                     Cardinality: 1..1
 *                     
 * @param sampledValue Required. One or more measured values
 *                     Field type: SampledValue
 *                     Cardinality: 1..*
 */
public record MeterValue(ZonedDateTime timestamp, Collection<SampledValue> sampledValue) {
}