package org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.common;

import java.math.BigDecimal;

/**
 * Single sampled value in MeterValues. Each value can be accompanied by optional fields.
 * 
 * To save on mobile data usage, default values of all of the optional fields are such that. 
 * The value without any additional fields will be interpreted, 
 * as a register reading of active import energy in Wh (Watt-hour) units.
 * 
 * @param value         	  Required. Indicates the measured value.
 *                            <p>
 *                            Field Type: decimal
 *                            Cardinality: 1..1
 *                            
 * @param context         	  Optional. Type of detail value: start, end or sample. Default = "Sample.Periodic".
 *                            <p>
 *                            Field Type: ReadingContextEnumType
 *                            Cardinality: 0..1
 *                            
 * @param measurand           Optional. Type of measurement. Default = "Energy.Active.Import.Register".
 *                            <p>
 *                            Field Type: MeasurandEnumType
 *                            Cardinality: 0..1
 *                            
 * @param phase               Optional. Indicates how the measured value is to be interpreted. 
 * 							  For instance between L1 and neutral (L1-N) Please note that not all values of phase are applicable to all Measurands. 
 * 							  When phase is absent, the measured value is interpreted as an overall value.
 *                            <p>
 *                            Field Type: PhaseEnumType
 *                            Cardinality: 0..1
 *                            
 * @param location         	  Optional. Indicates where the measured value has been sampled. Default = "Outlet".
 *                            <p>
 *                            Field Type: LocationEnumType
 *                            Cardinality: 0..1
 *                            
 * @param signedMeterValue    Optional. Contains the MeterValueSignature with sign/encoding method information.
 *                            <p>
 *                            Field Type: SignedMeterValueType
 *                            Cardinality: 0..1
 *                            
 * @param unitOfMeasure       Optional. Represents a UnitOfMeasure including a multiplier.
 *                            <p>
 *                            Field Type: UnitOfMeasureType
 *                            Cardinality: 0..1
 */
public record SampledValueType(BigDecimal value, ReadingContextEnumType context, MeasurandEnumType measurand, PhaseEnumType phase,
		LocationEnumType location, SignedMeterValueType signedMeterValue, UnitOfMeasureType unitOfMeasure) {
}