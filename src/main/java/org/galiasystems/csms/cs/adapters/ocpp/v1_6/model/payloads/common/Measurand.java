package org.galiasystems.csms.cs.adapters.ocpp.v1_6.model.payloads.common;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 * Allowable values of the optional "measurand" field of a Value element, as used in MeterValues.req and
 * StopTransaction.req messages. Default value of "measurand" is always "Energy.Active.Import.Register"
 * 
 * Import is energy flow from the Grid to the Charge Point, EV or other load. Export is energy flow
 * from the EV to the Charge Point and/or from the Charge Point to the Grid.
 * 
 * All "Register" values relating to a single charging transaction, or a non-transactional consumer
 * (e.g. charge point internal power supply, overall supply) MUST be monotonically increasing in time.
 * 
 * The actual quantity of energy corresponding to a reported ".Register" value is computed as
 * the register value in question minus the register value recorded/reported at the start of the transaction 
 * or other relevant starting reference point in time. 
 * For improved auditability, ".Register" values SHOULD reported exactly as they are directly read 
 * from a non-volatile register in the electrical metering hardware, 
 * and SHOULD NOT be re-based to zero at the start of transactions. 
 * This allows any "missing energy" between sequential transactions, due to hardware fault, mis-wiring, fraud, etc. to be identified, 
 * by allowing the Central System to confirm that the starting register value of any transaction is identical 
 * to the finishing register value of the preceding transaction on the same connector.
 */
public enum Measurand {
    /**
     * Instantaneous current flow from EV
     */
    Current_Export("Current.Export"),
    
    /**
     * Instantaneous current flow to EV
     */
    Current_Import("Current.Import"),
    
    /**
     * Maximum current offered to EV
     */
    Current_Offered("Current.Offered"),
    
    /**
     * Numerical value read from the "active electrical energy" (Wh or kWh) register of the (most authoritative)
     * electrical meter measuring energy exported (to the grid).
     */
    Energy_Active_Export_Register("Energy.Active.Export.Register"),
    
    /**
     * Numerical value read from the "active electrical energy" (Wh or kWh) register of the (most authoritative)
     * electrical meter measuring energy imported (from the grid supply).
     */
    Energy_Active_Import_Register("Energy.Active.Import.Register"),
    
    /**
     * Numerical value read from the "reactive electrical energy" (VARh or kVARh) register of the (most
     * authoritative) electrical meter measuring energy exported (to the grid).
     */
    Energy_Reactive_Export_Register("Energy.Reactive.Export.Register"),
    
    /**
     * Numerical value read from the "reactive electrical energy" (VARh or kVARh) register of the (most
     * authoritative) electrical meter measuring energy imported (from the grid supply).
     */
    Energy_Reactive_Import_Register("Energy.Reactive.Import.Register"),
    
    /**
     * Absolute amount of "active electrical energy" (Wh or kWh) exported (to the grid) during an associated time
     * "interval", specified by a Metervalues ReadingContext, and applicable interval duration configuration values
     * (in seconds) for "ClockAlignedDataInterval" and "MeterValueSampleInterval".
     */
    Energy_Active_Export_Interval("Energy.Active.Export.Interval"),
    
    /**
     * Absolute amount of "active electrical energy" (Wh or kWh) imported (from the grid supply) during an
     * associated time "interval", specified by a Metervalues ReadingContext, and applicable interval duration
     * configuration values (in seconds) for "ClockAlignedDataInterval" and "MeterValueSampleInterval".
     */
    Energy_Active_Import_Interval("Energy.Active.Import.Interval"),
    
    /**
     * Absolute amount of "reactive electrical energy" (VARh or kVARh) exported (to the grid) during an associated
     * time "interval", specified by a Metervalues ReadingContext, and applicable interval duration configuration
     * values (in seconds) for "ClockAlignedDataInterval" and "MeterValueSampleInterval".
     */
    Energy_Reactive_Export_Interval("Energy.Reactive.Export.Interval"),
    
    /**
     * Absolute amount of "reactive electrical energy" (VARh or kVARh) imported (from the grid supply) during an
     * associated time "interval", specified by a Metervalues ReadingContext, and applicable interval duration
     * configuration values (in seconds) for "ClockAlignedDataInterval" and "MeterValueSampleInterval".
     */
    Energy_Reactive_Import_Interval("Energy.Reactive.Import.Interval"),
    
    /**
     * Instantaneous reading of powerline frequency. NOTE: OCPP 1.6 does not have a UnitOfMeasure for
     * frequency, the UnitOfMeasure for any SampledValue with measurand: Frequency is Hertz.
     */
    Frequency("Frequency"),
    
    /**
     * Instantaneous active power exported by EV. (W or kW)
     */
    Power_Active_Export(("Power.Active.Export")),
    
    /**
     * Instantaneous active power imported by EV. (W or kW)
     */
    Power_Active_Import("Power.Active.Import"),
    
    /**
     * Instantaneous power factor of total energy flow
     */
    Power_Factor("Power.Factor"),
    
    /**
     * Maximum power offered to EV
     */
    Power_Offered("Power.Offered"),
    
    /**
     * Instantaneous reactive power exported by EV. (var or kvar)
     */
    Power_Reactive_Export("Power.Reactive.Export"),
    
    /**
     * Instantaneous reactive power imported by EV. (var or kvar)
     */
    Power_Reactive_Import("Power.Reactive.Import"),
    
    /**
     * Fan speed in RPM
     */
    RPM("RPM"),
    
    /**
     * State of charge of charging vehicle in percentage
     */
    SoC("SoC"),
    
    /**
     * Temperature reading inside Charge Point.
     */
    Temperature("Temperature"),
    
    /**
     * Instantaneous AC RMS supply voltage
     */
    Voltage("Voltage"),
    ;

    private final String jsonValue;

    Measurand(String jsonValue) {
        this.jsonValue = jsonValue;
    }

    @JsonValue
    public String getJsonValue() {
        return jsonValue;
    }

    @JsonCreator
    public static Measurand fromJson(String value) {
        for (Measurand e : values()) {
            if (e.jsonValue.equals(value)) {
                return e;
            }
        }
        throw new IllegalArgumentException("Unknown value: " + value);
    }

}