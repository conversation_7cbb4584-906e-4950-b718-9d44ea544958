package org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.transactionEvent;

/**
 * Reason for stopping a transaction.
 */
public enum ReasonEnumType {
	
	/**
	 * The transaction was stopped because of the authorization status in the response to a transactionEventRequest.
	 */
	DeAuthorized,
	
	/**
	 * Emergency stop button was used.
	 */
	EmergencyStop,
	
	/**
	 * EV charging session reached a locally enforced maximum energy transfer limit.
	 */
	EnergyLimitReached,
	
	/**
	 * Disconnecting of cable, vehicle moved away from inductive charge unit.
	 */
	EVDisconnected,
	
	/**
	 * A GroundFault has occurred.
	 */
	GroundFault,
	
	/**
	 * A Reset(Immediate) command was received.
	 */
	ImmediateReset,
	
	/**
	 * Stopped locally on request of the EV Driver at the Charging Station. 
	 * This is a regular termination of a transaction. 
	 * Examples: presenting an IdToken tag, pressing a button to stop.
	 */
	Local,
	
	/**
	 * A local credit limit enforced through the Charging Station has been exceeded.
	 */
	LocalOutOfCredit,
	
	/**
	 * The transaction was stopped using a token with a MasterPassGroupId.
	 */
	MasterPass,
	
	/**
	 * Any other reason.
	 */
	Other,
	
	/**
	 * A larger than intended electric current has occurred.
	 */
	OvercurrentFault,
	
	/**
	 * Complete loss of power.
	 */
	PowerLoss,
	
	/**
	 * Quality of power too low, e.g. voltage too low/high, phase imbalance, etc.
	 */
	PowerQuality,
	
	/**
	 * A locally initiated reset/reboot occurred. (for instance watchdog kicked in)
	 */
	Reboot,
	
	/**
	 * Stopped remotely on request of the CSMS. 
	 * This is a regular termination of a transaction. 
	 * Examples: termination using a smartphone app, exceeding a (non local) prepaid credit.
	 */
	Remote,
	
	/**
	 * Electric vehicle has reported reaching a locally enforced maximum battery State of Charge (SOC).
	 */
	SOCLimitReached,
	
	/**
	 * The transaction was stopped by the EV.
	 */
	StoppedByEV,
	
	/**
	 * EV charging session reached a locally enforced time limit.
	 */
	TimeLimitReached,
	
	/**
	 * EV not connected within timeout.
	 */
	Timeout,
	;
}