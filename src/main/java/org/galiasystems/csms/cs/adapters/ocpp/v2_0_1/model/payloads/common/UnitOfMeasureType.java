package org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.common;

/**
 * Represents a UnitOfMeasure with a multiplier of the SampledValueType.
 * 
 * @param unit         	      Optional. Unit of the value. Default = "Wh" if the (default) measurand is an "Energy" type. 
 * 							  This field SHALL use a value from the list Standardized Units of Measurements in Part 2 Appendices. 
 * 							  If an applicable unit is available in that list, otherwise a "custom" unit might be used.
 *                            <p>
 *                            Field Type: string[0..20]
 *                            Cardinality: 0..1
 *                            
 * @param multiplier          Optional. Multiplier, this value represents the exponent to base 10. 
 * 							  I.e. multiplier 3 means 10 raised to the 3rd power. Default is 0.
 *                            <p>
 *                            Field Type: integer
 *                            Cardinality: 0..1
 */
public record UnitOfMeasureType(String unit, Integer multiplier) {
}