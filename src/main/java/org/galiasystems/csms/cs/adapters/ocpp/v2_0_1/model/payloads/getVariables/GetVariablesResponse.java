package org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.getVariables;

import java.util.List;

import org.galiasystems.csms.cs.adapters.ocpp.payloads.CSResponse;

/**
 * GetVariablesResponse PDU sent by the Charging Station to the CSMS in response to GetVariablesRequest.
 *
 * @param getVariableResult Required. List of requested variables and their values.
 *                          <p>
 *                          Field Type: GetVariableResultType
 *                          Cardinality: 1..*
 */
public record GetVariablesResponse(List<GetVariableResultType> getVariableResult) implements CSResponse {
}