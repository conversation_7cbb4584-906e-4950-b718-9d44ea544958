package org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.handlers.payload;

import org.galiasystems.csms.cs.ChargingStationAdapterProxy;
import org.galiasystems.csms.cs.adapters.ocpp.handlers.payload.ResponsePayloadHandler;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.changeAvailability.ChangeAvailabilityRequest;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.changeAvailability.ChangeAvailabilityResponse;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.common.StatusInfoType;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.utils.EnumConverter;
import org.galiasystems.csms.management.types.ChangeAvailabilityResult;
import org.galiasystems.csms.management.types.enums.ChangeAvailabilityResultStatus;

import io.smallrye.mutiny.Uni;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;

@Singleton
public class ChangeAvailabilityHandler extends ResponsePayloadHandler<ChangeAvailabilityRequest, ChangeAvailabilityResponse> {

	@Inject
	private ChargingStationAdapterProxy chargingStationAdapterProxy;
	
	@Override
	protected Class<ChangeAvailabilityRequest> getRequestPayloadType() {
		return ChangeAvailabilityRequest.class;
	}

	@Override
	protected Class<ChangeAvailabilityResponse> getResponsePayloadType() {
		return ChangeAvailabilityResponse.class;
	}

	@Override
	protected Uni<Void> onMessage(final long chargingStationId, final String messageId, final ChangeAvailabilityResponse responsePayload,
			final ChangeAvailabilityRequest requestPayload) {
		
		final ChangeAvailabilityResultStatus changeAvailabilityResultStatus = EnumConverter.convertChangeAvailabilityStatusEnumType(responsePayload.status());
		
		final StatusInfoType statusInfo = responsePayload.statusInfo();
		final String reasonCode;
		final String additionalInfo;
		if (statusInfo != null) {
			reasonCode = statusInfo.reasonCode();
			additionalInfo = statusInfo.additionalInfo();
		} else {
			reasonCode = null;
			additionalInfo = null;
		}
		
		final ChangeAvailabilityResult changeAvailabilityResult = new ChangeAvailabilityResult(changeAvailabilityResultStatus, 
				reasonCode, additionalInfo);
		
		return this.chargingStationAdapterProxy.changeAvailabilityResultReceived(chargingStationId, changeAvailabilityResult)
				.invoke((ignored) -> super.sendResponseMessage(chargingStationId, messageId, changeAvailabilityResult));
	}
}