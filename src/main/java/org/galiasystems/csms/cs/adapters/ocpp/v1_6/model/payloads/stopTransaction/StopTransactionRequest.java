package org.galiasystems.csms.cs.adapters.ocpp.v1_6.model.payloads.stopTransaction;

import org.galiasystems.csms.cs.adapters.ocpp.payloads.CSRequest;
import org.galiasystems.csms.cs.adapters.ocpp.v1_6.model.payloads.common.MeterValue;

import java.time.ZonedDateTime;
import java.util.Collection;

/**
 * StopTransaction.req PDU sent by the Charge Point to the Central System.
 *
 * @param idTag           Optional. This contains the identifier which requested to stop the charging.
 *                        It is optional because a Charge Point may terminate charging without the presence of an idTag,
 *                        e.g., in case of a reset. A Charge Point SHALL send the idTag if known.
 *                        Field type: IdToken
 *                        Cardinality: 0..1
 *                        
 * @param meterStop       Required. This contains the meter value in Wh for the connector at the end of the transaction.
 *                        Field type: integer
 *                        Cardinality: 1..1
 *                        
 * @param timestamp       Required. This contains the date and time on which the transaction is stopped.
 *                        Field type: dateTime
 *                        Cardinality: 1..1
 *                        
 * @param transactionId   Required. This contains the transaction-id as received by the StartTransaction.conf.
 *                        Field type: integer
 *                        Cardinality: 1..1
 *                        
 * @param reason          Optional. This contains the reason why the transaction was stopped.
 *                        MAY only be omitted when the Reason is "Local".
 *                        Field type: Reason
 *                        Cardinality: 0..1
 *                        
 * @param transactionData Optional. This contains transaction usage details relevant for billing purposes.
 *                        Field type: List<MeterValue>
 *                        Cardinality: 0..*
 */
public record StopTransactionRequest(String idTag, int meterStop, ZonedDateTime timestamp, int transactionId,
        StopReason reason, Collection<MeterValue> transactionData) implements CSRequest {
}