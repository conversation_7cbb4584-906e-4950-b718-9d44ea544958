package org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.changeAvailability;

import org.galiasystems.csms.cs.adapters.ocpp.payloads.CSMSRequest;
import org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.common.EVSEType;

/**
 * ChangeAvailabilityRequest PDU sent by the CSMS to the Charging Station.
 * 
 * @param operationalStatus 	Required. This contains the type of availability change that the Charging Station should perform.
 *                          	<p>
 *                          	Field Type: OperationalStatusEnumType
 *                          	Cardinality: 1..1
 *                        
 * @param evse              	Optional. Contains Id’s to designate a specific EVSE/connector by index numbers. 
 *                          	When omitted, the message refers to the Charging Station as a whole.
 *                          	<p>
 *                          	Field Type: EVSEType
 *                          	Cardinality: 0..1
 */
public record ChangeAvailabilityRequest(OperationalStatusEnumType operationalStatus,
		EVSEType evse) implements CSMSRequest {
}