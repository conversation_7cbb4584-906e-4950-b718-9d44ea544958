package org.galiasystems.csms.cs.adapters.ocpp.v1_6.model.payloads.diagnostics;

import org.galiasystems.csms.cs.adapters.ocpp.payloads.CSMSRequest;

import java.time.ZonedDateTime;

/**
 * GetDiagnostics.req PDU sent by the Central System to the Charge Point.
 *
 * @param location      Required. This contains the location (directory) where the diagnostics file shall
 *                      be uploaded to.
 *                      Field type: string
 *                      Cardinality: 1..1
 * @param retries       Optional. This specifies how many times Charge Point must try to upload the
 *                      diagnostics before giving up. If this field is not present, it is left to Charge Point
 *                      to decide how many times it wants to retry
 *                      Field type: integer
 *                      Cardinality: 0..1
 * @param retryInterval Optional. The interval in seconds after which a retry may be attempted. If this
 *                      field is not present, it is left to Charge Point to decide how long to wait between
 *                      attempts.
 *                      Field type: integer
 *                      Cardinality: 0..1
 * @param startTime     Optional. This contains the date and time of the oldest logging information to
 *                      include in the diagnostics
 *                      Field type: dateTime
 *                      Cardinality: 0..1
 * @param stopTime      Optional. This contains the date and time of the latest logging information to
 *                      include in the diagnostics.
 *                      Field type: dateTime
 *                      Cardinality: 0..1
 */
public record GetDiagnosticsRequest(String location, Integer retries, Integer retryInterval, ZonedDateTime startTime,
                                    ZonedDateTime stopTime) implements CSMSRequest {
}