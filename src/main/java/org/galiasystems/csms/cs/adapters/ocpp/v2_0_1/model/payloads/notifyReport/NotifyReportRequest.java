package org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.notifyReport;

import java.time.ZonedDateTime;
import java.util.List;

import org.galiasystems.csms.cs.adapters.ocpp.payloads.CSRequest;

/**
 * NotifyReportRequest PDU sent by the Charging Station to the CSMS.
 * 
 * @param requestId   Required. The id of the GetReportRequest or GetBaseReportRequest that requested this report.
 *                    <p>
 *                    Field Type: integer
 *                    Cardinality: 1..1
 * 
 * @param generatedAt Required. Timestamp of the moment this message was generated at the Charging Station.
 *                    <p>
 *                    Field Type: dateTime
 *                    Cardinality: 1..1
 * 
 * @param tbc         Optional. “to be continued” indicator. 
 *                    Indicates whether another part of the report follows 
 *                    in an upcoming notifyReportRequest message. 
 *                    Default value when omitted is false.
 *                    <p>
 *                    Field Type: boolean
 *                    Cardinality: 0..1
 * 
 * @param seqNo       Required. Sequence number of this message. 
 *                    First message starts at 0.
 *                    <p>
 *                    Field Type: integer
 *                    Cardinality: 1..1
 * 
 * @param reportData  Optional. List of ReportData.
 *                    <p>
 *                    Field Type: ReportDataType
 *                    Cardinality: 0..*
 */
public record NotifyReportRequest(int requestId, ZonedDateTime generatedAt, boolean tbc, 
		int seqNo, List<ReportDataType> reportData) implements CSRequest {	
}