package org.galiasystems.csms.cs.adapters.ocpp.v2_0_1.model.payloads.common;

import java.time.ZonedDateTime;
import java.util.Collection;

/**
 * Collection of one or more sampled values in MeterValuesRequest and TransactionEvent. 
 * All sampled values in a MeterValue are sampled at the same point in time.
 * 
 * @param timestamp       Required. Timestamp for measured value(s).
 *                        <p>
 *                        Field Type: dateTime
 *                        Cardinality: 1..1
 *                        
 * @param sampledValue    Required. One or more measured values.
 *                        <p>
 *                        Field Type: SampledValueType
 *                        Cardinality: 1..*
 */
public record MeterValueType(ZonedDateTime timestamp, Collection<SampledValueType> sampledValue) {
}