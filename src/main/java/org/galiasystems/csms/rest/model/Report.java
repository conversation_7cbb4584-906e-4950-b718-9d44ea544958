package org.galiasystems.csms.rest.model;

import org.galiasystems.csms.management.model.enums.ReportStatus;
import org.galiasystems.csms.management.model.enums.ReportType;
import org.galiasystems.csms.management.types.enums.ReportResultStatus;

public record Report(long id, long requestId, ReportType type, ReportStatus status, 
		ReportResultStatus responseStatus, String responseReasonCode, String responseAdditionalInfo) {}