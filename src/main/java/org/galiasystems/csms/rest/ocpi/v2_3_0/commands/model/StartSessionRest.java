package org.galiasystems.csms.rest.ocpi.v2_3_0.commands.model;

import org.galiasystems.csms.rest.ocpi.v2_3_0.model.TokenRest;

/**
 * StartSession object, for the START_SESSION command, with information needed to start a sessions.
 * 
 * The evse_uid is optional. If no EVSE is specified, the Charge Point can itself decide on which EVSE to start a new session. 
 * (this might not be supported by all Charge Points).
 * 
 * The eMSP provides a Token that has to be used by the Charge Point. 
 * The Token provided by the eMSP for the StartSession SHALL be authorized by the eMSP before sending it to the CPO. 
 * Therefor the CPO SHALL NOT check the validity of the Token provided before sending the request to the Charge Point.
 * 
 * If this is an OCPP Charge Point, the Charge Point decides if it needs to validate the given Token, in such case:
 * • If this Token is of type: AD_HOC_USER or APP_USER the CPO SHALL NOT do a realtime authorization at the eMSP for this.
 * • If this Token is of type RFID, the CPO SHALL NOT do a realtime authorization at the eMSP for this Token 
 * 	 at the given EVSE/Charge Point within 15 minutes after having received this StartSession. 
 * 	 (This means that if the driver decided to use his RFID within 15 minutes at the same Charge Point, 
 * 	 because the app is not working somehow, the RFID is already authorized)
 * 
 * The eMSP MAY use Tokens that have not been pushed via the Token module, 
 * especially AD_HOC_USER or APP_USER Tokens are only used by commands send by an eMSP. 
 * As these are never used locally at the Charge Point like RFID.
 * 
 * Unknown Tokens received by the CPO in the StartSession Object don’t need to be stored in the Token module. 
 * In other words, when a Token has been received via StartSession, 
 * the same Token does not have to be returned in a Token GET request from the eMSP. 
 * However, the information of the Token SHALL be put in the Session and CDR.
 * 
 * An eMSP sending a StartSession SHALL only use Token that are owned by this eMSP in StartSession, 
 * using Tokens of other eMSPs is not allowed.
 * 
 * Notes:
 * 
 * In case of an OCPP 1.x Charge Point, the EVSE ID should be mapped to the connector ID of a Charge Point. 
 * OCPP 1.x does not have good support for Charge Points that have multiple connectors per EVSE. 
 * To make StartSession over OCPI work, the CPO SHOULD present the different connectors of an EVSE as separate EVSE, 
 * as is also written by the OCA in the application note: "Multiple Connectors per EVSE in a OCPP 1.x implementation".
 * 
 * @param response_url 				URL that the CommandResult POST should be sent to. 
 * 									This URL might contain a unique ID to be able to distinguish between StartSession requests.
 *                    				<p>
 *                    				Field Type: URL 
 *                    				Cardinality: 1
 *                    
 * @param token						Token object the Charge Point has to use to start a new session.
 * 									The Token provided in this request is authorized by the eMSP.
 *                    				<p>
 *                    				Field Type: Token
 *                    				Cardinality: 1
 *                    
 * @param location_id 				Location.id of the Location (belonging to the CPO this request is sent to) on which a session is to be started.
 *                    				<p>
 *                    				Field Type: CiString(36) 
 *                    				Cardinality: 1
 *                    
 * @param evse_uid 					Optional EVSE.uid of the EVSE of this Location on which a session is to be started. 
 * 									Required when connector_id is set.
 *                    				<p>
 *                    				Field Type: CiString(36) 
 *                    				Cardinality: ?
 *                    
 * @param connector_id  			Optional Connector.id of the Connector of the EVSE on which a session is to be started. 
 * 									This field is required when the capability: START_SESSION_CONNECTOR_REQUIRED is set on the EVSE.
 *                    				<p>
 *                    				Field Type: CiString(36) 
 *                    				Cardinality: ?
 *                    
 * @param authorization_reference	Reference to the authorization given by the eMSP, when given, 
 * 									this reference will be provided in the relevant Session and/or CDR.
 *                    				<p>
 *                    				Field Type: CiString(36) 
 *                    				Cardinality: ?
 */
public record StartSessionRest(String response_url, TokenRest token, String location_id, String evse_uid,
		String connector_id, String authorization_reference) implements Command {
}