package org.galiasystems.csms.rest.ocpi.v2_3_0.commands.model;

/**
 * The available commands in OCPI 2.3.0.
 */
public enum CommandType {

	/**
	 * Request the Charge Point to cancel a specific reservation.
	 */
	CANCEL_RESERVATION,
	
	/**
	 * Request the Charge Point to reserve a (specific) EVSE for a Token for a certain time, 
	 * starting now.
	 */
	RESERVE_NOW,
	
	/**
	 * Request the Charge Point to start a transaction on the given EVSE/Connector.
	 */
	START_SESSION,
	
	/**
	 * Request the Charge Point to stop an ongoing session.
	 */
	STOP_SESSION,
	
	/**
	 * Request the Charge Point to unlock the connector (if applicable).
	 * This functionality is for help desk operators only!
	 * 
	 * The command UNLOCK_CONNECTOR may only be used by an operator or the eMSP. 
	 * This command SHALL never be allowed to be sent directly by the EV-Driver. 
	 * The UNLOCK_CONNECTOR is intended to be used in the rare situation 
	 * that the connector is not unlocked successfully after a transaction is stopped. 
	 * The mechanical unlock of the lock mechanism might get stuck, for example: 
	 * fail when there is tension on the charging cable when the Charge Point tries to unlock the connector. 
	 * In such a situation the EV-Driver can call either the CPO or the eMSP to retry the unlocking.
	 */
	UNLOCK_CONNECTOR,
	;
}
