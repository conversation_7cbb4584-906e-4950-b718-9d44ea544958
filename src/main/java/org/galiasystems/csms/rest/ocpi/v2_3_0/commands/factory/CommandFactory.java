package org.galiasystems.csms.rest.ocpi.v2_3_0.commands.factory;

import java.util.Map;
import java.util.stream.Collectors;

import org.galiasystems.csms.rest.ocpi.v2_3_0.commands.model.CommandType;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.enterprise.inject.Instance;
import jakarta.inject.Inject;

@ApplicationScoped
public class CommandFactory {

	private final Map<CommandType, CommandHandler> handlers;
	
	@Inject
	public CommandFactory(final Instance<CommandHandler> allHandlers) {
		this.handlers = allHandlers.stream()
				.collect(Collectors.toMap(
						handler -> handler.getCommandType(),
				        handler -> handler,
				        (handler1, handler2) -> { 
				            throw new IllegalStateException("Duplicate handler for type: " + handler1.getCommandType() + 
				            		" (handlerClass1=" + handler1.getClass() + "; handlerClass2=" + handler2.getClass()); 
				        }
				    ));
    }
	
	public CommandHandler getHandler(final CommandType command) {
		return handlers.get(command);
    }
}
