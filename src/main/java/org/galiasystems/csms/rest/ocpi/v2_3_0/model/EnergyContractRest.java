package org.galiasystems.csms.rest.ocpi.v2_3_0.model;

/**
 * Information about a energy contract that belongs to a Token 
 * so a driver could use his/her own energy contract when charging at a Charge Point.
 * 
 * @param supplier_name    	Name of the energy supplier for this token.
 *                    		<p>
 *                    		Field Type: string(64)
 *                    		Cardinality: 1
 *                    
 * @param contract_id    	Contract ID at the energy supplier, that belongs to the owner of this token.
 *                    		<p>
 *                    		Field Type: string(64)
 *                    		Cardinality: 1
 */
public record EnergyContractRest(String supplier_name, String contract_id) {
}