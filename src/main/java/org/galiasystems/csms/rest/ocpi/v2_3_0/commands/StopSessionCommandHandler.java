package org.galiasystems.csms.rest.ocpi.v2_3_0.commands;

import java.time.ZonedDateTime;

import org.galiasystems.csms.rest.ocpi.v2_3_0.commands.factory.BaseCommandHandler;
import org.galiasystems.csms.rest.ocpi.v2_3_0.commands.model.CommandResponse;
import org.galiasystems.csms.rest.ocpi.v2_3_0.commands.model.CommandResponseType;
import org.galiasystems.csms.rest.ocpi.v2_3_0.commands.model.CommandType;
import org.galiasystems.csms.rest.ocpi.v2_3_0.commands.model.StopSessionRest;
import org.galiasystems.csms.rest.ocpi.v2_3_0.model.OcpiResponseRest;

import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;

@ApplicationScoped
public class StopSessionCommandHandler extends BaseCommandHandler<StopSessionRest>{

	public StopSessionCommandHandler() {
		super(StopSessionRest.class);
	}
	
	@Override
	public CommandType getCommandType() {
		return CommandType.STOP_SESSION;
	}

	@Override
	protected Uni<OcpiResponseRest<CommandResponse>> handle(final StopSessionRest command) {
		// TODO Auto-generated method stub
		final CommandResponse resp = new CommandResponse(CommandResponseType.ACCEPTED, 60, null);
		return Uni.createFrom().item(new OcpiResponseRest<>(resp, 1000, null, ZonedDateTime.now()));
	}

}
