package org.galiasystems.csms.rest.ocpi.v2_3_0.sessions.model;

import org.galiasystems.csms.management.model.enums.AuthMethod;
import org.galiasystems.csms.management.model.enums.SessionStatus;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Collection;

public record SessionRest(String country_code, String party_id, String id, ZonedDateTime start_date_time,
                          ZonedDateTime end_date_time, BigDecimal kwh, CdrTokenRest cdr_token, AuthMethod auth_method,
                          String authorization_reference,
                          String location_id, String evse_uid, String connector_id, String meter_id, String currency,
                          Collection<ChargingPeriodRest> charging_periods,
                          PriceRest total_cost, SessionStatus status, ZonedDateTime last_updated
) {

}