package org.galiasystems.csms.rest.ocpi.v2_3_0.sessions.utils;

import org.galiasystems.csms.management.model.ChargingPeriod;
import org.galiasystems.csms.management.model.CdrDimension;
import org.galiasystems.csms.management.model.Price;
import org.galiasystems.csms.management.model.Session;
import org.galiasystems.csms.management.model.SessionToken;
import org.galiasystems.csms.management.model.TaxAmount;
import org.galiasystems.csms.rest.ocpi.v2_3_0.sessions.model.CdrTokenRest;
import org.galiasystems.csms.rest.ocpi.v2_3_0.sessions.model.ChargingPeriodRest;
import org.galiasystems.csms.rest.ocpi.v2_3_0.sessions.model.CdrDimensionRest;
import org.galiasystems.csms.rest.ocpi.v2_3_0.sessions.model.PriceRest;
import org.galiasystems.csms.rest.ocpi.v2_3_0.sessions.model.SessionRest;
import org.galiasystems.csms.rest.ocpi.v2_3_0.sessions.model.TaxAmountRest;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Utility class for converting between Session and SessionRest objects.
 * Also provides methods for converting collections of these objects.
 */
public final class SessionConverter {

    private SessionConverter() {
        // Private constructor to prevent instantiation
    }

    /**
     * Converts a Session object to a SessionRest object.
     *
     * @param session The Session object to convert
     * @return The converted SessionRest object
     */
    public static SessionRest toRest(Session session) {
        if (session == null) {
            return null;
        }

        // Convert SessionToken to CdrTokenRest
        CdrTokenRest cdrTokenRest = null;
        if (session.sessionToken() != null) {
            SessionToken token = session.sessionToken();
            cdrTokenRest = new CdrTokenRest(
                    token.getCountryCode(),
                    token.getPartyId(),
                    token.getUid(),
                    token.getType(),
                    token.getContractId()
            );
        }

        // Convert ChargingPeriods to ChargingPeriodRest array
        Collection<ChargingPeriodRest> chargingPeriodsRest = null;
        if (session.chargingPeriods() != null && !session.chargingPeriods().isEmpty()) {
            chargingPeriodsRest = session.chargingPeriods().stream()
                    .map(SessionConverter::toChargingPeriodRest)
                    .toList();
        }

        // Convert Price to PriceRest
        PriceRest totalCostRest = null;
        if (session.totalCost() != null) {
            Price price = session.totalCost();
            TaxAmountRest taxAmountRest = null;
            if (price.taxes() != null) {
                TaxAmount taxAmount = price.taxes();
                taxAmountRest = new TaxAmountRest(
                        taxAmount.name(),
                        taxAmount.account_number(),
                        taxAmount.percentage(),
                        taxAmount.amount()
                );
            }
            totalCostRest = new PriceRest(price.before_taxes(), taxAmountRest);
        }

        // Create and return the SessionRest object
        return new SessionRest(
                session.countryCode(),
                session.partyId(),
                session.id() != null ? session.id().toString() : null,
                session.startDateTime(),
                session.endDateTime(),
                session.kwh(),
                cdrTokenRest,
                session.authMethod(),
                session.authorizationReference(),
                session.locationId(),
                session.evseUid(),
                session.connectorId(),
                session.meterId(),
                session.currency(),
                chargingPeriodsRest,
                totalCostRest,
                session.status(),
                session.lastUpdated()
        );
    }

    /**
     * Converts a SessionRest object to a Session object.
     *
     * @param sessionRest The SessionRest object to convert
     * @return The converted Session object
     */
    public static Session fromRest(SessionRest sessionRest) {
        if (sessionRest == null) {
            return null;
        }

        // Create a Session.Builder to build the Session object
        Session.Builder builder = new Session.Builder()
                .countryCode(sessionRest.country_code())
                .partyId(sessionRest.party_id())
                .id(sessionRest.id() != null ? Long.parseLong(sessionRest.id()) : null)
                .startDateTime(sessionRest.start_date_time())
                .endDateTime(sessionRest.end_date_time())
                .kwh(sessionRest.kwh())
                .authMethod(sessionRest.auth_method())
                .authorizationReference(sessionRest.authorization_reference())
                .locationId(sessionRest.location_id())
                .evseUid(sessionRest.evse_uid())
                .connectorId(sessionRest.connector_id())
                .meterId(sessionRest.meter_id())
                .currency(sessionRest.currency())
                .status(sessionRest.status())
                .lastUpdated(sessionRest.last_updated());

        // Convert CdrTokenRest to SessionToken
        if (sessionRest.cdr_token() != null) {
            CdrTokenRest tokenRest = sessionRest.cdr_token();
            SessionToken sessionToken = new SessionToken(
                    tokenRest.country_code(),
                    tokenRest.party_id(),
                    tokenRest.uid(),
                    tokenRest.type(),
                    tokenRest.contract_id(),
                    null, // visualNumber
                    null, // issuer
                    null, // groupId
                    true, // valid
                    null, // whitelist
                    null, // language
                    null, // defaultProfileType
                    null, // energyContract
                    null  // lastUpdated
            );
            builder.sessionToken(sessionToken);
        }

        // Convert ChargingPeriodRest array to List<ChargingPeriod>
        if (sessionRest.charging_periods() != null && !sessionRest.charging_periods().isEmpty()) {
            List<ChargingPeriod> chargingPeriods = new ArrayList<>();
            for (ChargingPeriodRest periodRest : sessionRest.charging_periods()) {
                chargingPeriods.add(fromChargingPeriodRest(periodRest));
            }
            builder.chargingPeriods(chargingPeriods);
        }

        // Convert PriceRest to Price
        if (sessionRest.total_cost() != null) {
            PriceRest priceRest = sessionRest.total_cost();
            TaxAmount taxAmount = null;
            if (priceRest.taxes() != null) {
                TaxAmountRest taxRest = priceRest.taxes();
                taxAmount = new TaxAmount(
                        taxRest.name(),
                        taxRest.account_number(),
                        taxRest.percentage(),
                        taxRest.amount()
                );
            }
            Price price = new Price(priceRest.before_taxes(), taxAmount);
            builder.totalCost(price);
        }

        // Build and return the Session object
        return builder.build();
    }

    /**
     * Converts a ChargingPeriod to a ChargingPeriodRest.
     *
     * @param chargingPeriod The ChargingPeriod to convert
     * @return The converted ChargingPeriodRest
     */
    private static ChargingPeriodRest toChargingPeriodRest(ChargingPeriod chargingPeriod) {
        if (chargingPeriod == null) {
            return null;
        }

        // Convert dimensions to Collection<CdrDimensionRest>
        List<CdrDimensionRest> dimensionsRest = new ArrayList<>();
        if (chargingPeriod.dimensions() != null && !chargingPeriod.dimensions().isEmpty()) {
            chargingPeriod.dimensions().forEach(dimension ->
                    dimensionsRest.add(new CdrDimensionRest(
                            dimension.type(),
                            dimension.volume()
                    ))
            );
        }

        return new ChargingPeriodRest(
                chargingPeriod.start_date_time(),
                dimensionsRest,
                chargingPeriod.tariff_id()
        );
    }

    /**
     * Converts a ChargingPeriodRest to a ChargingPeriod.
     *
     * @param periodRest The ChargingPeriodRest to convert
     * @return The converted ChargingPeriod
     */
    private static ChargingPeriod fromChargingPeriodRest(ChargingPeriodRest periodRest) {
        if (periodRest == null) {
            return null;
        }

        // Convert Collection<CdrDimensionRest> to List<CdrDimension>
        List<CdrDimension> dimensions = new ArrayList<>();
        if (periodRest.dimensions() != null && !periodRest.dimensions().isEmpty()) {
            for (CdrDimensionRest dimensionRest : periodRest.dimensions()) {
                dimensions.add(new CdrDimension(dimensionRest.type(), dimensionRest.volume()));
            }
        }

        return new ChargingPeriod(periodRest.start_date_time(), dimensions, periodRest.tariff_id());
    }

    /**
     * Converts a collection of Session objects to a list of SessionRest objects.
     *
     * @param sessions The collection of Session objects to convert
     * @return A list of converted SessionRest objects, or an empty list if the input is null or empty
     */
    public static List<SessionRest> toRestList(Collection<Session> sessions) {
        if (sessions == null || sessions.isEmpty()) {
            return Collections.emptyList();
        }

        return sessions.stream()
                .map(SessionConverter::toRest)
                .collect(Collectors.toList());
    }

    /**
     * Converts a collection of SessionRest objects to a list of Session objects.
     *
     * @param sessionRests The collection of SessionRest objects to convert
     * @return A list of converted Session objects, or an empty list if the input is null or empty
     */
    public static List<Session> fromRestList(Collection<SessionRest> sessionRests) {
        if (sessionRests == null || sessionRests.isEmpty()) {
            return Collections.emptyList();
        }

        return sessionRests.stream()
                .map(SessionConverter::fromRest)
                .collect(Collectors.toList());
    }
}