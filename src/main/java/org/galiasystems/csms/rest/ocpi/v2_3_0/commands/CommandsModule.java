package org.galiasystems.csms.rest.ocpi.v2_3_0.commands;

import java.io.IOException;
import java.io.InputStream;
import java.time.ZonedDateTime;

import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.media.Content;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import org.eclipse.microprofile.openapi.annotations.parameters.RequestBody;
import org.eclipse.microprofile.openapi.annotations.responses.APIResponse;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;
import org.galiasystems.csms.rest.ocpi.v2_3_0.commands.factory.CommandFactory;
import org.galiasystems.csms.rest.ocpi.v2_3_0.commands.factory.CommandHandler;
import org.galiasystems.csms.rest.ocpi.v2_3_0.commands.model.CommandResponse;
import org.galiasystems.csms.rest.ocpi.v2_3_0.commands.model.CommandResponseType;
import org.galiasystems.csms.rest.ocpi.v2_3_0.commands.model.CommandType;
import org.galiasystems.csms.rest.ocpi.v2_3_0.commands.model.StartSessionRest;
import org.galiasystems.csms.rest.ocpi.v2_3_0.commands.model.StopSessionRest;
import org.galiasystems.csms.rest.ocpi.v2_3_0.model.OcpiResponseRest;
import org.jboss.resteasy.reactive.RestPath;

import io.smallrye.mutiny.Uni;
import jakarta.inject.Inject;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;

@Path("ocpi/2.3.0/commands")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Tag(name = "Commands Module", description = "OCPI v2.3.0 Commands Module endpoints")
public class CommandsModule {
	
	@Inject
	CommandFactory commandFactory;
	
	@POST
	@Path("/{command}")
	@Operation(summary = "Accept a command")
	@APIResponse(responseCode = "200", description = "Command accepted")
	@RequestBody(
        required = true,
        content = @Content(
            schema = @Schema(oneOf = { StartSessionRest.class, StopSessionRest.class })
        )
    )
	public Uni<OcpiResponseRest<CommandResponse>> executeCommand(
			final @RestPath("command") CommandType command, 
			InputStream requestBodyStream) throws IOException {
		
		final CommandHandler commandHandler = commandFactory.getHandler(command);
		
		
		if (commandHandler == null) {
			final CommandResponse resp = new CommandResponse(CommandResponseType.NOT_SUPPORTED, 0, null);
			return Uni.createFrom().item(new OcpiResponseRest<>(resp, 1000, null, ZonedDateTime.now()));
		}
		
		return commandHandler.process(requestBodyStream);
    }
}
