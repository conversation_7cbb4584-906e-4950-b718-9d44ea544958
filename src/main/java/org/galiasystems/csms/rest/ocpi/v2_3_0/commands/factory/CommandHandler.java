package org.galiasystems.csms.rest.ocpi.v2_3_0.commands.factory;

import java.io.IOException;
import java.io.InputStream;

import org.galiasystems.csms.rest.ocpi.v2_3_0.commands.model.CommandResponse;
import org.galiasystems.csms.rest.ocpi.v2_3_0.commands.model.CommandType;
import org.galiasystems.csms.rest.ocpi.v2_3_0.model.OcpiResponseRest;

import io.smallrye.mutiny.Uni;

public interface CommandHandler {
	CommandType getCommandType();
	Uni<OcpiResponseRest<CommandResponse>> process(final InputStream inputStream) throws IOException;
}
