package org.galiasystems.csms.rest.ocpi.v2_3_0.commands.factory;

import java.io.IOException;
import java.io.InputStream;

import org.galiasystems.csms.rest.ocpi.v2_3_0.commands.model.Command;
import org.galiasystems.csms.rest.ocpi.v2_3_0.commands.model.CommandResponse;
import org.galiasystems.csms.rest.ocpi.v2_3_0.model.OcpiResponseRest;

import com.fasterxml.jackson.databind.ObjectMapper;

import io.smallrye.mutiny.Uni;
import jakarta.inject.Inject;

public abstract class BaseCommandHandler<T extends Command> implements CommandHandler{

	@Inject
    ObjectMapper objectMapper;

    private final Class<T> commandClass;

    protected BaseCommandHandler(final Class<T> commandClass) {
        this.commandClass = commandClass;
    }

    @Override
    public Uni<OcpiResponseRest<CommandResponse>> process(final InputStream inputStream) throws IOException {
        T pet = objectMapper.readValue(inputStream, commandClass);
        return handle(pet);
    }

    protected abstract Uni<OcpiResponseRest<CommandResponse>> handle(final T command);
}
