package org.galiasystems.csms.rest.ocpi.v2_3_0.model;

import java.time.ZonedDateTime;

/**
 * Format of all the response messages.
 * 
 * @param data    			Contains the actual response data object or list of objects from each request, 
 * 							depending on the cardinality of the response data, this is an array 
 * 							(card. * or +), or a single object (card. 1 or ?)
 *                    		<p>
 *                    		Field Type: Array or Object or String
 *                    		Cardinality: * or ?
 * 
 * @param status_code    	OCPI status code, as listed in Status Codes, indicates how the request was handled. 
 * 							To avoid confusion with HTTP codes, OCPI status codes consist of four digits.
 *                    		<p>
 *                    		Field Type: int
 *                    		Cardinality: 1
 *                    
 * @param status_message	An optional status message which may help when debugging.
 *                    		<p>
 *                    		Field Type: string
 *                    		Cardinality: ?
 *                    
 * @param timestamp    		IThe time this message was generated.
 *                    		<p>
 *                    		Field Type: DateTime
 *                    		Cardinality: 1
 */
public record OcpiResponseRest<T>(T data, int status_code, String status_message, ZonedDateTime timestamp) {
}