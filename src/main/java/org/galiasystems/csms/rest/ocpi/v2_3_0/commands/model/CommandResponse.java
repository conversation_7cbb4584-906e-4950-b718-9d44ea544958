package org.galiasystems.csms.rest.ocpi.v2_3_0.commands.model;

import java.util.Collection;

import org.galiasystems.csms.rest.ocpi.v2_3_0.model.DisplayTextRest;

/**
 * The CommandResponse object is send in the HTTP response body.
 * 
 * Because OCPI does not allow/require retries, it could happen that the asynchronous result url given by the eMSP is never successfully called. 
 * The eMSP might have had a glitch, HTTP 500 returned, was offline for a moment etc. 
 * For the eMSP to be able to give a quick as possible response to another system or driver app. 
 * It is important for the eMSP to know the timeout on a certain command.
 * 
 * @param result    Response from the CPO on the command request.
 *                  <p>
 *                  Field Type: CommandResponseType 
 *                  Cardinality: 1
 *                 
 * @param timeout	Timeout for this command in seconds. 
 * 					When the Result is not received within this timeout, the eMSP can assume that the message might never be send.
 *                  <p>
 *                  Field Type: int
 *                  Cardinality: 1
 *                    
 * @param message   Human-readable description of the result (if one can be provided), multiple languages can be provided.
 *                  <p>
 *                  Field Type: DisplayText 
 *                  Cardinality: *
 */
public record CommandResponse(CommandResponseType result, int timeout, Collection<DisplayTextRest>  message) {
}