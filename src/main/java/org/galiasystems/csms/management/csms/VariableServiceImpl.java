package org.galiasystems.csms.management.csms;

import java.util.List;

import org.galiasystems.csms.cs.ChargingStationAdapterProxy;
import org.galiasystems.csms.management.types.GetVariableResult;
import org.galiasystems.csms.management.types.SetVariableResult;
import org.galiasystems.csms.management.types.VariableValueToGet;
import org.galiasystems.csms.management.types.VariableValueToSet;
import org.jboss.logging.Logger;

import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;

@ApplicationScoped
public class VariableServiceImpl implements VariableService {
	
	@Inject
    private Logger log;
	
	@Inject
    private ChargingStationAdapterProxy chargingStationAdapter;

	@Override
	public Uni<List<SetVariableResult>> setVariables(final long chargingStationId, final List<VariableValueToSet> variableValuesToSet) {
    	return this.chargingStationAdapter.setVariables(chargingStationId, variableValuesToSet)
    			.invoke((ignored) -> {
    				if (this.log.isDebugEnabled()) {
    		    		log.debug("Charging Station '" + chargingStationId + "' variables set!");
    				}
    			});
    }

	@Override
	public Uni<List<GetVariableResult>> getVariables(final long chargingStationId, final List<VariableValueToGet> variableValuesToGet) {
		return this.chargingStationAdapter.getVariables(chargingStationId, variableValuesToGet)
				.invoke((ignored) -> {
    				if (this.log.isDebugEnabled()) {
    		    		log.debug("Charging Station '" + chargingStationId + "' variables get!");
    				}
    			});
	}
}
