package org.galiasystems.csms.management.csms;

import java.time.ZonedDateTime;
import java.util.Collection;

import org.galiasystems.csms.management.filter.ChargingStationFilter;
import org.galiasystems.csms.management.model.ChargingStation;
import org.galiasystems.csms.management.model.IdToken;
import org.galiasystems.csms.management.model.enums.AvailabilityStatus;
import org.galiasystems.csms.management.model.enums.ResetType;
import org.galiasystems.csms.management.types.*;

import io.smallrye.mutiny.Uni;
import org.galiasystems.csms.management.types.enums.MessageTrigger;

public interface ChargingStationService {

	Uni<ChargingStation> registerChargingStation(final String name);

	Uni<ChargingStation> getChargingStation(final long id);
	
	Uni<Collection<ChargingStation>> getChargingStations(final ChargingStationFilter filter);

	Uni<ChangeAvailabilityResult> changeAvailability(final long chargingStationId, final Integer evseId, final Integer connectorId,
			final AvailabilityStatus availabilityStatus);

	Uni<ResetResult> reset(final long id, final ResetType resetType,
			final Integer evseId);

	Uni<ChargingStation> updateChargingStation(long chargingStationId, String name);

	Uni<RequestStartTransactionResult> requestStartTransaction(final long chargingStationId, final Integer evseId, final long remoteStartId, 
    		final IdToken idToken);

	Uni<RequestStopTransactionResult> requestStopTransaction(final long chargingStationId, final String transactionId);

	Uni<GetDiagnosticsResult> getDiagnostics(final long chargingStationId, final String location,
											 final Integer retries, final Integer retryInterval,
											 final ZonedDateTime startTime, final ZonedDateTime stopTime);

	Uni<TriggerMessageResult> triggerMessage(final long chargingStationId, final MessageTrigger messageTrigger, final Integer connectorId);
}