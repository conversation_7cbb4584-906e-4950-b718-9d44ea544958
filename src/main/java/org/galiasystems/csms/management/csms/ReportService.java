package org.galiasystems.csms.management.csms;

import java.util.Collection;

import org.galiasystems.csms.management.model.Report;
import org.galiasystems.csms.management.model.enums.ReportType;

import io.smallrye.mutiny.Uni;

public interface ReportService {

	Uni<Report> executeReport(final long chargingStationId, final ReportType reportType);
	
	Uni<Report> getReport(final long reportId);
	
	Uni<Collection<Report>> getReports();
	
	Uni<Collection<Report>> getReports(final long chargingStationId);
}
