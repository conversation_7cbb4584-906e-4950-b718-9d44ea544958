package org.galiasystems.csms.management.csms;

import java.util.Collection;

import org.galiasystems.csms.management.model.Evse;
import org.galiasystems.csms.management.repository.EvseRepository;

import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;

@ApplicationScoped
public class EvseServiceImpl implements EvseService {

	@Inject
    private EvseRepository evseRepository;
	
	@Override
	public Uni<Evse> getEvse(final long id) {
		return this.evseRepository.getEvse(id);
	}

	@Override
	public Uni<Collection<Evse>> getEvses(final long chargingStationId) {
		return this.evseRepository.getEvses(chargingStationId);
	}
}