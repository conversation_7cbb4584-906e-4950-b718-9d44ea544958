package org.galiasystems.csms.management.csms.base;

import java.util.Set;

import org.galiasystems.csms.management.model.User;
import org.galiasystems.csms.security.GraphqlAuthMechanism;

import io.quarkus.security.identity.SecurityIdentity;
import jakarta.inject.Inject;

public abstract class CsmsServiceBase {
	
	@Inject
    SecurityIdentity currentIdentity;

	protected User getCurrentUser() {
		return this.currentIdentity.getAttribute(GraphqlAuthMechanism.SECURITY_IDENTITY_ATTRIBUTE_NAME_USER);
	}
	
	protected Long getCurrentUserTenantId() {
		return getCurrentUser().getTenantId();
	}
	
	protected String getCurrentUserUserName() {
		return getCurrentUser().getUserName();
	}
	
	protected Set<String> getRoles() {
		return this.currentIdentity.getRoles();
	}
	
	protected boolean isCurrentUserGlobalAdmin() {
		return getCurrentUser().isGlobalAdmin();
	}
	
	protected boolean isCurrentUserTenantAdmin() {
		return getCurrentUser().isTenantAdmin();
	}
	
	protected String getPrincipalName() {
		return this.currentIdentity.getPrincipal().getName();
	}
}