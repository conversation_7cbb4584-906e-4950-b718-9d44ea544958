package org.galiasystems.csms.management.csms;

import java.util.List;

import org.galiasystems.csms.management.types.GetVariableResult;
import org.galiasystems.csms.management.types.SetVariableResult;
import org.galiasystems.csms.management.types.VariableValueToGet;
import org.galiasystems.csms.management.types.VariableValueToSet;

import io.smallrye.mutiny.Uni;

public interface VariableService {

	Uni<List<SetVariableResult>> setVariables(final long chargingStationId, final List<VariableValueToSet> variableValuesToSet);
	
	Uni<List<GetVariableResult>> getVariables(final long chargingStationId, final List<VariableValueToGet> variableValuesToGet);
}
