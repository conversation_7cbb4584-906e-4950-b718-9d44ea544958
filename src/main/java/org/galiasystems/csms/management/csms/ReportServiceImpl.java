package org.galiasystems.csms.management.csms;

import java.util.Collection;

import org.galiasystems.csms.cs.ChargingStationAdapterProxy;
import org.galiasystems.csms.management.model.Report;
import org.galiasystems.csms.management.model.enums.ReportStatus;
import org.galiasystems.csms.management.model.enums.ReportType;
import org.galiasystems.csms.management.repository.ReportRepository;
import org.galiasystems.csms.management.repository.TransactionalService;
import org.jboss.logging.Logger;

import io.smallrye.mutiny.Uni;
import io.vertx.core.Context;
import io.vertx.core.Vertx;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;

@ApplicationScoped
public class ReportServiceImpl implements ReportService {
	
	@Inject
    private Logger log;

    @Inject
    private ChargingStationAdapterProxy chargingStationAdapter;
	
	@Inject
    private ReportRepository reportRepository;
	
	@Inject
    private TransactionalService transactionalService;
	
	@Override
	public Uni<Report> executeReport(final long chargingStationId, final ReportType reportType) {
    	final Report newReport = new Report(null, null, reportType, ReportStatus.New, null, null, null);
    	
    	Context context = Vertx.currentContext();
    	
    	return this.reportRepository.createReport(chargingStationId, newReport)
    		.onItem()
    		.transformToUni(
    			(report) -> this.transactionalService.doInTransaction(
			    				() -> this.reportRepository.updateReport(report.getId(), 
					    				(reportToUpdate) -> {
				    						reportToUpdate.setRequestId(reportToUpdate.getId());
				    						reportToUpdate.setStatus(ReportStatus.Requested);
					    				}
			    					  )
			    					  .call(
			    							  (reportToExecute) -> this.chargingStationAdapter.executeReport(
			    									  					chargingStationId,
			    									  					reportToExecute.getRequestId(),
			    									  					reportToExecute.getType()
			    									  				)
			    					  )
			    					  .emitOn(runnable -> context.runOnContext(ignored -> runnable.run()))
    						)
    		)
    		.invoke((ignored) -> {
				if (this.log.isDebugEnabled()) {
					this.log.debug("Charging Station '" + chargingStationId + "' Base Report requested!");
				}
			});   
    }
    
	@Override
	public Uni<Report> getReport(final long reportId) {
		return this.reportRepository.getReport(reportId);
	}

	@Override
	public Uni<Collection<Report>> getReports() {
		return this.reportRepository.getReports();
	}
    
    @Override
    public Uni<Collection<Report>> getReports(final long chargingStationId) {
    	return this.reportRepository.getReports(chargingStationId);
    }
}
