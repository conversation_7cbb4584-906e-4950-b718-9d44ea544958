package org.galiasystems.csms.management.repository;

import java.util.Collection;
import java.util.List;

import org.galiasystems.csms.management.model.ChargingStationVariableValue;

import io.smallrye.mutiny.Uni;

public interface ChargingStationVariableValueRepository {
	
	Uni<List<ChargingStationVariableValue>> upsertChargingStationVariableValues(final long chargingStationVariableId,
			final Collection<ChargingStationVariableValue> chargingStationVariableValues);

}
