package org.galiasystems.csms.management.repository;

import java.util.Collection;

import org.galiasystems.csms.management.model.Evse;
import org.galiasystems.csms.management.model.enums.EvseStatus;

import io.smallrye.mutiny.Uni;

public interface EvseRepository {
	
	Uni<Evse> createEvse(final long chargingStationId, final Evse evse);
	
	Uni<Evse> getEvse(final long id);
	
	Uni<Collection<Evse>> getEvses();
	
	Uni<Collection<Evse>> getEvses(final long chargingStationId);
	
	Uni<Evse> getEvseByChargingStationEvseId(final long chargingStationId, final Integer chargingStationEvseId);
	
	Uni<Evse> updateEvseStatusIfExists(final long chargingStationId, final int evseId, final EvseStatus status);
}