package org.galiasystems.csms.management.repository;

import java.util.Collection;

import org.galiasystems.csms.management.model.Location;

import io.smallrye.mutiny.Uni;

public interface LocationRepository {
	
	Uni<Location> createLocation(final long tenantId, final Location location);
	
	Uni<Collection<Location>> getLocations();
	
	Uni<Collection<Location>> getLocationsOf(final long tenantId);
	
	Uni<Location> getLocation(final long id);
	
	Uni<Location> updateLocation(final Location location);
}
