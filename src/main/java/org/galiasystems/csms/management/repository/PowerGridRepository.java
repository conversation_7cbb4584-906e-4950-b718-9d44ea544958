package org.galiasystems.csms.management.repository;

import java.util.Collection;

import org.galiasystems.csms.management.model.PowerGrid;

import io.smallrye.mutiny.Uni;

public interface PowerGridRepository {
	
	Uni<PowerGrid> createPowerGrid(final long locationId, final PowerGrid powerGrid);
	
	Uni<Collection<PowerGrid>> getPowerGrids();
	
	Uni<Collection<PowerGrid>> getPowerGridsOf(final long locationId);
	
	Uni<PowerGrid> getPowerGrid(final long id);
}
