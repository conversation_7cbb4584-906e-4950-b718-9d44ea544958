package org.galiasystems.csms.management.repository;

import java.time.ZonedDateTime;
import java.util.Collection;

import org.galiasystems.csms.management.model.Connector;
import org.galiasystems.csms.management.model.enums.ConnectorStatus;

import io.smallrye.mutiny.Uni;

public interface ConnectorRepository {

	Uni<Connector> createConnector(final long evseId, final Connector connector);
	
	Uni<Collection<Connector>> getConnectors();
	
	Uni<Collection<Connector>> getConnectorsOf(final long evseId);
	
	Uni<Connector> getConnector(final long id);

	Uni<Connector> getConnectorByEvseConnectorId(final long evseId, final Integer evseConnectorId);

	Uni<Connector> updateConnectorStatusIfExists(final long evseId, final Integer evseConnectorId, final ConnectorStatus status,
			final ZonedDateTime lastStatusUpdate, final String errorCode, final String errorInfo, final String vendorImplementationId, 
			final String vendorErrorCode);
}