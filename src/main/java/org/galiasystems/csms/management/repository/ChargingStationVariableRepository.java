package org.galiasystems.csms.management.repository;

import java.util.Collection;
import java.util.List;

import org.galiasystems.csms.management.model.ChargingStationVariable;

import io.smallrye.mutiny.Uni;

public interface ChargingStationVariableRepository {

	Uni<List<ChargingStationVariable>> upsertChargingStationVariables(final Long chargingStationId,
			final Collection<ChargingStationVariable> chargingStationVariables);

	Uni<Collection<ChargingStationVariable>> getChargingStationVariables(final long chargingStationId);
}