package org.galiasystems.csms.management.repository;

import java.util.Collection;

import io.quarkus.hibernate.reactive.panache.common.WithSession;
import io.quarkus.hibernate.reactive.panache.common.WithTransaction;
import org.galiasystems.csms.management.filter.UserFilter;
import org.galiasystems.csms.management.model.User;

import io.smallrye.mutiny.Uni;

public interface UserRepository {

	Uni<User> createUser(final User user);
	
	Uni<User> updateUser(final User user);

	@WithTransaction
	Uni<User> updateUsersPassword(User user);

	Uni<Collection<User>> getUsers(final UserFilter filter);
	
	Uni<User> getUser(final long id);
	
	Uni<User> getUserByUserName(final String userName);

	@WithSession
	Uni<User> getChargingStationUserByChargingStationId(Long id);
}