package org.galiasystems.csms.management.repository;

import io.smallrye.mutiny.Uni;
import org.galiasystems.csms.management.model.MeterValue;

import java.util.Collection;

public interface MeterValueRepository {

    Uni<Void> createMeterValues(final long chargingStationId, final Integer chargingStationEvseId, 
			final Collection<MeterValue> meterValue);

    Uni<Collection<MeterValue>> getMeterValues(final Long chargingStationId, final Integer evseChargingStationId);
}