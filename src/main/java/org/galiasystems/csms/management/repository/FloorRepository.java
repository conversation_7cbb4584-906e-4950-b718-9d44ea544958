package org.galiasystems.csms.management.repository;

import java.util.Collection;

import org.galiasystems.csms.management.model.Floor;

import io.smallrye.mutiny.Uni;

public interface FloorRepository {

	Uni<Floor> createFloor(final long locationId, final Floor floor);
	
	Uni<Collection<Floor>> getFloors();
	
	Uni<Collection<Floor>> getFloorsOf(final long locationId);
	
	Uni<Floor> getFloor(final long id);
}
