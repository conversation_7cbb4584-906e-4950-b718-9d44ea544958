package org.galiasystems.csms.management.cs;

import java.time.ZonedDateTime;
import java.util.Collection;
import java.util.List;

import org.galiasystems.csms.cs.ChargingStationAdapterType;
import org.galiasystems.csms.management.model.*;
import org.galiasystems.csms.management.model.enums.ChargingStationStatus;
import org.galiasystems.csms.management.model.enums.ConnectorStatus;
import org.galiasystems.csms.management.model.enums.EvseStatus;
import org.galiasystems.csms.management.types.*;

import io.smallrye.mutiny.Uni;

public interface ChargingStationManager {

	Uni<ChargingStation> chargingStationConnected(final long chargingStationId, ChargingStationAdapterType chargingStationAdapterType);

	Uni<ChargingStation> chargingStationBooted(final ChargingStation chargingStationId);

	Uni<ChargingStation> chargingStationDown(final long chargingStationId);

	Uni<Report> reportResponseReceived(final long chargingStationId, final long requestId,
			final ReportRequestResult reportResult);

	Uni<Void> reportDataReceived(final long chargingStationId, final int reportRequestId,
    		final boolean finalData, final Collection<ChargingStationVariable> chargingStationVariables);
	
	Uni<Void> chargingStationStatusReceived(final long chargingStationId, final ChargingStationStatus chargingStationStatus,
			final ZonedDateTime lastStatusUpdate, final String errorCode, final String errorInfo, final String vendorImplementationId,
			final String vendorErrorCode);
	
	Uni<Void> evseStatusReceived(final long chargingStationId, final int evseId, final EvseStatus evseStatus);
	
	Uni<Void> connectorStatusReceived(final long chargingStationId, final int evseId, final int connectorId,
    		final ConnectorStatus connectorStatus, final ZonedDateTime lastStatusUpdate, final String errorCode, final String errorInfo, 
    		final String vendorImplementationId, final String vendorErrorCode);
	
	Uni<Void> setVariableResultReceived(final long chargingStationId, final List<SetVariableResult> setVariableResults);
	
	Uni<Void> getVariableResultReceived(final long chargingStationId, final List<GetVariableResult> getVariableResults);
	
	Uni<Void> changeAvailabilityResultReceived(final long chargingStationId, final ChangeAvailabilityResult changeAvailabilityResult);
	
	Uni<Void> resetResultReceived(final long chargingStationId, final ResetResult resetResult);

	Uni<TransactionEventResult> transactionEventReceived(final long chargingStationId, final Integer chargingStationEvseId, 
			final Integer evseConnectorId, final String evseTransactionId, final EvseTransactionEvent transactionEvent);

	Uni<Long> getNextEvseTransactionId();

	Uni<Void> requestStartTransactionResultReceived(final long chargingStationId, final RequestStartTransactionResult requestStartTransactionResult);

	Uni<Void> requestStopTransactionResultReceived(final long chargingStationId, final RequestStopTransactionResult requestStopTransactionResult);

	Uni<Void> meterValueReceived(final long chargingStationId, final Integer chargingStationEvseId,
			final Collection<MeterValue> meterValues);

	Uni<Void> getDiagnosticsResultReceived(final long chargingStationId, final GetDiagnosticsResult getDiagnosticsResult);

	Uni<Void> triggerMessageReceived(final long chargingStationId, final TriggerMessageResult triggerMessageResult);
}