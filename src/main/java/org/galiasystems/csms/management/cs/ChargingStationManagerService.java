package org.galiasystems.csms.management.cs;

import java.time.ZonedDateTime;
import java.util.Collection;
import java.util.List;

import org.galiasystems.csms.cs.ChargingStationAdapterType;
import org.galiasystems.csms.management.model.*;
import org.galiasystems.csms.management.model.enums.AvailabilityStatus;
import org.galiasystems.csms.management.model.enums.ChargingStationStatus;
import org.galiasystems.csms.management.model.enums.ConnectorStatus;
import org.galiasystems.csms.management.model.enums.EvseStatus;
import org.galiasystems.csms.management.model.enums.ReportStatus;
import org.galiasystems.csms.management.repository.*;
import org.galiasystems.csms.management.types.*;
import org.galiasystems.csms.management.types.enums.ReportResultStatus;
import org.galiasystems.csms.utils.Pair;
import org.jboss.logging.Logger;

import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;

@ApplicationScoped
public class ChargingStationManagerService implements ChargingStationManager {

	public static final Long DEFAULT_TENANT_ID = -1L;

	@Inject
    private Logger log;
	
	@Inject
    private TransactionalService transactionalService;
	
	@Inject
    private ChargingStationRepository chargingStationRepository;
	
	@Inject
    private EvseRepository evseRepository;
	
	@Inject
    private ConnectorRepository connectorRepository;
	
	@Inject
    private ReportRepository reportRepository;
	
	@Inject
	private EvseTransactionRepository evseTransactionRepository;
	
	@Inject
	private EvseTransactionEventRepository evseTransactionEventRepository;

	@Inject
	private MeterValueRepository meterValueRepository;

	@Override
	public Uni<ChargingStation> chargingStationConnected(final long chargingStationId, ChargingStationAdapterType chargingStationAdapterType) {
		return this.transactionalService.doInTransaction(() -> this.chargingStationRepository.updateChargingStation(chargingStationId, (chargingStation) -> {
			chargingStation.setAdapterType(chargingStationAdapterType);
		}).call((ignored) ->
				chargingStationStatusChanged(chargingStationId, ChargingStationStatus.Connected, ZonedDateTime.now(), null, null, null, null)
						.onItem()
						.invoke((chargingStation) -> {
							if (this.log.isDebugEnabled()) {
								this.log.debug("Charging Station '" + chargingStation.getId() + "' connected!");
							}
						})
		));
	}

	@Override
	public Uni<ChargingStation> chargingStationBooted(final ChargingStation chargingStation) {
		return this.transactionalService.doInTransaction(() -> {
			return this.chargingStationRepository.updateChargingStation(chargingStation.getId(),
					(updatedChargingStation) -> {
						updatedChargingStation.setChargePointSerialNumber(chargingStation.getChargePointSerialNumber());
						updatedChargingStation.setChargePointVendor(chargingStation.getChargePointVendor());
						updatedChargingStation.setMeterType(chargingStation.getMeterType());
						updatedChargingStation.setMeterSerialNumber(chargingStation.getMeterSerialNumber());
						updatedChargingStation.setChargePointModel(chargingStation.getChargePointModel());
						updatedChargingStation.setIccid(chargingStation.getIccid());
						updatedChargingStation.setChargeBoxSerialNumber(chargingStation.getChargeBoxSerialNumber());
						updatedChargingStation.setFirmwareVersion(chargingStation.getFirmwareVersion());
						updatedChargingStation.setImsi(chargingStation.getImsi());
					}).call((ignored) ->
					chargingStationStatusChanged(chargingStation.getId(), ChargingStationStatus.Online, ZonedDateTime.now(), null, null, null, null)
							.onItem()
							.invoke((chargingStation1) -> {
								if (this.log.isDebugEnabled()) {
									this.log.debug("Charging Station '" + chargingStation1.getId() + "' online!");
								}
							}));
		});
    }

    @Override
	public Uni<ChargingStation> chargingStationDown(final long chargingStationId) {
    	return chargingStationStatusChanged(chargingStationId, ChargingStationStatus.Offline, ZonedDateTime.now(), null, null, null, null)
    			.onItem()
    			.invoke((chargingStation) -> {
    				if (this.log.isDebugEnabled()) {
    					this.log.debug("Charging Station '" + chargingStation.getId() + "' offline!");
    				}
    			});
    }

	private Uni<ChargingStation> chargingStationStatusChanged(final Long chargingStationId, final ChargingStationStatus newStatus,
			final ZonedDateTime lastStatusUpdate, final String errorCode, final String errorInfo, final String vendorImplementationId,
			final String vendorErrorCode) {
        return this.chargingStationRepository.updateChargingStation(chargingStationId, 
        		(chargingStation) -> {
    				final var oldStatus = chargingStation.getStatus();
    				if (this.log.isDebugEnabled()) {
    					this.log.debug("Charging Station '" + chargingStationId + "' status changed! from: " + oldStatus + "; to: " + newStatus);
    				}
    				chargingStation.setStatus(newStatus);
    				chargingStation.setLastStatusUpdate(lastStatusUpdate);
    				chargingStation.setErrorCode(errorCode);
    				chargingStation.setErrorInfo(errorInfo);
    				chargingStation.setVendorImplementationId(vendorImplementationId);
    				chargingStation.setVendorErrorCode(vendorErrorCode);
    				});
    }

	@Override
	public Uni<Report> reportResponseReceived(final long chargingStationId, final long requestId,
			final ReportRequestResult reportResult) {
		
		final ReportResultStatus reportResultStatus = reportResult.resultStatus();
		
		return this.reportRepository.updateReport(chargingStationId, requestId, (report) -> {
			if (reportResultStatus == ReportResultStatus.Accepted) {
				report.setStatus(ReportStatus.Processing);
			} else {
				report.setStatus(ReportStatus.Rejected);
			}
			report.setResponseStatus(reportResultStatus);
			report.setResponseReasonCode(reportResult.reasonCode());
			report.setResponseAdditionalInfo(reportResult.additionalInfo());
		});
		
	}

	@Override
	public Uni<Void> reportDataReceived(final long chargingStationId, final int reportRequestId,
			final boolean finalData, final Collection<ChargingStationVariable> chargingStationVariables) {
				
		return this.transactionalService.doInTransaction(
				() -> this.reportRepository.updateReport(chargingStationId, reportRequestId, 
					(report) -> {
						if (finalData) {
							report.setStatus(ReportStatus.Processed);
						}
					})
					.onItem()
					.call((ignored) -> this.chargingStationRepository.updateChargingStationVariables(chargingStationId, 
							chargingStationVariables))
					
//					.call((ignored) -> this.chargingStationVariableRepository.upsertChargingStationVariables(chargingStationId, 
//							chargingStationVariables))
		
			).replaceWithVoid();
	}
	
	@Override
	public Uni<Void> chargingStationStatusReceived(final long chargingStationId, final ChargingStationStatus chargingStationStatus,
			final ZonedDateTime lastStatusUpdate, final String errorCode, final String errorInfo, final String vendorImplementationId,
			final String vendorErrorCode) {
		
		return chargingStationStatusChanged(chargingStationId, chargingStationStatus, lastStatusUpdate, errorCode, errorInfo, 
									vendorImplementationId, vendorErrorCode)
				.replaceWithVoid();
	}
	
	@Override
	public Uni<Void> evseStatusReceived(final long chargingStationId, final int evseId, final EvseStatus evseStatus) {
		return this.evseRepository.updateEvseStatusIfExists(chargingStationId, evseId, evseStatus)
				.onItem()
				.ifNull()
				.switchTo(this.evseRepository.createEvse(chargingStationId, new Evse(null, evseId, evseStatus,
						AvailabilityStatus.Unknown, null, null, null, null))
				)
				.replaceWithVoid();
	}

	@Override
	public Uni<Void> connectorStatusReceived(final long chargingStationId, final int chargingStationEvseId, final int evseConnectorId,
			final ConnectorStatus connectorStatus, final ZonedDateTime lastStatusUpdate, final String errorCode, final String errorInfo, 
    		final String vendorImplementationId, final String vendorErrorCode) {
		
		return this.transactionalService.doInTransaction(() -> {
			return this.evseRepository.getEvseByChargingStationEvseId(chargingStationId, chargingStationEvseId)
				.onItem()
				.ifNull()
				.switchTo(this.evseRepository.createEvse(chargingStationId, new Evse(null, chargingStationEvseId, EvseStatus.Available,
						AvailabilityStatus.Unknown, null, null, null, null))
				)
				.onItem()
				.call((evse) -> this.connectorRepository.updateConnectorStatusIfExists(evse.id(), evseConnectorId, connectorStatus,
						lastStatusUpdate, errorCode, errorInfo, vendorImplementationId, vendorErrorCode)
							.onItem()
							.ifNull()
							.switchTo(this.connectorRepository.createConnector(evse.id(), new Connector(null, evseConnectorId,
									connectorStatus, AvailabilityStatus.Unknown, lastStatusUpdate, errorCode, errorInfo, 
									vendorImplementationId, vendorErrorCode)))
				)
				.replaceWithVoid();
		});
	}
	
	@Override
	public Uni<TransactionEventResult> transactionEventReceived(final long chargingStationId, final Integer chargingStationEvseId,
			final Integer evseConnectorId, final String evseTransactionId, final EvseTransactionEvent transactionEvent) {
		
		return this.transactionalService.doInTransaction(() -> {
			return this.evseTransactionRepository.getEvseTransactionByTransactionId(chargingStationId, evseTransactionId)
				.onItem()
				.ifNull()
				.switchTo(this.evseRepository.getEvseByChargingStationEvseId(chargingStationId, chargingStationEvseId)
							.onItem()
							.transformToUni((evse) -> this.connectorRepository.getConnectorByEvseConnectorId(evse.id(), evseConnectorId)
														.onItem()
									.transform((connector) -> new Pair<>(evse.id(), connector.id()))
									.onFailure().recoverWithItem(() -> new Pair<>(evse.id(), null))
							)
							.onItem()
							.transformToUni((pair) -> this.evseTransactionRepository.createEvseTransaction(pair.first(), pair.second(),
									new EvseTransaction(null, evseTransactionId, pair.first(), pair.second())))
				)
				.onItem()
				.call((evseTransaction) -> this.evseTransactionEventRepository.createEvseTransactionEvent(evseTransaction.id(), 
						transactionEvent))
				
				//FIXME: implement result calculation, evse/connector status calulcation etc.
				.replaceWith(new TransactionEventResult(null, null, null, null))
				.onItem()
				.invoke((t) -> {
					System.out.print("asd");
				});
		})
		.onFailure()
		.invoke((e) -> {
			System.out.print(e);
		})
		.onItem()
		.invoke((t) -> {
			System.out.print("asd");
		})
				;
	}

	@Override
	public Uni<Long> getNextEvseTransactionId() {
		return evseTransactionRepository.getNextEvseTransactionId();
	}

	@Override
	public Uni<Void> requestStartTransactionResultReceived(final long chargingStationId, final RequestStartTransactionResult requestStartTransactionResult) {
		
		//FIXME: implement result handling
		return Uni.createFrom().voidItem();
	}

	@Override
	public Uni<Void> requestStopTransactionResultReceived(final long chargingStationId, final RequestStopTransactionResult requestStopTransactionResult) {
		
		//FIXME: implement result handling
		return Uni.createFrom().voidItem();
	}

	@Override
	public Uni<Void> setVariableResultReceived(final long chargingStationId, final List<SetVariableResult> setVariableResults) {
		
		//FIXME: implement result handling
		return Uni.createFrom().voidItem();
	}

	@Override
	public Uni<Void> getVariableResultReceived(final long chargingStationId, final List<GetVariableResult> getVariableResults) {
		
		//FIXME: implement result handling
		return Uni.createFrom().voidItem();
	}

	@Override
	public Uni<Void> changeAvailabilityResultReceived(final long chargingStationId, final ChangeAvailabilityResult changeAvailabilityResult) {
		
		//FIXME: implement result handling
		return Uni.createFrom().voidItem();
	}
	
	@Override
	public Uni<Void> resetResultReceived(final long chargingStationId, final ResetResult resetResult) {
		
		//FIXME: implement result handling
		return Uni.createFrom().voidItem();
	}

	@Override
	public Uni<Void> meterValueReceived(final long chargingStationId, final Integer chargingStationEvseId, 
			final Collection<MeterValue> meterValues) {
		return meterValueRepository.createMeterValues(chargingStationId, chargingStationEvseId, meterValues);
	}

	@Override
	public Uni<Void> getDiagnosticsResultReceived(long chargingStationId, GetDiagnosticsResult getDiagnosticsResult) {
		//FIXME: implement result handling
		return Uni.createFrom().voidItem();
	}

	@Override
	public Uni<Void> triggerMessageReceived(long chargingStationId, TriggerMessageResult triggerMessageResult) {
		//FIXME: implement result handling
		return Uni.createFrom().voidItem();
	}
}