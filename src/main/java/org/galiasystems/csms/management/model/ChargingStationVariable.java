package org.galiasystems.csms.management.model;

import java.math.BigDecimal;
import java.util.Collection;

import org.galiasystems.csms.management.model.enums.DataType;

public record ChargingStationVariable(Long id, String componentName, String componentInstance, Integer evseId, 
		Integer connectorId, String variableName, String variableInstance, String unit, DataType dataType,
		BigDecimal minLimit, BigDecimal maxLimit, String valuesList, Boolean supportsMonitoring,
		Collection<ChargingStationVariableValue> chargingStationVariableValues) {
}
