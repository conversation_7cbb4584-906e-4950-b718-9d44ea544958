package org.galiasystems.csms.management.model;

import java.io.Serializable;
import java.math.BigDecimal;

import org.galiasystems.csms.management.model.enums.*;

public record SampledValue(BigDecimal value, SampledValueReadingContext context, Measurand measurand,
						   MeasuredPhase phase, MeasurementLocation location, SignedMeterValue signedMeterValue,
						   UnitOfMeasure unitOfMeasure) implements Serializable {
	
	public static Builder builder() {
		return new Builder();
	}

	public static class Builder {
		private BigDecimal value;
		private SampledValueReadingContext context;
		private Measurand measurand;
		private MeasuredPhase phase;
		private MeasurementLocation location;
		private SignedMeterValue signedMeterValue;
		private UnitOfMeasure unitOfMeasure;

		public Builder value(BigDecimal value) {
			this.value = value;
			return this;
		}

		public Builder context(SampledValueReadingContext context) {
			this.context = context;
			return this;
		}

		public Builder measurand(Measurand measurand) {
			this.measurand = measurand;
			return this;
		}

		public Builder phase(MeasuredPhase phase) {
			this.phase = phase;
			return this;
		}

		public Builder location(MeasurementLocation location) {
			this.location = location;
			return this;
		}

		public Builder signedMeterValue(SignedMeterValue signedMeterValue) {
			this.signedMeterValue = signedMeterValue;
			return this;
		}

		public Builder unitOfMeasure(UnitOfMeasure unitOfMeasure) {
			this.unitOfMeasure = unitOfMeasure;
			return this;
		}

		public SampledValue build() {
			return new SampledValue(value, context, measurand, phase, location, signedMeterValue, unitOfMeasure);
		}
	}
}