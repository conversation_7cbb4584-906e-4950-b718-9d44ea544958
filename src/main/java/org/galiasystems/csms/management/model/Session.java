package org.galiasystems.csms.management.model;

import java.math.BigDecimal;
import java.net.URL;
import java.time.ZonedDateTime;
import java.util.List;

import org.galiasystems.csms.management.model.enums.AuthMethod;
import org.galiasystems.csms.management.model.enums.ChargingStationSessionStatus;
import org.galiasystems.csms.management.model.enums.SessionStatus;

/**
 * Represents a charging session at a Charge Point.
 */
public record Session(
    Long id,
    String externalSessionId,
    String countryCode,
    String partyId,
    URL startSessionResponseUrl,
    ZonedDateTime startDateTime,
    ZonedDateTime endDateTime,
    BigDecimal kwh,
    SessionToken sessionToken,
    AuthMethod authMethod,
    String authorizationReference,
    String locationId,
    String evseUid,
    String connectorId,
    String meterId,
    String currency,
    List<ChargingPeriod> chargingPeriods,
    Price totalCost,
    ChargingStationSessionStatus chargingStationSessionStatus,
    SessionStatus status,
    ZonedDateTime lastUpdated
) {
    /**
     * Builder for creating Session instances.
     */
    public static class Builder {
        private Long id;
        private String externalSessionId;
        private String countryCode;
        private String partyId;
        private URL startSessionResponseUrl;
        private ZonedDateTime startDateTime;
        private ZonedDateTime endDateTime;
        private BigDecimal kwh;
        private SessionToken sessionToken;
        private AuthMethod authMethod;
        private String authorizationReference;
        private String locationId;
        private String evseUid;
        private String connectorId;
        private String meterId;
        private String currency;
        private List<ChargingPeriod> chargingPeriods;
        private Price totalCost;
        private ChargingStationSessionStatus chargingStationSessionStatus;
        private SessionStatus status;
        private ZonedDateTime lastUpdated;

        public Builder id(final Long id) {
            this.id = id;
            return this;
        }
        
        public Builder externalSessionId(final String externalSessionId) {
            this.externalSessionId = externalSessionId;
            return this;
        }

        public Builder countryCode(final String countryCode) {
            this.countryCode = countryCode;
            return this;
        }

        public Builder partyId(final String partyId) {
            this.partyId = partyId;
            return this;
        }
        
        public Builder startSessionResponseUrl(final URL startSessionResponseUrl) {
            this.startSessionResponseUrl = startSessionResponseUrl;
            return this;
        }

        public Builder startDateTime(final ZonedDateTime startDateTime) {
            this.startDateTime = startDateTime;
            return this;
        }

        public Builder endDateTime(final ZonedDateTime endDateTime) {
            this.endDateTime = endDateTime;
            return this;
        }

        public Builder kwh(final BigDecimal kwh) {
            this.kwh = kwh;
            return this;
        }

        public Builder sessionToken(final SessionToken sessionToken) {
            this.sessionToken = sessionToken;
            return this;
        }

        public Builder authMethod(final AuthMethod authMethod) {
            this.authMethod = authMethod;
            return this;
        }

        public Builder authorizationReference(final String authorizationReference) {
            this.authorizationReference = authorizationReference;
            return this;
        }

        public Builder locationId(final String locationId) {
            this.locationId = locationId;
            return this;
        }

        public Builder evseUid(final String evseUid) {
            this.evseUid = evseUid;
            return this;
        }

        public Builder connectorId(final String connectorId) {
            this.connectorId = connectorId;
            return this;
        }

        public Builder meterId(final String meterId) {
            this.meterId = meterId;
            return this;
        }

        public Builder currency(final String currency) {
            this.currency = currency;
            return this;
        }

        public Builder chargingPeriods(final List<ChargingPeriod> chargingPeriods) {
            this.chargingPeriods = chargingPeriods;
            return this;
        }

        public Builder totalCost(final Price totalCost) {
            this.totalCost = totalCost;
            return this;
        }
        
        public Builder chargingStationSessionStatus(final ChargingStationSessionStatus chargingStationSessionStatus) {
            this.chargingStationSessionStatus = chargingStationSessionStatus;
            return this;
        }

        public Builder status(final SessionStatus status) {
            this.status = status;
            return this;
        }

        public Builder lastUpdated(final ZonedDateTime lastUpdated) {
            this.lastUpdated = lastUpdated;
            return this;
        }

        public Session build() {
            return new Session(
                id, externalSessionId, countryCode, partyId, startSessionResponseUrl, startDateTime, endDateTime, kwh, 
                sessionToken, authMethod, authorizationReference, locationId, evseUid, connectorId, meterId,
                currency, chargingPeriods, totalCost, chargingStationSessionStatus, status, lastUpdated
            );
        }
    }
}