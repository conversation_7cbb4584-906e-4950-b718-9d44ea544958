package org.galiasystems.csms.management.model.enums;

public enum CdrDimensionType {
    /**
     * Average charging current during this ChargingPeriod: defined in A (Ampere).
     * When negative, the current is flowing from the EV to the grid.
     */
    CURRENT(true),
    /**
     * Total amount of energy (dis-)charged during this ChargingPeriod: defined in
     * kWh. When negative, more energy was feed into the grid then charged into the
     * EV. Default step_size is 1.
     */
    ENERGY(false),
    /**
     * Total amount of energy feed back into the grid: defined in kWh.
     */
    ENERGY_EXPORT(true),
    /**
     * Total amount of energy charged, defined in kWh.
     */
    ENERGY_IMPORT(true),
    /**
     * Sum of the maximum current over all phases, reached during this
     * ChargingPeriod: defined in A (Ampere).
     */
    MAX_CURRENT(false),
    /**
     * Sum of the minimum current over all phases, reached during this
     * ChargingPeriod, when negative, current has flowed from the EV to the grid.
     * Defined in A (Ampere).
     */
    MIN_CURRENT(false),
    /**
     * Maximum power reached during this ChargingPeriod: defined in kW (Kilowatt).
     */
    MAX_POWER(false),
    /**
     * Minimum power reached during this ChargingPeriod: defined in kW (Kilowatt),
     * when negative, the power has flowed from the EV to the grid.
     */
    MIN_POWER(false),
    /**
     * Time during this ChargingPeriod not charging: defined in hours, default
     * step_size multiplier is 1 second.
     */
    PARKING_TIME(false),
    /**
     * Average power during this ChargingPeriod: defined in kW (Kilowatt). When
     * negative, the power is flowing from the EV to the grid.
     */
    POWER(true),
    /**
     * Time during this ChargingPeriod Charge Point has been reserved and not yet
     * been in use for this customer: defined in hours, default step_size multiplier is 1
     * second.
     */
    RESERVATION_TIME(false),
    /**
     * Current state of charge of the EV, in percentage, values allowed: 0 to 100. See
     * note below.
     */
    STATE_OF_CHARGE(true),
    /**
     * Time charging during this ChargingPeriod: defined in hours, default step_size
     * multiplier is 1 second.
     */
    TIME(false);

    private final boolean sessionOnly;

    CdrDimensionType(boolean sessionOnly) {
        this.sessionOnly = sessionOnly;
    }

    public boolean isSessionOnly() {
        return sessionOnly;
    }
}