package org.galiasystems.csms.management.model;

import java.math.BigDecimal;

/**
 * @param name           A description of the tax. In countries where a tax name is required like
 *                       Canada this can be something like "QST". In countries where this is not
 *                       required, this can be something more generic like "VAT" or "General
 *                       Sales Tax".
 *                       <p>
 *                       Field Type: string
 *                       Cardinality: 1
 * @param account_number Tax Account Number of the business entity remitting these taxes.
 *                       Optional as this is not required in all countries.
 *                       <p>
 *                       Field Type: string
 *                       Cardinality: ?
 * @param percentage     Tax percentage. Optional as this is not required in all countries.
 *                       <p>
 *                       Field Type: BigDecimal
 *                       Cardinality: ?
 * @param amount         The amount of money of this tax that is due.
 *                       <p>
 *                       Field Type: BigDecimal
 *                       Cardinality: 1
 */
public record TaxAmount(String name, String account_number, BigDecimal percentage, BigDecimal amount) {
}