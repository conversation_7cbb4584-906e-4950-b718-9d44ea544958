package org.galiasystems.csms.management.model;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 *
 * @param before_taxes    Price/Cost excluding taxes.
 *                      <p>
 *                      Field Type: BigDecimal
 *                      Cardinality: 1
 *
 * @param taxes          All taxes that are applicable to this price and relevant to the receiver of the Session or CDR.
 *                      <p>
 *                      Field Type: TaxAmount
 *                      Cardinality: *
 */
public record Price(BigDecimal before_taxes, TaxAmount taxes) implements Serializable {
}