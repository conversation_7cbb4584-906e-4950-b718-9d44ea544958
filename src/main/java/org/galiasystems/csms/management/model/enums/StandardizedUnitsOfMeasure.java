package org.galiasystems.csms.management.model.enums;

/**
 * The standardized values for Unit of Measure. 
 * Default value of "unit" is always "Wh".
 */
public enum StandardizedUnitsOfMeasure {
	
	/**
     * Amperes (current).
     */
    A,

    /**
     * Arbitrary Strength Unit (Signal Strength).
     */
    ASU,
	
    /**
     * Bytes
     */
    B,
    
    /**
     * Degrees (temperature).
     */
    Celsius,
    
    /**
     * Decibel (for example Signal Strength).
     */
    dB,
  
    /**
     * Power relative to 1mW (10log(P/1mW)).
     */
    dBm,
    
    /**
     * Degrees (angle/rotation).
     */
    Deg,
    
    /**
     * Degrees (temperature).
     */
    Fahrenheit,

    /**
     * Hertz (frequency).
     */
    Hz,
    
    /**
     * Degrees Kelvin (temperature).
     */
    K,
    
    /**
     * Lux (Light Intensity).
     */
    lx,
    
    /**
     * Meter (length).
     */
    m,
    
    /**
     * m/s2 (Acceleration).
     */
    ms2,
    
    /**
     * <PERSON><PERSON> (Force).
     */
    N,
    
    /**
     * Ohm (Impedance).
     */
    Ohm,
    
    /**
     * kiloPascal (Pressure).
     */
    kPa,
    
    /**
     * Percentage.
     */
    Percent,
    
    /**
     * Relative Humidity%,
     */
    RH,
    
    /**
     * Revolutions per Minute.
     */
    RPM,

    /**
     * Seconds (Time).
     */
    s,
    
    /**
     * Voltage (r.m.s. AC).
     */
    V,
    
    /**
     * Volt-Ampere (apparent power).
     */
    VA,
    
    /**
     * kiloVolt Ampere (apparent power).
     */
    kVA,
    
    /**
     * Volt-Ampere-hours (apparent energy).
     */
    VAh,
    
    /**
     * kiloVolt-Ampere-hours (apparent energy),
     */
    kVAh,
    
    /**
     * vars (reactive power).
     */
    var, 
    
    /**
     * kilovars (reactive power).
     */
    kvar,
    
    /**
     * var-hours (reactive energy).
     */
    varh,
    
    /**
     * kilovar-hours (reactive energy).
     */
    kvarh,
    
    /**
     * Watts (power)
     */
    W,
    
    /**
     * kilowatts (power).
     */
    kW,
    
    /**
     * Watt-hours (energy). Default.
     */
    Wh,
    
    /**
     * kilowatt-hours (energy).
     */
    kWh,
    ;
}