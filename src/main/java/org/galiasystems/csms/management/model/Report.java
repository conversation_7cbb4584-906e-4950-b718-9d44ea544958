package org.galiasystems.csms.management.model;

import org.galiasystems.csms.management.model.enums.ReportStatus;
import org.galiasystems.csms.management.model.enums.ReportType;
import org.galiasystems.csms.management.types.enums.ReportResultStatus;

public class Report {
	
	private Long id;
	
	private Long requestId;
	
	private ReportType type;
	
	private ReportStatus status;
	
	private ReportResultStatus responseStatus;
	
	private String responseReasonCode;
	
	private String responseAdditionalInfo;
	
	private ChargingStation chargingStation;
	
	
	public Report() {
		super();
	}
    
    public Report(final Long id, final Long requestId, final ReportType type, final ReportStatus status, 
    		final ReportResultStatus responseStatus, final String responseReasonCode, 
    		final String responseAdditionalInfo) {
    	this();
    	this.id = id;
		this.requestId = requestId;
		this.type = type;
		this.status = status;
		this.responseStatus = responseStatus;
		this.responseReasonCode = responseReasonCode;
		this.responseAdditionalInfo = responseAdditionalInfo;
		
	}

	public Long getId() {
		return id;
	}

	public void setId(final Long id) {
		this.id = id;
	}

	public Long getRequestId() {
		return requestId;
	}

	public void setRequestId(final Long requestId) {
		this.requestId = requestId;
	}

	public ReportType getType() {
		return type;
	}

	public void setType(final ReportType type) {
		this.type = type;
	}

	public ReportStatus getStatus() {
		return status;
	}

	public void setStatus(final ReportStatus status) {
		this.status = status;
	}

	public ReportResultStatus getResponseStatus() {
		return responseStatus;
	}

	public void setResponseStatus(final ReportResultStatus responseStatus) {
		this.responseStatus = responseStatus;
	}

	public String getResponseReasonCode() {
		return responseReasonCode;
	}

	public void setResponseReasonCode(final String responseReasonCode) {
		this.responseReasonCode = responseReasonCode;
	}

	public String getResponseAdditionalInfo() {
		return responseAdditionalInfo;
	}

	public void setResponseAdditionalInfo(final String responseAdditionalInfo) {
		this.responseAdditionalInfo = responseAdditionalInfo;
	}
	
	public ChargingStation getChargingStation() {
		return chargingStation;
	}

	public void setChargingStation(final ChargingStation chargingStation) {
		this.chargingStation = chargingStation;
	}
}
