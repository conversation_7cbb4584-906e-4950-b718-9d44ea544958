package org.galiasystems.csms.management.model;

import java.io.Serializable;
import java.time.ZonedDateTime;
import java.util.Collection;

/**
 * All sampled values in a
 * MeterValue are sampled at the same point in time.
 *
 * @param timestamp     Required. Timestamp for measured value(s)
 *                      Field type:dateTime
 *                      Cardinality: 1..1
 * @param sampledValues Required. One or more measured values
 *                      Field type: SampledValue
 *                      Cardinality: 1..*
 */
public record MeterValue(ZonedDateTime timestamp, Collection<SampledValue> sampledValues) implements Serializable {
}