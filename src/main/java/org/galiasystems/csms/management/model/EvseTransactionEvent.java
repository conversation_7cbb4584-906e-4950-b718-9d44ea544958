package org.galiasystems.csms.management.model;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;

import org.galiasystems.csms.management.model.enums.TransactionChargingState;
import org.galiasystems.csms.management.model.enums.TransactionEventTriggerReason;
import org.galiasystems.csms.management.model.enums.TransactionEventType;
import org.galiasystems.csms.management.model.enums.TransactionReason;

public record EvseTransactionEvent(Long id, TransactionEventType eventType, Date timestamp, TransactionEventTriggerReason triggerReason,
		Integer eventSeqNo, Boolean offline, Integer numberOfPhasesUsed, Integer cableMaxCurrent, Integer reservationId,
								   TransactionChargingState chargingState, Integer timeSpentCharging,
								   TransactionReason stoppedReason, Integer remoteStartId,
		IdToken idToken, Collection<MeterValue> meterValues) {

	public static Builder builder() {
		return new Builder();
	}

	public static class Builder {
		private Long id;
		private TransactionEventType eventType;
		private Date timestamp;
		private TransactionEventTriggerReason triggerReason;
		private Integer eventSeqNo;
		private Boolean offline;
		private Integer numberOfPhasesUsed;
		private Integer cableMaxCurrent;
		private Integer reservationId;
		private TransactionChargingState chargingState;
		private Integer timeSpentCharging;
		private TransactionReason stoppedReason;
		private Integer remoteStartId;
		private IdToken idToken;
		private Collection<MeterValue> meterValues = new ArrayList<>();

		public Builder id(Long id) {
			this.id = id;
			return this;
		}

		public Builder eventType(TransactionEventType eventType) {
			this.eventType = eventType;
			return this;
		}

		public Builder timestamp(Date timestamp) {
			this.timestamp = timestamp;
			return this;
		}

		public Builder triggerReason(TransactionEventTriggerReason triggerReason) {
			this.triggerReason = triggerReason;
			return this;
		}

		public Builder eventSeqNo(Integer eventSeqNo) {
			this.eventSeqNo = eventSeqNo;
			return this;
		}

		public Builder offline(Boolean offline) {
			this.offline = offline;
			return this;
		}

		public Builder numberOfPhasesUsed(Integer numberOfPhasesUsed) {
			this.numberOfPhasesUsed = numberOfPhasesUsed;
			return this;
		}

		public Builder cableMaxCurrent(Integer cableMaxCurrent) {
			this.cableMaxCurrent = cableMaxCurrent;
			return this;
		}

		public Builder reservationId(Integer reservationId) {
			this.reservationId = reservationId;
			return this;
		}

		public Builder chargingState(TransactionChargingState chargingState) {
			this.chargingState = chargingState;
			return this;
		}

		public Builder timeSpentCharging(Integer timeSpentCharging) {
			this.timeSpentCharging = timeSpentCharging;
			return this;
		}

		public Builder stoppedReason(TransactionReason stoppedReason) {
			this.stoppedReason = stoppedReason;
			return this;
		}

		public Builder remoteStartId(Integer remoteStartId) {
			this.remoteStartId = remoteStartId;
			return this;
		}

		public Builder idToken(IdToken idToken) {
			this.idToken = idToken;
			return this;
		}

		public Builder meterValues(Collection<MeterValue> meterValues) {
			this.meterValues = meterValues;
			return this;
		}

		public EvseTransactionEvent build() {
			return new EvseTransactionEvent(id, eventType, timestamp, triggerReason, eventSeqNo, offline,
					numberOfPhasesUsed, cableMaxCurrent, reservationId, chargingState, timeSpentCharging,
					stoppedReason, remoteStartId, idToken, meterValues);
		}
	}
}