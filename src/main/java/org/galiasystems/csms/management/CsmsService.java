package org.galiasystems.csms.management;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;

import java.util.Collection;

import org.galiasystems.csms.management.csms.base.CsmsServiceBase;
import org.galiasystems.csms.management.model.ChargingStationVariable;
import org.galiasystems.csms.management.model.Floor;
import org.galiasystems.csms.management.model.Location;
import org.galiasystems.csms.management.model.PowerGrid;
import org.galiasystems.csms.management.model.Tenant;
import org.galiasystems.csms.management.repository.ChargingStationVariableRepository;
import org.galiasystems.csms.management.repository.FloorRepository;
import org.galiasystems.csms.management.repository.LocationRepository;
import org.galiasystems.csms.management.repository.PowerGridRepository;
import org.galiasystems.csms.management.repository.TenantRepository;
import org.galiasystems.csms.management.repository.TransactionalService;
import org.galiasystems.csms.repository.Transactional;
import org.jboss.logging.Logger;

import io.smallrye.mutiny.Uni;


@ApplicationScoped
public class CsmsService extends CsmsServiceBase implements Csms  {

    @Inject
    private Logger log;
    
    @Inject
    private TenantRepository tenantRepository;
    
    @Inject
    private LocationRepository locationRepository;
    
    @Inject
    private FloorRepository floorRepository;
    
    @Inject
    private PowerGridRepository powerGridRepository;
    
    @Inject
    private ChargingStationVariableRepository chargingStationVariableRepository;
    
    @Inject
    private TransactionalService transactionalService;

	@Override
	public Uni<Collection<ChargingStationVariable>> getChargingStationVariables(
			final long chargingStationId) {
		return this.chargingStationVariableRepository.getChargingStationVariables(chargingStationId);
	}
	
	@Override
	@Transactional
	public Uni<Tenant> createTenant(final Tenant tenant) {
		
		return this.transactionalService.doInTransaction(() -> {
					
					
					var result = this.tenantRepository
					.createTenant(tenant)
					.invoke((createdTenant) -> {
						log.info("Tenant was created: " + createdTenant.id());
					})
					.onItem()
					.call((createdTenant) -> {
						final Location location = new Location(null, Location.DEFAULT_LOCATION_NAME);
						return createLocation(createdTenant.id(), location);
					});
					
					
					
				
					
					return result;
				}
				);
		
		
//		return this.tenantRepository
//				.createTenant(tenant)
//				.invoke((createdTenant) -> {
//					log.info("Tenant was created: " + createdTenant.id());
//				})
//				.onItem()
//				.call((createdTenant) -> {
//					final Location location = new Location(null, Location.DEFAULT_LOCATION_NAME);
//					return createLocation(createdTenant.id(), location);
//				});
	}

	@Override
	public Uni<Tenant> updateTenant(final Tenant tenant) {
		return this.tenantRepository.updateTenant(tenant);
	}
	
	@Override
	public Uni<Collection<Tenant>> getTenants() {
		return this.tenantRepository.getTenants();
	}

	@Override
	public Uni<Tenant> getTenant(final long id) {
		return this.tenantRepository.getTenant(id);
	}
	
	@Override
	public Uni<Location> createLocation(final Location location) {
		final long tenantId = getCurrentUserTenantId();
		return createLocation(tenantId, location);
		
	}
	
	private Uni<Location> createLocation(final long tenantId, final Location location) {
		return this.locationRepository
				.createLocation(tenantId, location)
				.invoke((createdLocation) -> {
					log.info("Location was created: " + createdLocation.id());
				})
				.onItem()
				.call((createdLocation) -> {
					final PowerGrid powerGrid = new PowerGrid(null, PowerGrid.DEFAULT_POWER_GRID_NAME);
					return createPowerGrid(createdLocation.id(), powerGrid);
				});
	}

	@Override
	public Uni<Collection<Location>> getLocations() {
		final long tenantId = getCurrentUserTenantId();
		return this.locationRepository.getLocationsOf(tenantId);
	}

	@Override
	public Uni<Location> getLocation(final long id) {
		return this.locationRepository.getLocation(id);
	}
	
	@Override
	public Uni<Floor> createFloor(final long locationId, final Floor floor) {
		return this.floorRepository.createFloor(locationId, floor);
	}

	@Override
	public Uni<Collection<Floor>> getFloors(final long locationId) {
		return this.floorRepository.getFloorsOf(locationId);
	}

	@Override
	public Uni<Floor> getFloor(final long id) {
		return this.floorRepository.getFloor(id);
	}

	@Override
	public Uni<PowerGrid> createPowerGrid(final long locationId, final PowerGrid powerGrid) {
		return this.powerGridRepository
				.createPowerGrid(locationId, powerGrid)
				.invoke((createdLocation) -> {
					log.info("Location was created: " + createdLocation.id());
				});
	}

	@Override
	public Uni<Collection<PowerGrid>> getPowerGrids(final long locationId) {
		return this.powerGridRepository.getPowerGridsOf(locationId);
	}

	@Override
	public Uni<PowerGrid> getPowerGrid(final long id) {
		return this.powerGridRepository.getPowerGrid(id);
	}

	@Override
	public Uni<Location> updateLocation(final Location location) {
		return this.locationRepository.updateLocation(location);
	}
}