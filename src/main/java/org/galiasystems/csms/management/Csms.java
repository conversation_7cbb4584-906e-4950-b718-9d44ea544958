package org.galiasystems.csms.management;

import java.util.Collection;

import org.galiasystems.csms.management.model.ChargingStationVariable;
import org.galiasystems.csms.management.model.Floor;
import org.galiasystems.csms.management.model.Location;
import org.galiasystems.csms.management.model.PowerGrid;
import org.galiasystems.csms.management.model.Tenant;

import io.smallrye.mutiny.Uni;

public interface Csms {
	
	Uni<Tenant> createTenant(final Tenant tenant);
	
	Uni<Tenant> updateTenant(final Tenant tenant);
	
	Uni<Collection<Tenant>> getTenants();
	
	Uni<Tenant> getTenant(final long id);
	
	Uni<Location> createLocation(final Location location);
	
	Uni<Collection<Location>> getLocations();
	
	Uni<Location> getLocation(final long id);
	
	Uni<Floor> createFloor(final long locationId, final Floor floor);
	
	Uni<Collection<Floor>> getFloors(final long locationId);
	
	Uni<Floor> getFloor(final long id);
	
	Uni<PowerGrid> createPowerGrid(final long locationId, final PowerGrid powerGrid);
	
	Uni<Collection<PowerGrid>> getPowerGrids(final long locationId);
	
	Uni<PowerGrid> getPowerGrid(final long id);
	
	Uni<Collection<ChargingStationVariable>> getChargingStationVariables(final long chargingStationId);
	
	Uni<Location> updateLocation(final Location location);
}