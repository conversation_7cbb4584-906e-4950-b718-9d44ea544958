package org.galiasystems.csms.management.types;

import org.galiasystems.csms.management.model.enums.VariableValueType;
import org.galiasystems.csms.management.types.enums.GetVariableResultStatus;

public record GetVariableResult(GetVariableResultStatus resultStatus, VariableValueType variableValueType, 
		String attributeValue, String componentName, String componentInstance, Integer evseId, Integer evseConnectorId, 
		String variableName, String variableInstance, String reasonCode, String additionalInfo) {
}