package org.galiasystems.csms.management.types;

import org.galiasystems.csms.management.model.enums.VariableValueType;
import org.galiasystems.csms.management.types.enums.SetVariableResultStatus;

public record SetVariableResult(SetVariableResultStatus resultStatus, VariableValueType variableValueType,
		String componentName, String componentInstance, Integer evseId, Integer evseConnectorId, 
		String variableName, String variableInstance, String reasonCode, String additionalInfo) {
}