###########################
###### Common configs #####
###########################

quarkus.http.port=8888
#quarkus.http.test-port=0
#quarkus.log.level=ERROR
quarkus.http.cors=true

quarkus.hibernate-orm.physical-naming-strategy=org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy

hibernate.reactive.panache.sessionOnDemandOpened=true

# security
quarkus.http.auth.basic=true

quarkus.ssl.native=true

quarkus.http.ssl-port=8443

# TODO use tls registry: https://quarkus.io/guides/tls-registry-reference
quarkus.http.ssl.certificate.key-store-file=META-INF/resources/server.keystore
quarkus.http.ssl.certificate.key-store-password=password

# none, request, required
quarkus.http.ssl.client-auth=request
quarkus.http.insecure-requests=enabled
quarkus.http.ssl.certificate.trust-store-file=META-INF/resources/server.truststore
quarkus.http.ssl.certificate.trust-store-password=password

# OAuth2
#quarkus.oidc-client.credentials.secret=secret
quarkus.oidc-client.grant.type=code
#quarkus.http.auth.permission.authenticated.paths=/*
#quarkus.http.auth.permission.authenticated.policy=deny
#quarkus.smallrye-jwt.enabled=true

# CSMS
csms.oauth2.userTokens.cookie.sessionId.name=SESSION_ID
csms.oauth2.userTokens.cookie.accessToken.name=ACCESS_TOKEN
csms.oauth2.userTokens.cookie.sameSite=LAX
csms.oauth2.userTokens.cookie.domain=localhost
csms.oauth2.userTokens.cookie.secure=true
csms.oauth2.userTokens.cookie.httpOnly=true
csms.oauth2.userTokens.cookie.path=/

###########################
###### Dev profile ########
###########################
# Datasource configs
%dev.quarkus.datasource.devservices.port=53277

%dev.quarkus.log.category."org.galiasystems".level=DEBUG
# %dev.quarkus.log.level=DEBUG
# %dev.quarkus.http.access-log.enabled=true

%dev.quarkus.http.cors.origins=/.*/
%dev.quarkus.http.cors.access-control-allow-credentials=true

# Hibernate configs
%dev.quarkus.hibernate-orm.log.sql=true
%dev.quarkus.hibernate-orm.database.generation=drop-and-create
%dev.quarkus.hibernate-orm.sql-load-script=db/data/import.sql

# Flyway config
%dev.quarkus.flyway.enabled=false

# OAuth2
%dev.quarkus.oidc-client.auth-server-url=http://localhost:8180/realms/flutter-app/
%dev.quarkus.oidc-client.client-id=flutter-client
%dev.quarkus.oidc-client.credentials.secret=63vMfP5b0xa8Pjx6KbNquYqggP6Z76jC
#%dev.quarkus.oidc.auth-server-url=http://localhost:8180/realms/flutter-app/
%dev.quarkus.rest-client.custom-keycloak.url=http://localhost:8180/realms/flutter-app

# CSMS
%dev.csms.oauth2.userTokens.cookie.sessionId.name=SESSION_ID
%dev.csms.oauth2.userTokens.cookie.accessToken.name=ACCESS_TOKEN
%dev.csms.oauth2.userTokens.cookie.sameSite=LAX
%dev.csms.oauth2.userTokens.cookie.domain=localhost
%dev.csms.oauth2.userTokens.cookie.secure=true
%dev.csms.oauth2.userTokens.cookie.httpOnly=true
%dev.csms.oauth2.userTokens.cookie.path=/

# Mock OidcClient
%dev.quarkus.arc.selected-alternatives=org.galiasystems.csms.security.login.oauth2.MockOidcClient
###########################
###### Test profile #######
###########################
# Datasource configs
#%test.quarkus.datasource.devservices.port=53277
%test.quarkus.http.host=0.0.0.0
%test.quarkus.http.port=8081
# Hibernate configs
%test.quarkus.hibernate-orm.log.sql=true
%test.quarkus.hibernate-orm.database.generation=drop-and-create
%test.quarkus.hibernate-orm.sql-load-script=db/data/import.sql
# Mock OidcClient
%test.quarkus.arc.selected-alternatives=org.galiasystems.csms.security.login.oauth2.MockOidcClient
# CSMS
%test.csms.oauth2.userTokens.cookie.sessionId.name=SESSION_ID
%test.csms.oauth2.userTokens.cookie.accessToken.name=ACCESS_TOKEN
%test.csms.oauth2.userTokens.cookie.sameSite=LAX
%test.csms.oauth2.userTokens.cookie.domain=localhost
%test.csms.oauth2.userTokens.cookie.secure=true
%test.csms.oauth2.userTokens.cookie.httpOnly=true
%test.csms.oauth2.userTokens.cookie.path=/
# OAuth2
%test.quarkus.rest-client.custom-keycloak.url=http://localhost:8180/realms/flutter-app

# Flyway config
%test.quarkus.flyway.enabled=false

###########################
###### Prod profile #######
###########################
%prod.quarkus.datasource.db-kind=postgresql
%prod.quarkus.datasource.username=quarkus_test
%prod.quarkus.datasource.password=quarkus_test

# Reactive config
%prod.quarkus.datasource.reactive.url=vertx-reactive:postgresql://localhost/quarkus_test
%prod.quarkus.datasource.reactive.max-size=20

# Flyway config
%prod.quarkus.flyway.enabled=true

# JDBC config
%prod.quarkus.datasource.jdbc.url=****************************************

%prod.quarkus.hibernate-orm.database.generation = none
%prod.quarkus.hibernate-orm.sql-load-script = no-file



###########################
###### Samples      #######
###########################


# Run Flyway migrations automatically
# quarkus.flyway.migrate-at-start=true

#manually by injecting the Flyway object and calling Flyway#repair().
# quarkus.flyway.repair-at-start=true

# More Flyway configuration options
# quarkus.flyway.baseline-on-migrate=true
# quarkus.flyway.baseline-version=1.0.0
# quarkus.flyway.baseline-description=Initial version
# quarkus.flyway.connect-retries=10
# quarkus.flyway.schemas=TEST_SCHEMA
# quarkus.flyway.table=flyway_quarkus_history
# quarkus.flyway.locations=db/location1,db/location2
# quarkus.flyway.sql-migration-prefix=X
# quarkus.flyway.repeatable-sql-migration-prefix=K
# %dev.quarkus.flyway.clean-at-start=true

# %dev-with-data.quarkus.hibernate-orm.database.generation = update
# %dev-with-data.quarkus.hibernate-orm.sql-load-script = no-file