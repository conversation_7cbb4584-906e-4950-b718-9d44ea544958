insert into tenant(id, name, version) values (-1, 'DefaultTenant', 0);

insert into location(id, tenant_id, name, version) values (-1, -1, 'DefaultLocation', 0);

insert into floor(id, location_id, level, name, layout, row_count, column_count, version) values (-1, -1, 0, 'DefaultFloor', 'Rectangle', 10, 10, 0);

insert into power_grid(id, location_id, name, version) values (-1, -1, 'DefaultPowerGrid', 0);

insert into charging_station(id, power_grid_id, name, status, availability_status, version, url) values (-1, -1, 'CP0', 'Offline', 'Unknown', 0, 'http://localhost:8080/ocpp/-1');

insert into charging_station(id, power_grid_id, name, status, availability_status, version, url) values (-2, -1, 'CP1', 'Offline', 'Unknown', 0, 'http://localhost:8080/ocpp/-2');

insert into evse(id, charging_station_id, charging_station_evse_id, status, availability_status, parking_floor, parking_row, parking_column, parking_place_id, version) values (-1, -1, 1, 'Available', 'Operative', null, null, null, null, 0);

insert into connector(id, evse_id, evse_connector_id, status, availability_status, version) values (-1, -1, 0, 'Available', 'Operative', 0);

insert into evse_transaction(id, evse_id, connector_id, evse_transaction_id, version) values (-1, -1, -1, '225fc33a-a9b8-453d-ae95-c6283ff580ff', 0);

insert into evse_transaction_event(id, transaction_id, event_type, timestamp, trigger_reason, event_seq_no, offline, number_of_phases_used, cable_max_current, reservation_id, charging_state, time_spent_charging, stopped_reason, remote_start_id, id_token, meter_values, version) values (-1, -1, 'Started', '2024-10-20 15:52:53.458116+00', 'Authorized', 1, false, 1, 1, 1, 'Charging', 100, 'DeAuthorized', 1, '{"idToken": "idToken", "type": "Central", "additionalIdTokens": [{"idToken": "additionalIdToken1", "type": "additionalIdToken1_type"}, {"idToken": "additionalIdToken2", "type": "additionalIdToken2_type"}]}', '[{"timestamp": "2024-08-04T15:57:38.018+00:00", "sampledValues": [{"value": 1.1}, {"value": 1.2}]}, {"timestamp": "2024-08-04T15:57:38.018+00:00", "sampledValues": [{"value": 2.1}, {"value": 2.2}]}]', 0);

insert into report(id, request_id, charging_station_id, status, type, response_additional_info, response_reason_code, response_status, version) values (-1, -1, -1, 'Requested', 'BaseReport_FullInventory', 'response_additional_info', 'response_reason_code', 'Accepted', 0);

insert into charging_station_variable(id, charging_station_id, component_name, component_instance, evse_id, connector_id, variable_name, variable_instance, unit, data_type, min_limit, max_limit, values_list, supports_monitoring, version) values (-1, -1, 'component_name', 'component_instance', 1, 1, 'variable_name_full', 'variable_instance', 'unit', 'String', 1, 10, 'values_list', true, 0);
insert into charging_station_variable_value(id, charging_station_variable_id, type, value, mutability, version) values (-1, -1, 'Actual', 'actual value', 'ReadWrite', 0);
insert into charging_station_variable_value(id, charging_station_variable_id, type, value, mutability, version) values (-2, -1, 'Target', 'target value', 'WriteOnly', 0);
insert into charging_station_variable_value(id, charging_station_variable_id, type, value, mutability, version) values (-3, -1, 'MinSet', 'min value', 'ReadOnly', 0);
insert into charging_station_variable_value(id, charging_station_variable_id, type, value, mutability, version) values (-4, -1, 'MaxSet', 'max value', 'ReadOnly', 0);

insert into charging_station_variable(id, charging_station_id, component_name, component_instance, evse_id, connector_id, variable_name, variable_instance, unit, data_type, min_limit, max_limit, values_list, supports_monitoring, version) values (-2, -1, 'component_name', null, null, null, 'variable_name_min', null, null, null, null, null, null, null, 0);
insert into charging_station_variable_value(id, charging_station_variable_id, type, value, mutability, version) values (-5, -2, null, null, null, 0);

insert into csms_user(id, tenant_id, user_name, password, version) values (-1, -1, -1, '$2a$10$P412KssO7zvMkaK7ZuIKQO0xqhm16ljL.vaLS8IkYLCzxS3/xm7g6', 0);
insert into csms_user_role(id, user_id, role, location_id, power_grid_id, charging_station_id, version) values (-1, -1, 'ChargingStation', null, null, -1, 0);

insert into csms_user(id, tenant_id, user_name, password, version) values (-2, -1, 'defaulttenantadmin', null, 0);
insert into csms_user_role(id, user_id, role, location_id, power_grid_id, charging_station_id, version) values (-2, -2, 'TenantAdmin', null, null, null, 0);

insert into csms_user(id, tenant_id, user_name, password, version) values (-3, null, 'globaladmin', null, 0);
insert into csms_user_role(id, user_id, role, location_id, power_grid_id, charging_station_id, version) values (-3, -3, 'GlobalAdmin', null, null, null, 0);

insert into csms_user(id, tenant_id, user_name, password, version) values (-4, -1, 'flutteruser', null, 0);
insert into csms_user_role(id, user_id, role, location_id, power_grid_id, charging_station_id, version) values (-4, -4, 'ChargingStationAdmin', null, null, -1, 0);

CREATE SEQUENCE IF NOT EXISTS transaction_id_seq START WITH 1 INCREMENT BY 1;

CREATE SEQUENCE IF NOT EXISTS meter_value_id_seq START WITH 1 INCREMENT BY 1;

-- Meter value inserts for testing purposes
-- Energy_Active_Import_Register values (kWh) for charging station -1, evse 0
INSERT INTO meter_value (id, timestamp, value, context, measurand, phase, location, signed_meter_value, unit_of_measure, charging_station_id, charging_station_evse_id, version) VALUES
(-101, NOW() - INTERVAL '7 days', 0.50, 'Sample_Clock', 'Energy_Active_Import_Register', NULL, 'Outlet', NULL, '{"unit": "Wh"}', -1, 0, 0),
(-102, NOW() - INTERVAL '6 days', 1.75, 'Sample_Clock', 'Energy_Active_Import_Register', NULL, 'Outlet', NULL, '{"unit": "Wh"}', -1, 0, 0),
(-103, NOW() - INTERVAL '5 days', 3.25, 'Sample_Clock', 'Energy_Active_Import_Register', NULL, 'Outlet', NULL, '{"unit": "Wh"}', -1, 0, 0),
(-104, NOW() - INTERVAL '4 days', 5.00, 'Sample_Clock', 'Energy_Active_Import_Register', NULL, 'Outlet', NULL, '{"unit": "Wh"}', -1, 0, 0),
(-105, NOW() - INTERVAL '3 days', 7.50, 'Sample_Clock', 'Energy_Active_Import_Register', NULL, 'Outlet', NULL, '{"unit": "Wh"}', -1, 0, 0),
(-106, NOW() - INTERVAL '2 days', 10.25, 'Sample_Clock', 'Energy_Active_Import_Register', NULL, 'Outlet', NULL, '{"unit": "Wh"}', -1, 0, 0),
(-107, NOW() - INTERVAL '1 day', 13.75, 'Sample_Clock', 'Energy_Active_Import_Register', NULL, 'Outlet', NULL, '{"unit": "Wh"}', -1, 0, 0),
(-108, NOW(), 17.50, 'Sample_Clock', 'Energy_Active_Import_Register', NULL, 'Outlet', NULL, '{"unit": "Wh"}', -1, 0, 0);

-- Energy_Active_Import_Register values (kWh) for charging station -1, evse 1
INSERT INTO meter_value (id, timestamp, value, context, measurand, phase, location, signed_meter_value, unit_of_measure, charging_station_id, charging_station_evse_id, version) VALUES
(-109, NOW() - INTERVAL '7 days', 0.75, 'Sample_Clock', 'Energy_Active_Import_Register', NULL, 'Outlet', NULL, '{"unit": "Wh"}', -1, 1, 0),
(-110, NOW() - INTERVAL '6 days', 2.25, 'Sample_Clock', 'Energy_Active_Import_Register', NULL, 'Outlet', NULL, '{"unit": "Wh"}', -1, 1, 0),
(-111, NOW() - INTERVAL '5 days', 4.00, 'Sample_Clock', 'Energy_Active_Import_Register', NULL, 'Outlet', NULL, '{"unit": "Wh"}', -1, 1, 0),
(-112, NOW() - INTERVAL '4 days', 6.50, 'Sample_Clock', 'Energy_Active_Import_Register', NULL, 'Outlet', NULL, '{"unit": "Wh"}', -1, 1, 0),
(-113, NOW() - INTERVAL '3 days', 9.25, 'Sample_Clock', 'Energy_Active_Import_Register', NULL, 'Outlet', NULL, '{"unit": "Wh"}', -1, 1, 0),
(-114, NOW() - INTERVAL '2 days', 12.75, 'Sample_Clock', 'Energy_Active_Import_Register', NULL, 'Outlet', NULL, '{"unit": "Wh"}', -1, 1, 0),
(-115, NOW() - INTERVAL '1 day', 16.50, 'Sample_Clock', 'Energy_Active_Import_Register', NULL, 'Outlet', NULL, '{"unit": "Wh"}', -1, 1, 0),
(-116, NOW(), 21.00, 'Sample_Clock', 'Energy_Active_Import_Register', NULL, 'Outlet', NULL, '{"unit": "Wh"}', -1, 1, 0);

-- Power_Active_Import values (W) for charging station -1, evse 0
INSERT INTO meter_value (id, timestamp, value, context, measurand, phase, location, signed_meter_value, unit_of_measure, charging_station_id, charging_station_evse_id, version) VALUES
(-117, NOW() - INTERVAL '7 days', 1000.00, 'Sample_Clock', 'Power_Active_Import', NULL, 'Outlet', NULL, '{"unit": "W"}', -1, 0, 0),
(-118, NOW() - INTERVAL '6 days', 1200.00, 'Sample_Clock', 'Power_Active_Import', NULL, 'Outlet', NULL, '{"unit": "W"}', -1, 0, 0),
(-119, NOW() - INTERVAL '5 days', 1500.00, 'Sample_Clock', 'Power_Active_Import', NULL, 'Outlet', NULL, '{"unit": "W"}', -1, 0, 0),
(-120, NOW() - INTERVAL '4 days', 1800.00, 'Sample_Clock', 'Power_Active_Import', NULL, 'Outlet', NULL, '{"unit": "W"}', -1, 0, 0),
(-121, NOW() - INTERVAL '3 days', 2100.00, 'Sample_Clock', 'Power_Active_Import', NULL, 'Outlet', NULL, '{"unit": "W"}', -1, 0, 0),
(-122, NOW() - INTERVAL '2 days', 2400.00, 'Sample_Clock', 'Power_Active_Import', NULL, 'Outlet', NULL, '{"unit": "W"}', -1, 0, 0),
(-123, NOW() - INTERVAL '1 day', 2700.00, 'Sample_Clock', 'Power_Active_Import', NULL, 'Outlet', NULL, '{"unit": "W"}', -1, 0, 0),
(-124, NOW(), 3000.00, 'Sample_Clock', 'Power_Active_Import', NULL, 'Outlet', NULL, '{"unit": "W"}', -1, 0, 0);

-- Voltage values (V) for charging station -1, evse 0
INSERT INTO meter_value (id, timestamp, value, context, measurand, phase, location, signed_meter_value, unit_of_measure, charging_station_id, charging_station_evse_id, version) VALUES
(-125, NOW() - INTERVAL '7 days', 220.00, 'Sample_Clock', 'Voltage', 'L1', 'Outlet', NULL, '{"unit": "V"}', -1, 0, 0),
(-126, NOW() - INTERVAL '6 days', 221.50, 'Sample_Clock', 'Voltage', 'L1', 'Outlet', NULL, '{"unit": "V"}', -1, 0, 0),
(-127, NOW() - INTERVAL '5 days', 219.75, 'Sample_Clock', 'Voltage', 'L1', 'Outlet', NULL, '{"unit": "V"}', -1, 0, 0),
(-128, NOW() - INTERVAL '4 days', 222.25, 'Sample_Clock', 'Voltage', 'L1', 'Outlet', NULL, '{"unit": "V"}', -1, 0, 0),
(-129, NOW() - INTERVAL '3 days', 220.50, 'Sample_Clock', 'Voltage', 'L1', 'Outlet', NULL, '{"unit": "V"}', -1, 0, 0),
(-130, NOW() - INTERVAL '2 days', 223.00, 'Sample_Clock', 'Voltage', 'L1', 'Outlet', NULL, '{"unit": "V"}', -1, 0, 0),
(-131, NOW() - INTERVAL '1 day', 221.25, 'Sample_Clock', 'Voltage', 'L1', 'Outlet', NULL, '{"unit": "V"}', -1, 0, 0),
(-132, NOW(), 222.50, 'Sample_Clock', 'Voltage', 'L1', 'Outlet', NULL, '{"unit": "V"}', -1, 0, 0);

-- Current_Import values (A) for charging station -1, evse 0
INSERT INTO meter_value (id, timestamp, value, context, measurand, phase, location, signed_meter_value, unit_of_measure, charging_station_id, charging_station_evse_id, version) VALUES
(-133, NOW() - INTERVAL '7 days', 5.00, 'Sample_Clock', 'Current_Import', 'L1', 'Outlet', NULL, '{"unit": "A"}', -1, 0, 0),
(-134, NOW() - INTERVAL '6 days', 6.25, 'Sample_Clock', 'Current_Import', 'L1', 'Outlet', NULL, '{"unit": "A"}', -1, 0, 0),
(-135, NOW() - INTERVAL '5 days', 7.50, 'Sample_Clock', 'Current_Import', 'L1', 'Outlet', NULL, '{"unit": "A"}', -1, 0, 0),
(-136, NOW() - INTERVAL '4 days', 8.75, 'Sample_Clock', 'Current_Import', 'L1', 'Outlet', NULL, '{"unit": "A"}', -1, 0, 0),
(-137, NOW() - INTERVAL '3 days', 10.00, 'Sample_Clock', 'Current_Import', 'L1', 'Outlet', NULL, '{"unit": "A"}', -1, 0, 0),
(-138, NOW() - INTERVAL '2 days', 11.25, 'Sample_Clock', 'Current_Import', 'L1', 'Outlet', NULL, '{"unit": "A"}', -1, 0, 0),
(-139, NOW() - INTERVAL '1 day', 12.50, 'Sample_Clock', 'Current_Import', 'L1', 'Outlet', NULL, '{"unit": "A"}', -1, 0, 0),
(-140, NOW(), 13.75, 'Sample_Clock', 'Current_Import', 'L1', 'Outlet', NULL, '{"unit": "A"}', -1, 0, 0);

-- SoC values (%) for charging station -1, evse 0
INSERT INTO meter_value (id, timestamp, value, context, measurand, phase, location, signed_meter_value, unit_of_measure, charging_station_id, charging_station_evse_id, version) VALUES
(-141, NOW() - INTERVAL '7 days', 10.00, 'Sample_Clock', 'SoC', NULL, 'EV', NULL, '{"unit": "Percent"}', -1, 0, 0),
(-142, NOW() - INTERVAL '6 days', 22.50, 'Sample_Clock', 'SoC', NULL, 'EV', NULL, '{"unit": "Percent"}', -1, 0, 0),
(-143, NOW() - INTERVAL '5 days', 35.00, 'Sample_Clock', 'SoC', NULL, 'EV', NULL, '{"unit": "Percent"}', -1, 0, 0),
(-144, NOW() - INTERVAL '4 days', 47.50, 'Sample_Clock', 'SoC', NULL, 'EV', NULL, '{"unit": "Percent"}', -1, 0, 0),
(-145, NOW() - INTERVAL '3 days', 60.00, 'Sample_Clock', 'SoC', NULL, 'EV', NULL, '{"unit": "Percent"}', -1, 0, 0),
(-146, NOW() - INTERVAL '2 days', 72.50, 'Sample_Clock', 'SoC', NULL, 'EV', NULL, '{"unit": "Percent"}', -1, 0, 0),
(-147, NOW() - INTERVAL '1 day', 85.00, 'Sample_Clock', 'SoC', NULL, 'EV', NULL, '{"unit": "Percent"}', -1, 0, 0),
(-148, NOW(), 97.50, 'Sample_Clock', 'SoC', NULL, 'EV', NULL, '{"unit": "Percent"}', -1, 0, 0);

-- Energy_Active_Import_Register values (kWh) for charging station -2, evse 0
INSERT INTO meter_value (id, timestamp, value, context, measurand, phase, location, signed_meter_value, unit_of_measure, charging_station_id, charging_station_evse_id, version) VALUES
(-149, NOW() - INTERVAL '7 days', 0.25, 'Sample_Clock', 'Energy_Active_Import_Register', NULL, 'Outlet', NULL, '{"unit": "Wh"}', -2, 0, 0),
(-150, NOW() - INTERVAL '6 days', 1.50, 'Sample_Clock', 'Energy_Active_Import_Register', NULL, 'Outlet', NULL, '{"unit": "Wh"}', -2, 0, 0),
(-151, NOW() - INTERVAL '5 days', 3.00, 'Sample_Clock', 'Energy_Active_Import_Register', NULL, 'Outlet', NULL, '{"unit": "Wh"}', -2, 0, 0),
(-152, NOW() - INTERVAL '4 days', 4.75, 'Sample_Clock', 'Energy_Active_Import_Register', NULL, 'Outlet', NULL, '{"unit": "Wh"}', -2, 0, 0),
(-153, NOW() - INTERVAL '3 days', 7.00, 'Sample_Clock', 'Energy_Active_Import_Register', NULL, 'Outlet', NULL, '{"unit": "Wh"}', -2, 0, 0),
(-154, NOW() - INTERVAL '2 days', 9.50, 'Sample_Clock', 'Energy_Active_Import_Register', NULL, 'Outlet', NULL, '{"unit": "Wh"}', -2, 0, 0),
(-155, NOW() - INTERVAL '1 day', 12.25, 'Sample_Clock', 'Energy_Active_Import_Register', NULL, 'Outlet', NULL, '{"unit": "Wh"}', -2, 0, 0),
(-156, NOW(), 15.75, 'Sample_Clock', 'Energy_Active_Import_Register', NULL, 'Outlet', NULL, '{"unit": "Wh"}', -2, 0, 0);

-- Session owner table inserts
insert into session_owner(id, name, version) values
(-1, 'testOcpiUser', 0);

-- Session table inserts
insert into session(id, session_owner_id, start_date_time, end_date_time, evse_uid, connector_id, charging_station_session_status, status, last_updated, version) values
(-1, -1, '2025-04-01 08:00:00+00', '2025-04-01 10:30:00+00', 'evse-001', 'connector-001', 'INITIAL', 'COMPLETED', '2025-04-01 10:30:00+00', 0),
(-2, -1, '2025-04-01 11:00:00+00', '2025-04-01 12:45:00+00', 'evse-002', 'connector-002', 'INITIAL', 'COMPLETED', '2025-04-01 12:45:00+00', 0),
(-3, -1, '2025-04-02 09:15:00+00', '2025-04-02 11:20:00+00', 'evse-001', 'connector-001', 'INITIAL', 'COMPLETED', '2025-04-02 11:20:00+00', 0),
(-4, -1, '2025-04-02 14:30:00+00', '2025-04-02 16:00:00+00', 'evse-002', 'connector-002', 'INITIAL', 'COMPLETED', '2025-04-02 16:00:00+00', 0),
(-5, -1, '2025-04-03 10:00:00+00', '2025-04-03 11:45:00+00', 'evse-001', 'connector-001', 'INITIAL', 'COMPLETED', '2025-04-03 11:45:00+00', 0),
(-6, -1, '2025-04-03 13:30:00+00', '2025-04-03 15:15:00+00', 'evse-002', 'connector-002', 'INITIAL', 'COMPLETED', '2025-04-03 15:15:00+00', 0),
(-7, -1, '2025-04-04 08:45:00+00', '2025-04-04 10:30:00+00', 'evse-001', 'connector-001', 'INITIAL', 'COMPLETED', '2025-04-04 10:30:00+00', 0),
(-8, -1, '2025-04-04 12:00:00+00', null, 'evse-002', 'connector-002', 'INITIAL', 'ACTIVE', '2025-04-04 12:00:00+00', 0),
(-9, -1, '2025-04-05 09:30:00+00', null, 'evse-001', 'connector-001', 'INITIAL', 'ACTIVE', '2025-04-05 09:30:00+00', 0),
(-10, -1, '2025-04-06 00:00:00+00', null, 'evse-002', 'connector-002', 'INITIAL', 'PENDING', '2025-04-06 00:00:00+00', 0);