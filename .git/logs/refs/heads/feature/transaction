0000000000000000000000000000000000000000 402976077cc6b94551241e597fbc4374a76ddf9d malloc <a@a.a> 1744538979 +0200	branch: Created from HEAD
402976077cc6b94551241e597fbc4374a76ddf9d e594d47b6a8cb6a7ac9822ad9250a9a8828105b4 malloc <a@a.a> 1744638264 +0200	commit: feat: enhance remote start transaction
e594d47b6a8cb6a7ac9822ad9250a9a8828105b4 66acd1caeb7d98d1bca80de23b787cfe9c9c2223 malloc <a@a.a> 1744643928 +0200	commit: feat: fix remote start transaction
66acd1caeb7d98d1bca80de23b787cfe9c9c2223 963c1388fec47221466a8275ce87b0e22061dd9c malloc <a@a.a> 1744732837 +0200	commit: feat: status and idTag added to EvseTransaction
963c1388fec47221466a8275ce87b0e22061dd9c f1016dbccd76e1d42a17b2ad29ab7c928dab6a85 malloc <a@a.a> 1744810115 +0200	commit: feat: stop transaction by idTag
