0000000000000000000000000000000000000000 a42486c09d10eed008c58ac2fe9890d31e105edf malloc <a@a.a> 1724905454 +0200	branch: Created from main^0
a42486c09d10eed008c58ac2fe9890d31e105edf 3c890653a109ab977fd80481cfbb2c119de546fa malloc <a@a.a> 1724962724 +0200	commit: feat: CS register mutation, password generation
3c890653a109ab977fd80481cfbb2c119de546fa 14a587be6354bba3ebd1dc4aebe7c06061ed8922 malloc <a@a.a> 1725479775 +0200	commit: feat: failing create user
14a587be6354bba3ebd1dc4aebe7c06061ed8922 7117c0a75735d932273424564fd71461e131cdc3 malloc <a@a.a> 1725711687 +0200	commit (merge): Merge remote-tracking branch 'refs/remotes/origin/main' into feature/create_charging_station
7117c0a75735d932273424564fd71461e131cdc3 f66607cd25fdfadd4ba6c42f03310024c02e7b4e malloc <a@a.a> 1725718070 +0200	commit: feat: update user
f66607cd25fdfadd4ba6c42f03310024c02e7b4e 0da599810910d5ffaa5b5bd29d1c4cd0dae8092d malloc <a@a.a> 1725718245 +0200	commit (merge): Merge remote-tracking branch 'origin/feature/create_charging_station' into feature/create_charging_station
0da599810910d5ffaa5b5bd29d1c4cd0dae8092d 7d5c4b28ed952d43654f14a7dcc09e29d08cdfe2 malloc <a@a.a> 1725718423 +0200	commit (merge): Merge remote-tracking branch 'refs/remotes/origin/main' into feature/create_charging_station
7d5c4b28ed952d43654f14a7dcc09e29d08cdfe2 393ff794e9ddfa6c29197f527737ccec4f9d880b malloc <a@a.a> 1725741094 +0200	commit: feat: charging station id + creation
393ff794e9ddfa6c29197f527737ccec4f9d880b aff94d51c9810802b95714f1139225ca5aa69965 malloc <a@a.a> 1725804465 +0200	commit: feat: charging station id + creation
