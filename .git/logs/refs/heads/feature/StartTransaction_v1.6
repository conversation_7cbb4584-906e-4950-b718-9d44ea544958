0000000000000000000000000000000000000000 690560a4f1dedb6aa861df4d7fe28f839031b4bf malloc <a@a.a> 1737982704 +0100	branch: Created from main^0
690560a4f1dedb6aa861df4d7fe28f839031b4bf 5b0e2552714e04a86fb101f62532ede12f8c78f5 malloc <a@a.a> 1738170107 +0100	commit: feat: start transaction ocpp1.6
5b0e2552714e04a86fb101f62532ede12f8c78f5 4c437e1e05efb46c18f45da13848494f4b81ca74 malloc <a@a.a> 1738185894 +0100	commit: feat: start transaction ocpp1.6
4c437e1e05efb46c18f45da13848494f4b81ca74 19b0b0e33b467870db062c0c9f20ea98287745e3 malloc <a@a.a> 1738331453 +0100	commit: feat: stop transaction ocpp1.6
19b0b0e33b467870db062c0c9f20ea98287745e3 9c0f02adc53f17ee180cc839375f3cab97b3386f malloc <a@a.a> 1738676577 +0100	commit: fix: review
9c0f02adc53f17ee180cc839375f3cab97b3386f ec631109cbbb17e3305759a523ca2ab366d4dc0d malloc <a@a.a> 1738691261 +0100	commit: fix: no evse id in transaction message
ec631109cbbb17e3305759a523ca2ab366d4dc0d 6f9ba867cf542ae1cde222fb18be9a63c4f4ad74 malloc <a@a.a> 1738767354 +0100	commit: fix: transactionId for ocpp1.6 from database sequence
