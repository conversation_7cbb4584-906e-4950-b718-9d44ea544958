meta {
  name: Location
  type: graphql
  seq: 7
}

post {
  url: {{graphql_url}}
  body: graphql
  auth: none
}

body:graphql {
  query Location($id: ID!) {
    location(id: $id) {
      id
      name
      floors {
        id
        level
        name
        layout
        rowCount
        columnCount
      }
      powerGrids {
      	id
      	name
      }
    }
  }
  
}

body:graphql:vars {
  {
    "id": "-1"
  }
}
