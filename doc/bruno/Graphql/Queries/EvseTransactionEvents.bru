meta {
  name: EvseTransactionEvents
  type: graphql
  seq: 20
}

post {
  url: {{graphql_url}}
  body: graphql
  auth: none
}

body:graphql {
  query EvseTransactionEvents($evseTransactionId: ID!) {
    evseTransactionEvents(evseTransactionId: $evseTransactionId) {
      id
      eventType
      timestamp
  		triggerReason
      eventSeqNo
      offline
      numberOfPhasesUsed
  		cableMaxCurrent
      reservationId
      chargingState
      timeSpentCharging
  		stoppedReason
      remoteStartId
      idToken {
      	idToken
        type
  		  additionalIdTokens {
        	idToken
          type
        }
      }
      meterValues {
      	timestamp
        sampledValues {
        	value
          context
          measurand
  				phase
          location
          signedMeterData
          signingMethod
          encodingMethod
  				publicKey
          unit
          unitMultiplier
        }
      }
    }
  }
  
}

body:graphql:vars {
  {
    "evseTransactionId": "-1"
  }
}