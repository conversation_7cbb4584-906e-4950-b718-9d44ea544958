meta {
  name: Connectors
  type: graphql
  seq: 22
}

post {
  url: {{graphql_url}}
  body: graphql
  auth: none
}

body:graphql {
  query Connectors($evseId: ID!) {
    connectors(evseId: $evseId) {
      id
      evseConnectorId
      status
      availabilityStatus
      lastStatusUpdate
      errorCode
      errorInfo
      vendorImplementationId
  		vendorErrorCode
    }
  }
  
}

body:graphql:vars {
  {
    "evseId": "-1"
  }
}
