meta {
  name: Diagnostics
  type: graphql
  seq: 28
}

post {
  url: {{graphql_url}}
  body: graphql
  auth: none
}

body:graphql {
  query Diagnostics(
    $chargingStationId: ID!
    $location: String!
    $retries: Int
    $retryInterval: Int
    $startTime: DateTime 
    $stopTime: DateTime 
  ) {
    diagnostics(
      chargingStationId: $chargingStationId
      location: $location
      retries: $retries
      retryInterval: $retryInterval
      startTime: $startTime
      stopTime: $stopTime
    ) {
      fileName
    }
  }
  
}

body:graphql:vars {
  {
    "chargingStationId": "-1",
    "location": "ftp://admin:admin@*************",
    "startTime": "2025-03-18T09:49:30.9012261+01:00",
    "stopTime": "2025-03-21T09:49:30.9012261+01:00"
  }
}

script:pre-request {
  const cookie = bru.getVar("cookie");
  
  if(cookie) {
    req.setHeader("<PERSON><PERSON>", cookie)
  }
}
