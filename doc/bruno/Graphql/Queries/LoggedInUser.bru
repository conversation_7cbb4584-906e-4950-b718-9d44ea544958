meta {
  name: LoggedInUser
  type: graphql
  seq: 26
}

post {
  url: {{graphql_url}}
  body: graphql
  auth: none
}

body:graphql {
  query LoggedInUser {
    loggedInUser {
      user {
        id
        username
      }
      userTokens {
      	accessToken
        accessTokenExpiresAt, 
        refreshToken
        refreshTokenExpiresAt
      }
    }
  }
}

script:pre-request {
  const cookie = bru.getVar("cookie");
  
  if(cookie) {
    req.setHeader("Cookie", cookie)
  }
}
