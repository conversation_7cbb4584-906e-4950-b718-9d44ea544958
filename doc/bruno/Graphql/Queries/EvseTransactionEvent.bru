meta {
  name: EvseTransactionEvent
  type: graphql
  seq: 19
}

post {
  url: {{graphql_url}}
  body: graphql
  auth: none
}

body:graphql {
  query EvseTransactionEvent($id: ID!) {
    evseTransactionEvent(id: $id) {
      id
      eventType
      timestamp
  		triggerReason
      eventSeqNo
      offline
      numberOfPhasesUsed
  		cableMaxCurrent
      reservationId
      chargingState
      timeSpentCharging
  		stoppedReason
      remoteStartId
      idToken {
      	idToken
        type
  		  additionalIdTokens {
        	idToken
          type
        }
      }
      meterValues {
      	timestamp
        sampledValues {
        	value
          context
          measurand
  				phase
          location
          signedMeterData
          signingMethod
          encodingMethod
  				publicKey
          unit
          unitMultiplier
        }
      }
    }
  }
}

body:graphql:vars {
  {
    "id": "-1"
  }
}