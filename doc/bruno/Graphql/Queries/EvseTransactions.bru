meta {
  name: EvseTransactions
  type: graphql
  seq: 18
}

post {
  url: {{graphql_url}}
  body: graphql
  auth: none
}

body:graphql {
  query EvseTransactions($evseId: ID!) {
    evseTransactions(evseId: $evseId) {
      id
      evseTransactionId
      connector {
        id
        evseConnectorId
        status
        availabilityStatus
      }
      evseTransactionEvents {
        id
        eventType
        timestamp
        triggerReason
        eventSeqNo
        offline
        numberOfPhasesUsed
        cableMaxCurrent
        reservationId
        chargingState
        timeSpentCharging
        stoppedReason
        remoteStartId
        idToken {
          idToken
          type
          additionalIdTokens {
            idToken
            type
          }
        }
        meterValues {
          timestamp
          sampledValues {
            value
            context
            measurand
            phase
            location
            signedMeterData
            signingMethod
            encodingMethod
            publicKey
            unit
            unitMultiplier
          }
        }
      }
    }
  }
  
}

body:graphql:vars {
  {
    "evseId": "-1"
  }
}