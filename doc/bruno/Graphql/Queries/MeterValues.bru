meta {
  name: MeterValues
  type: graphql
  seq: 29
}

post {
  url: {{graphql_url}}
  body: graphql
  auth: none
}

body:graphql {
  query MeterValues($chargingStationId: ID!, $evseChargingStationId: Int) {
    meterValues(chargingStationId: $chargingStationId, evseChargingStationId: $evseChargingStationId) {
      timestamp
      sampledValues {
        value
        measurand
        unit
      }
    }
  }
}

body:graphql:vars {
  {
    "chargingStationId": -1,
    "evseChargingStationId": 1
  }
}
