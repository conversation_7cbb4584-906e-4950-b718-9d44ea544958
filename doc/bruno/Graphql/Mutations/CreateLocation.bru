meta {
  name: CreateLocation
  type: graphql
  seq: 5
}

post {
  url: {{graphql_url}}
  body: graphql
  auth: none
}

body:graphql {
  mutation CreateLocation($name: String!) {
    createLocation(name: $name) {
      id
      name
    }
  }
  
}

body:graphql:vars {
  {
    "name": "newLocation"
  }
}

script:pre-request {
  const cookie = bru.getVar("cookie");
  
  if(cookie) {
    req.setHeader("Cookie", cookie)
  }
}
