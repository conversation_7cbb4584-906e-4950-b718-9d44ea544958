meta {
  name: CreateTenant
  type: graphql
  seq: 1
}

post {
  url: {{graphql_url}}
  body: graphql
  auth: none
}

body:graphql {
  mutation CreateTenant($name: String!) {
    createTenant(name: $name) {
      id
      name
    }
  }
  
}

body:graphql:vars {
  {
    "name": "newTenant"
  }
}

script:pre-request {
  const cookie = bru.getVar("cookie");
  
  if(cookie) {
    req.setHeader("Cookie", cookie)
  }
}
