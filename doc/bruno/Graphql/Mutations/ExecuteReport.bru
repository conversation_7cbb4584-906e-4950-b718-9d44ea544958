meta {
  name: ExecuteReport
  type: graphql
  seq: 9
}

post {
  url: {{graphql_url}}
  body: graphql
  auth: none
}

body:graphql {
  mutation ExecuteReport($chargingStationId: ID!, $reportType: ReportType!) {
    executeReport(
      chargingStationId: $chargingStationId
      reportType: $reportType
    ) {
      id
      requestId
      responseAdditionalInfo
      responseStatus
      type
      responseReasonCode
      status
    }
  }
  
}

body:graphql:vars {
  {
    "chargingStationId": "-1",
    "reportType": "BaseReport_FullInventory"
  }
}

script:pre-request {
  const cookie = bru.getVar("cookie");
  
  if(cookie) {
    req.setHeader("Cookie", cookie)
  }
}
