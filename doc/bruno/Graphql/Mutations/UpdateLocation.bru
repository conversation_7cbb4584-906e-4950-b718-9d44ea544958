meta {
  name: UpdateLocation
  type: graphql
  seq: 6
}

post {
  url: {{graphql_url}}
  body: graphql
  auth: none
}

body:graphql {
  mutation UpdateLocation($id: ID!, $name: String!) {
    updateLocation(id: $id, name: $name) {
      id
      name
    }
  }
  
}

body:graphql:vars {
  {
    "id": -1,
    "name": "updatedLocation"
  }
}

script:pre-request {
  const cookie = bru.getVar("cookie");
  
  if(cookie) {
    req.setHeader("Cookie", cookie)
  }
}
