meta {
  name: UpdateTenant
  type: graphql
  seq: 2
}

post {
  url: {{graphql_url}}
  body: graphql
  auth: none
}

body:graphql {
  mutation UpdateTenant($id: ID, $name: String!) {
    updateTenant(id: $id, name: $name) {
      id
      name
    }
  }
  
}

body:graphql:vars {
  {
    "id": 1,
    "name": "updatedTenant"
  }
}

script:pre-request {
  const cookie = bru.getVar("cookie");
  
  if(cookie) {
    req.setHeader("Cookie", cookie)
  }
}
