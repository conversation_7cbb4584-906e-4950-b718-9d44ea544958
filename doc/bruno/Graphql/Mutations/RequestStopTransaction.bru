meta {
  name: RequestStopTransaction
  type: graphql
  seq: 20
}

post {
  url: {{graphql_url}}
  body: graphql
  auth: none
}

body:graphql {
  mutation RequestStopTransaction($id: ID!, 
    		$transactionId: BigInteger!) {
    requestStopTransaction(
      id: $id
      transactionId: $transactionId
    ) {
      status
    }
  }
  
}

body:graphql:vars {
  {
    "id": "-1",
    "transactionId": 1
  }
}

script:pre-request {
  const cookie = bru.getVar("cookie");
  
  if(cookie) {
    req.setHeader("Cookie", cookie)
  }
}
