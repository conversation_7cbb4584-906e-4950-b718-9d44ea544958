meta {
  name: CreateFloor
  type: graphql
  seq: 7
}

post {
  url: {{graphql_url}}
  body: graphql
  auth: none
}

body:graphql {
  mutation CreateFloor($locationId: ID!, $name: String!) {
    createFloor(locationId: $locationId, name: $name) {
      id
      level
      name
      layout
      rowCount
      columnCount
    }
  }
  
}

body:graphql:vars {
  {
    "locationId": "-1",
    "name": "newFloor"
  }
}

script:pre-request {
  const cookie = bru.getVar("cookie");
  
  if(cookie) {
    req.setHeader("Cookie", cookie)
  }
}
