meta {
  name: RequestStartTransaction
  type: graphql
  seq: 19
}

post {
  url: {{graphql_url}}
  body: graphql
  auth: none
}

body:graphql {
  mutation RequestStartTransaction($id: ID!, 
    		$evseId: Int) {
    requestStartTransaction(
      id: $id
      evseId: $evseId
    ) {
      status
    }
  }
  
}

body:graphql:vars {
  {
    "id": "-1",
    "evseId": 1
  }
}

script:pre-request {
  const cookie = bru.getVar("cookie");
  
  if(cookie) {
    req.setHeader("Cookie", cookie)
  }
}
