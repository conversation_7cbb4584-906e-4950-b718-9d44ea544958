meta {
  name: CreatePowerGrid
  type: graphql
  seq: 8
}

post {
  url: {{graphql_url}}
  body: graphql
  auth: none
}

body:graphql {
  mutation CreatePowerGrid($locationId: ID!, $name: String!) {
    createPowerGrid(locationId: $locationId, name: $name) {
      id
      name
    }
  }
  
}

body:graphql:vars {
  {
    "locationId": "-1",
    "name": "newPowerGrid"
  }
}

script:pre-request {
  const cookie = bru.getVar("cookie");
  
  if(cookie) {
    req.setHeader("Cookie", cookie)
  }
}
