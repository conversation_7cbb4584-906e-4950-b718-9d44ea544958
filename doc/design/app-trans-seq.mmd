   sequenceDiagram
    participant App as Third party app
    participant CSMS as Central System (CSMS)
    participant CS as Charging Station (CS)
    
     Note right of CS: Prerequisite: AuthorizeRemoteTxRequests(1.6) / AuthorizeRemoteStart(2.0.1) == false
    
	App ->> CSMS: createChargingSession(chargingStationId, connectorId)
	activate CSMS
	CSMS ->> CSMS: generateAnSaveIdTag
	CSMS ->> CS: remoteStartTransactionReq(idTag)
	CS ->> CSMS: remoteStartTransactionRes
    CSMS-->>App: sessionId
    deactivate CSMS
    
    
    CS ->> CSMS: startTransactionReq(idTag)
    activate CSMS
    CSMS ->> CSMS: findSession
    CSMS ->> CSMS: createTransaction
    CSMS ->> CSMS: connectTransactiionToSession
    CSMS ->> CS: startTransactionRes()
	deactivate CSMS
	
	
	CS ->> CSMS: meterValuesReq()
	activate CSMS
	CSMS ->> CSMS: createEvseTransactionEventEntity
	CSMS ->> CS: meterValuesRes()
	deactivate CSMS
	
	
	App ->> CSMS: closeChargingSession(sessionId)
	activate CSMS
	CSMS ->> CS: remoteStopTransactionReq(transactionId)
	CS ->> CSMS: remoteStopTransactionRes
    CSMS-->>App: session
    deactivate CSMS
    
