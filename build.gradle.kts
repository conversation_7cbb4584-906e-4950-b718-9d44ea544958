plugins {
    java
    id("io.quarkus")
}

repositories {
    mavenCentral()
    mavenLocal()
}

val quarkusPlatformGroupId: String by project
val quarkusPlatformArtifactId: String by project
val quarkusPlatformVersion: String by project

dependencies {
    // OIDC
	implementation("io.quarkus:quarkus-oidc-client")
	
    // Hibernate Reactive dependency
    // Reactive SQL client for PostgreSQL
    implementation("io.quarkus:quarkus-reactive-pg-client")
    // implementation("io.quarkus:quarkus-hibernate-reactive")
    implementation("io.quarkus:quarkus-hibernate-reactive-panache")

    // JPA Metamodel Generator
    annotationProcessor("org.hibernate:hibernate-jpamodelgen:6.4.4.Final")

    // Flyway specific dependencies
    implementation("io.quarkus:quarkus-flyway")
    // JDBC driver dependencies
    implementation("io.quarkus:quarkus-jdbc-postgresql")
    
    // websocket reactive
    implementation("io.quarkus:quarkus-websockets-next")
    
    // Graphql
    implementation("io.quarkus:quarkus-smallrye-graphql")

    // Rest
    implementation("io.quarkus:quarkus-smallrye-openapi")
    implementation("io.quarkus:quarkus-rest")
    implementation("io.quarkus:quarkus-rest-jackson")
	implementation("io.quarkus:quarkus-rest-client-jackson")
    
	// validation
	implementation("io.quarkus:quarkus-hibernate-validator")
	
    // quarkus
    implementation(enforcedPlatform("${quarkusPlatformGroupId}:${quarkusPlatformArtifactId}:${quarkusPlatformVersion}"))
    implementation("io.quarkus:quarkus-arc")
	
	// Jakarta Persistence security
	implementation("io.quarkus:quarkus-security-jpa-reactive")

    // Common
    implementation("org.apache.commons:commons-text")
    
	// Test
    testImplementation("io.quarkus:quarkus-junit5")
    testImplementation("io.rest-assured:rest-assured")
    testImplementation("org.junit.jupiter:junit-jupiter-engine")
    testImplementation("io.quarkus:quarkus-websockets")
    testImplementation("com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.17.1")
    testImplementation("io.quarkus:quarkus-test-vertx:2.9.1.Final")
    testImplementation("io.quarkus:quarkus-test-hibernate-reactive-panache:3.3.3")
}

group = "org.galiasystems"
version = "1.0.0-SNAPSHOT"

java {
    sourceCompatibility = JavaVersion.VERSION_21
    targetCompatibility = JavaVersion.VERSION_21
}

tasks.withType<Test> {
    systemProperty("java.util.logging.manager", "org.jboss.logmanager.LogManager")
    useJUnitPlatform()
    testLogging {
        showStandardStreams = true
    }
}
tasks.withType<JavaCompile> {
    options.encoding = "UTF-8"
    options.compilerArgs.add("-parameters")

    // Enable annotation processing for JPA metamodel generation
    options.annotationProcessorPath = configurations["annotationProcessor"]

    // Set the output directory for generated sources
    options.generatedSourceOutputDirectory.set(file("${project.buildDir}/generated/sources/annotationProcessor/java/main"))
}

// Make the generated sources directory available to the IDE
sourceSets {
    main {
        java {
            srcDir("${project.buildDir}/generated/sources/annotationProcessor/java/main")
        }
    }
}

// Add a specific task to generate JPA metamodel classes
tasks.register("generateJpaMetamodel") {
    group = "build"
    description = "Generate JPA metamodel classes"

    // This task depends on the compileJava task
    dependsOn("compileJava")

    doLast {
        println("JPA metamodel classes generated in: ${project.buildDir}/generated/sources/annotationProcessor/java/main")
    }
}